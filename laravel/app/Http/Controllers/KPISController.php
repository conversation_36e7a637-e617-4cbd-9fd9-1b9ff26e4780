<?php

namespace App\Http\Controllers;

use App\Exceptions\CrmException;
use App\Http\Requests\KpiRatioRequest;
use App\Line;
use App\Models\Kpi;
use App\Models\KpiPercent;
use App\Models\KpiRatio;
use App\Role;
use Illuminate\Http\Request;

class KPISController extends ApiController
{

    public function show(Kpi $kpi)
    {
        $lines = Line::select(['id', 'name'])->with('positions', 'divisionTypes')->get()->map(fn($line) => [
            'id' => $line->id,
            'name' => $line->name,
            'roles' => collect([...$line->divisionTypes, ...$line->positions])
                ->map(fn($role) => ['id' => $role->id . '_' . get_class($role), 'name' => $role->name])
        ]);
        $kpi->load('ratios');

        $kpi = ['id' => $kpi->id, 'name' => $kpi->name, 'ratios' => $kpi->ratios->map(fn($ratio) => [
            'id' => $ratio->id,
            'line_id' => $ratio->line_id,
            'role_id' => $ratio->roleable_id . '_' . $ratio->roleable_type,
            'minimum' => $ratio->minimum,
            'ratio' => $ratio->ratio,
        ])];

        return $this->respond(['lines' => $lines, 'kpi' => $kpi]);
    }

    public function getKPIs()
    {
        return $this->respond(Kpi::select(['id', 'name', 'sort'])->orderBy('sort', 'Asc')->get());
    }

    public function update(KpiRatioRequest $request, KpiRatio $kpiRatio)
    {
        $kpiRatio->update([
            'line_id' => $request->line_id ?? $kpiRatio->line_id,
            'roleable_id' => $request->roleable_id ?? $kpiRatio->roleable_id,
            'roleable_type' => $request->roleable_type ?? $kpiRatio->roleable_type,
            'ratio' => $request->ratio ?? $kpiRatio->ratio,
            'minimum' => $request->minimum ?? $kpiRatio->minimum,
        ]);

        return $this->respond();
    }

    public function saveKpiRatioPercents(Request $request)
    {
        $kpiRatio = $request->kpiRatio;
        KpiPercent::create([
            'kpi_ratio_id' => $kpiRatio['id'],
            'from_percent' => $request->from,
            'to_percent' => $request->to,
            'value' => $request->value
        ]);
        return $this->respondSuccess();
    }

    public function store(KpiRatioRequest $request, Kpi $kpi)
    {
        $data = fn($line_id) => [
            'line_id' => $line_id,
            'roleable_id' => $request->roleable_id,
            'roleable_type' => $request->roleable_type,
            'ratio' => $request->ratio,
            'minimum' => $request->minimum,
        ];

        $kpi->ratios()->create($data($request->line_id));

        foreach ($request->lines_to_copy as $key => $lineId) {
            $kpi->ratios()->create($data($lineId));
        }

        return $this->respond();
    }

    public function destroy(KpiRatio $kpiRatio)
    {
        $kpiRatio->delete();

        return $this->respond();
    }

    public function getPercents(Request $request)
    {
        return $this->respond(KpiPercent::select('id','from_percent','to_percent','value')->where('kpi_ratio_id', $request->id)->get());
    }
}
