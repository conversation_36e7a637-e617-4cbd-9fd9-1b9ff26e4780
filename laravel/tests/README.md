# Testing Guidelines

## Test Performance Improvements

We've made several changes to improve test performance:

1. **Separate Database Configurations**:
   - Unit tests now use SQLite in-memory database (fast)
   - Feature tests still use MySQL (slower but more realistic)

2. **Removed RefreshDatabase from Unit Tests**:
   - Unit tests no longer run migrations for each test
   - This dramatically improves performance

## Writing Fast Tests

### Unit Tests (Fast)

Unit tests should:
- Test isolated components without database dependencies
- Use mocks for external dependencies
- Be placed in the `tests/Unit` directory

Example:
```php
<?php
namespace Tests\Unit;

it('can perform calculations', function () {
    expect(1 + 1)->toBe(2);
});

it('can mock dependencies', function () {
    $mock = mock('App\Services\SomeService')
        ->shouldReceive('doSomething')
        ->andReturn('mocked result')
        ->getMock();
    
    expect($mock->doSomething())->toBe('mocked result');
});
```

### Feature Tests (Slower)

Feature tests should:
- Test full application features with database interactions
- Be placed in the `tests/Feature` directory
- Use the RefreshDatabase trait

Example:
```php
<?php
namespace Tests\Feature;

it('can create a user', function () {
    $response = $this->post('/users', [
        'name' => 'Test User',
        'email' => '<EMAIL>',
        'password' => 'password',
    ]);
    
    $response->assertStatus(201);
    $this->assertDatabaseHas('users', [
        'email' => '<EMAIL>',
    ]);
});
```

## Running Tests

### Run All Tests
```bash
./vendor/bin/sail artisan test
```

### Run Unit Tests Only (Fast)
```bash
./vendor/bin/sail artisan test --testsuite=Unit
```

### Run Feature Tests Only
```bash
./vendor/bin/sail artisan test --testsuite=Feature
```

### Run a Specific Test File
```bash
./vendor/bin/sail artisan test tests/Unit/ExampleTest.php
```

## Performance Tips

1. **Avoid RefreshDatabase When Possible**:
   - Use mocks and in-memory SQLite for unit tests
   - Only use RefreshDatabase for feature tests that need it

2. **Use DatabaseTransactions for Some Feature Tests**:
   - If you don't need to reset the database, use DatabaseTransactions instead of RefreshDatabase
   - This wraps each test in a transaction and rolls back after the test

3. **Consider Using Database Seeders**:
   - For tests that need specific data, use seeders instead of creating data in each test

4. **Use Laravel Sail**:
   - Always run tests using Laravel Sail to ensure consistent environment
