<?php

namespace Tests\Unit;

use App\Providers\DistributionStrategyServiceProvider;
use App\Services\Sales\Ceiling\Strategies\Distribution\{DistributionStrategyFactory,
    DistributionType,
    PrivatePharmacyStrategy,
    StoreStrategy,
    LocalChainStrategy};
use App\Services\Sales\Ceiling\Strategies\Distribution\Contracts\{
    TransactionManagerInterface,
    SettingsProviderInterface,
    LimitCalculatorInterface
};
use App\Services\Sales\Ceiling\Strategies\Distribution\Services\{
    TransactionManager,
    SalesSettingsProvider,
    LimitCalculator,
    SaleDetailFactory,
    SaleCreator
};
use App\Services\Sales\Ceiling\Strategies\Distribution\Algorithms\{HierarchicalChainDistributionAlgorithm,
    SimpleDistributionAlgorithm,
    SplitDistributionAlgorithm};
use Illuminate\Foundation\Application;
use Tests\TestCase;

/**
 * Test to verify that the service provider correctly registers all dependencies
 */
class ServiceProviderRegistrationTest extends TestCase
{
    private DistributionStrategyServiceProvider $serviceProvider;

    protected function setUp(): void
    {
        parent::setUp();

        // Manually register the service provider for testing
        $this->serviceProvider = new DistributionStrategyServiceProvider($this->app);
        $this->serviceProvider->register();
    }

    public function test_interfaces_are_bound_to_implementations()
    {
        // Test that interfaces are properly bound to their implementations
        $this->assertInstanceOf(
            TransactionManager::class,
            $this->app->make(TransactionManagerInterface::class)
        );

        $this->assertInstanceOf(
            SalesSettingsProvider::class,
            $this->app->make(SettingsProviderInterface::class)
        );

        $this->assertInstanceOf(
            LimitCalculator::class,
            $this->app->make(LimitCalculatorInterface::class)
        );
    }

    public function test_service_classes_are_registered_as_singletons()
    {
        // Test that service classes are registered as singletons
        $factory1 = $this->app->make(DistributionStrategyFactory::class);
        $factory2 = $this->app->make(DistributionStrategyFactory::class);
        $this->assertSame($factory1, $factory2);

        $saleDetailFactory1 = $this->app->make(SaleDetailFactory::class);
        $saleDetailFactory2 = $this->app->make(SaleDetailFactory::class);
        $this->assertSame($saleDetailFactory1, $saleDetailFactory2);

        $saleCreator1 = $this->app->make(SaleCreator::class);
        $saleCreator2 = $this->app->make(SaleCreator::class);
        $this->assertSame($saleCreator1, $saleCreator2);
    }

    public function test_distribution_algorithms_are_registered()
    {
        // Test that distribution algorithms can be resolved
        $simpleAlgorithm = $this->app->make(SimpleDistributionAlgorithm::class);
        $this->assertInstanceOf(SimpleDistributionAlgorithm::class, $simpleAlgorithm);

        $splitAlgorithm = $this->app->make(SplitDistributionAlgorithm::class);
        $this->assertInstanceOf(SplitDistributionAlgorithm::class, $splitAlgorithm);

        $splitAlgorithm = $this->app->make(HierarchicalChainDistributionAlgorithm::class);
        $this->assertInstanceOf(HierarchicalChainDistributionAlgorithm::class, $splitAlgorithm);
    }

    public function test_strategy_classes_are_registered()
    {
        // Test that strategy classes can be resolved with all dependencies
        $privatePharmacyStrategy = $this->app->make(PrivatePharmacyStrategy::class);
        $this->assertInstanceOf(PrivatePharmacyStrategy::class, $privatePharmacyStrategy);

        $storeStrategy = $this->app->make(StoreStrategy::class);
        $this->assertInstanceOf(StoreStrategy::class, $storeStrategy);

        $localChainStrategy = $this->app->make(LocalChainStrategy::class);
        $this->assertInstanceOf(LocalChainStrategy::class, $localChainStrategy);
    }

    public function test_distribution_strategy_factory_can_create_strategies()
    {
        // Test that the factory can create strategies using the service container
        $factory = $this->app->make(DistributionStrategyFactory::class);
        $this->assertInstanceOf(DistributionStrategyFactory::class, $factory);

        // Test that factory supports all distribution types
        $this->assertTrue($factory->supports(DistributionType::PRIVATE_PHARMACY)); // PRIVATE_PHARMACY
        $this->assertTrue($factory->supports(DistributionType::STORES)); // STORES
        $this->assertTrue($factory->supports(DistributionType::LOCAL_CHAINS)); // LOCAL_CHAINS
        $this->assertFalse($factory->supports(DistributionType::fromValue(999))); // Unknown type
    }

    public function test_all_dependencies_are_properly_injected()
    {
        // Test that complex dependency injection works correctly
        $factory = $this->app->make(DistributionStrategyFactory::class);

        // This should not throw any exceptions if all dependencies are properly registered
        $privatePharmacyStrategy = $factory->create(DistributionType::PRIVATE_PHARMACY);
        $this->assertInstanceOf(PrivatePharmacyStrategy::class, $privatePharmacyStrategy);

        $storeStrategy = $factory->create(DistributionType::STORES);
        $this->assertInstanceOf(StoreStrategy::class, $storeStrategy);

        $localChainStrategy = $factory->create(DistributionType::LOCAL_CHAINS);
        $this->assertInstanceOf(LocalChainStrategy::class, $localChainStrategy);
    }

    public function test_service_provider_boot_method_runs_without_errors()
    {
        // Test that the boot method can be called without errors
        $this->serviceProvider->boot();

        // If we reach this point, the boot method executed successfully
        $this->assertTrue(true);
    }

    public function test_circular_dependencies_are_avoided()
    {
        // Test that there are no circular dependencies in the service registration
        try {
            $this->app->make(PrivatePharmacyStrategy::class);
            $this->app->make(StoreStrategy::class);
            $this->app->make(LocalChainStrategy::class);

            // If we reach this point, no circular dependencies exist
            $this->assertTrue(true);
        } catch (\Exception $e) {
            $this->fail("Circular dependency detected: " . $e->getMessage());
        }
    }
}
