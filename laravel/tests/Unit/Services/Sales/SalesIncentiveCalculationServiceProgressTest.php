<?php

namespace Tests\Unit\Services\Sales;

use App\Services\ProgressService;
use App\Services\Sales\SalesIncentiveCalculationService;
use App\Services\Sales\SalesIncentiveHolder;
use App\Services\Sales\LinkedPositionIncentiveService;
use App\Services\Reports\SalesIncentives\Fields\SalesIncentiveReportFields;
use App\Services\Reports\SalesIncentives\IncentiveMonthlyViewReportService;
use App\Services\Reports\SalesIncentives\IncentiveSalesViewReportService;
use App\Services\Reports\SalesIncentives\IncentiveReportTypes;
use App\User;
use App\Line;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Mockery;
use Tests\TestCase;

/**
 * Test class for enhanced progress tracking in SalesIncentiveCalculationService
 */
class SalesIncentiveCalculationServiceProgressTest extends TestCase
{
    private SalesIncentiveCalculationService $service;
    private $mockUser;
    private $mockSalesViewReportService;
    private $mockMonthlyViewReportService;
    private $mockReportFields;
    private $mockIncentiveHolder;
    private $mockLinkedPositionService;

    protected function setUp(): void
    {
        parent::setUp();

        // Mock all dependencies
        $this->mockSalesViewReportService = Mockery::mock(IncentiveSalesViewReportService::class);
        $this->mockMonthlyViewReportService = Mockery::mock(IncentiveMonthlyViewReportService::class);
        $this->mockReportFields = Mockery::mock(SalesIncentiveReportFields::class);
        $this->mockIncentiveHolder = Mockery::mock(SalesIncentiveHolder::class);
        $this->mockLinkedPositionService = Mockery::mock(LinkedPositionIncentiveService::class);

        // Create service instance with mocked dependencies
        $this->service = new SalesIncentiveCalculationService(
            $this->mockSalesViewReportService,
            $this->mockMonthlyViewReportService,
            $this->mockReportFields,
            $this->mockIncentiveHolder,
            $this->mockLinkedPositionService
        );

        // Mock user
        $this->mockUser = Mockery::mock(User::class);
        $this->mockUser->shouldReceive('getAttribute')->with('id')->andReturn(1);
        $this->mockUser->shouldReceive('userLines')->andReturn(collect());
        $this->mockUser->shouldReceive('belowUsersOfAllLinesWithPositions')->andReturn(collect());
        $this->mockUser->shouldReceive('filterDivisions')->andReturn(collect());

        Auth::shouldReceive('user')->andReturn($this->mockUser);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_progress_service_enhanced_methods()
    {
        // Test phase progress calculation
        $phases = [
            'phase1' => ['weight' => 30, 'message' => 'Phase 1'],
            'phase2' => ['weight' => 50, 'message' => 'Phase 2'],
            'phase3' => ['weight' => 20, 'message' => 'Phase 3']
        ];

        // Test phase 1 at 50% completion
        $result = ProgressService::updatePhaseProgress(1, 'test-request', 'phase1', 50, $phases, 'Testing phase 1');
        $this->assertTrue($result);

        // Verify progress calculation: 50% of 30% = 15% overall
        $progress = ProgressService::getProgress(1, 'test-request');
        $this->assertNotNull($progress);
        $this->assertEquals(15, $progress['progress']);

        // Test phase 2 at 100% completion (phase 1 complete + phase 2 complete)
        ProgressService::updatePhaseProgress(1, 'test-request', 'phase2', 100, $phases, 'Testing phase 2');
        $progress = ProgressService::getProgress(1, 'test-request');
        $this->assertEquals(80, $progress['progress']); // 30% + 50% = 80%
    }

    public function test_progress_service_cancel_functionality()
    {
        // Register progress
        ProgressService::registerProgress(1, 'test-cancel', 'Starting test');
        
        // Cancel progress
        $result = ProgressService::cancelProgress(1, 'test-cancel', 'Test cancellation');
        $this->assertTrue($result);

        // Verify cancellation
        $progress = ProgressService::getProgress(1, 'test-cancel');
        $this->assertNotNull($progress);
        $this->assertTrue($progress['cancelled'] ?? false);
        $this->assertTrue($progress['isComplete']);
    }

    public function test_progress_tracking_initialization()
    {
        // Mock the request to include SSERequestId
        request()->merge(['SSERequestId' => 'test-request-123']);

        // Mock Line model using Mockery alias
        $mockLine = Mockery::mock('alias:' . Line::class);
        $mockLine->shouldReceive('whereIn')->andReturnSelf();
        $mockLine->shouldReceive('with')->andReturnSelf();
        $mockLine->shouldReceive('get')->andReturn(collect());

        // Mock report fields
        $this->mockReportFields->shouldReceive('getFields')->andReturn([]);

        // Mock incentive holder
        $this->mockIncentiveHolder->shouldReceive('init')->once();
        $this->mockIncentiveHolder->shouldReceive('getTrackedManagerIncentives')->andReturn([]);

        // Mock view report service configuration
        $this->mockSalesViewReportService->shouldReceive('configureReportService')->once();

        // Mock linked position service
        $this->mockLinkedPositionService->shouldReceive('process')->once();

        // Test filter method with progress tracking
        $saleFilter = [
            'fromDate' => '2024-01-01',
            'toDate' => '2024-01-31',
            'lines' => [1, 2],
            'filter' => 1,
            'view' => IncentiveReportTypes::SALES_VIEW->value,
            'checked' => false,
            'mappingType' => 'test'
        ];

        // Clear any existing cache
        Cache::forget('sse_progress_1');

        $result = $this->service->filter($saleFilter);

        // Verify result structure
        $this->assertIsArray($result);
        $this->assertArrayHasKey('data', $result);
        $this->assertArrayHasKey('fields', $result);

        // Verify progress was tracked
        $progress = ProgressService::getProgress(1, 'test-request-123');
        $this->assertNotNull($progress);
        $this->assertTrue($progress['isComplete']);
        $this->assertEquals(100, $progress['progress']);
    }

    public function test_error_handling_in_progress_tracking()
    {
        // Test with invalid user
        $saleFilter = [
            'user_id' => 999999, // Non-existent user
            'fromDate' => '2024-01-01',
            'toDate' => '2024-01-31',
            'lines' => [1],
            'view' => IncentiveReportTypes::SALES_VIEW->value
        ];

        $mockUser = Mockery::mock('alias:' . User::class);
        $mockUser->shouldReceive('find')->with(999999)->andReturn(null);
        request()->merge(['SSERequestId' => 'error-test']);

        $this->expectException(\RuntimeException::class);
        $this->expectExceptionMessage('Invalid or unauthorized user for incentive calculation.');

        $this->service->filter($saleFilter);

        // Verify error was logged in progress
        // Progress might be null or contain error information
        // We just verify the exception was thrown correctly
    }

    public function test_memory_and_performance_logging()
    {
        // Enable log capture
        Log::shouldReceive('info')->atLeast()->once();
        Log::shouldReceive('debug')->zeroOrMoreTimes();

        request()->merge(['SSERequestId' => 'perf-test']);

        // Mock dependencies for successful execution
        $mockLinePerf = Mockery::mock('alias:' . Line::class);
        $mockLinePerf->shouldReceive('whereIn')->andReturnSelf();
        $mockLinePerf->shouldReceive('with')->andReturnSelf();
        $mockLinePerf->shouldReceive('get')->andReturn(collect([
            (object)['id' => 1, 'name' => 'Test Line', 'divisions' => collect(), 'products' => collect()]
        ]));

        $this->mockReportFields->shouldReceive('getFields')->andReturn([]);
        $this->mockIncentiveHolder->shouldReceive('init')->once();
        $this->mockIncentiveHolder->shouldReceive('getTrackedManagerIncentives')->andReturn([]);
        $this->mockSalesViewReportService->shouldReceive('configureReportService')->once();
        $this->mockLinkedPositionService->shouldReceive('process')->once();

        $saleFilter = [
            'fromDate' => '2024-01-01',
            'toDate' => '2024-01-31',
            'lines' => [1],
            'filter' => 1,
            'view' => IncentiveReportTypes::SALES_VIEW->value,
            'checked' => false,
            'mappingType' => 'test'
        ];

        $result = $this->service->filter($saleFilter);

        // Verify that performance metrics were logged
        // This is verified through the Log::shouldReceive('info') expectation above
        $this->assertIsArray($result);
    }
}
