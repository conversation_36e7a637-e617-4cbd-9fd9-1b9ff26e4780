<?php

namespace Tests\Unit\Services\Sales\Ceiling\Strategies\Distribution;

use App\Sale;
use App\SaleDetail;
use App\Services\Sales\Ceiling\Strategies\Distribution\{
    LocalChainStrategy,
    DistributionStrategy,
    DistributionType,
    Contracts\TransactionManagerInterface,
    Contracts\SettingsProviderInterface,
    Contracts\LimitCalculatorInterface,
    Services\SaleDetailFactory,
    Services\SaleCreator,
    Algorithms\HierarchicalChainDistributionAlgorithm
};
use App\Services\SalesDistributionService;
use Illuminate\Support\Collection;
use Tests\TestCase;
use Mockery;

/**
 * Test class for LocalChainStrategy
 *
 * Tests the local chain distribution strategy that uses hierarchical chain distribution
 * for excess sales with proper dependency injection and SOLID principles
 *
 * @covers \App\Services\Sales\Ceiling\Strategies\Distribution\LocalChainStrategy
 */
class LocalChainStrategyTest extends TestCase
{
    private LocalChainStrategy $strategy;
    private TransactionManagerInterface $transactionManager;
    private SettingsProviderInterface $settingsProvider;
    private LimitCalculatorInterface $limitCalculator;
    private SaleDetailFactory $saleDetailFactory;
    private SaleCreator $saleCreator;
    private SalesDistributionService $salesService;
    private HierarchicalChainDistributionAlgorithm $excessDistributor;

    protected function setUp(): void
    {
        parent::setUp();

        $this->transactionManager = Mockery::mock(TransactionManagerInterface::class);
        $this->settingsProvider = Mockery::mock(SettingsProviderInterface::class);
        $this->limitCalculator = Mockery::mock(LimitCalculatorInterface::class);
        $this->saleDetailFactory = Mockery::mock(SaleDetailFactory::class);
        $this->saleCreator = Mockery::mock(SaleCreator::class);
        $this->salesService = Mockery::mock(SalesDistributionService::class);
        $this->excessDistributor = Mockery::mock(HierarchicalChainDistributionAlgorithm::class);

        $this->strategy = new LocalChainStrategy(
            $this->transactionManager,
            $this->settingsProvider,
            $this->limitCalculator,
            $this->saleDetailFactory,
            $this->saleCreator,
            $this->salesService,
            $this->excessDistributor
        );

        // Enable Mockery to mock static methods
        Mockery::getConfiguration()->allowMockingNonExistentMethods(true);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    /**
     * Test successful recalculate and distribute differences
     */
    public function test_recalculate_and_distribute_differences_success(): void
    {
        // Arrange
        $ceilingSales = collect([
            $this->createMockCeilingSale(),
        ]);

        $salesContributionBaseOn = [1, 2, 3];
        $originalSale = $this->createMockSale();

        // Create a test double that overrides getOriginalSale
        $strategyMock = new class(
            $this->transactionManager,
            $this->settingsProvider,
            $this->limitCalculator,
            $this->saleDetailFactory,
            $this->saleCreator,
            $this->salesService,
            $this->excessDistributor
        ) extends LocalChainStrategy {
            private $originalSale;

            public function setOriginalSale(Sale $sale) {
                $this->originalSale = $sale;
            }

            protected function getOriginalSale($ceilingSale): ?Sale {
                return $this->originalSale;
            }
        };

        $strategyMock->setOriginalSale($originalSale);

        // Mock transaction manager
        $this->transactionManager
            ->shouldReceive('executeInTransaction')
            ->once()
            ->andReturnUsing(function ($callback) {
                return $callback();
            });

        // Mock settings provider
        $this->settingsProvider
            ->shouldReceive('getSalesContributionSettings')
            ->once()
            ->andReturn($salesContributionBaseOn);

        // Mock validation success
        $this->limitCalculator
            ->shouldReceive('exceedsLimit')
            ->once()
            ->andReturn(true);

        // Mock createLimitedSaleDistribution
        $this->mockCreateLimitedSaleDistribution(true);

        // Mock updateOriginalSalesCeiling
        $this->saleCreator
            ->shouldReceive('updateOriginalSalesCeiling')
            ->once()
            ->andReturn(true);

        // Mock createAndDistributeExcessSale
        $this->mockCreateAndDistributeExcessSaleSuccess();

        // Act
        $result = $strategyMock->recalculateAndDistributeDifferences($ceilingSales);

        // Assert
        $this->assertTrue($result);
    }

    /**
     * Test recalculate and distribute differences with validation failure
     */
    public function test_recalculate_and_distribute_differences_validation_failure(): void
    {
        // Arrange
        $ceilingSales = collect([
            $this->createMockCeilingSale(),
        ]);

        $salesContributionBaseOn = [1, 2, 3];

        // Mock transaction manager
        $this->transactionManager
            ->shouldReceive('executeInTransaction')
            ->once()
            ->andReturnUsing(function ($callback) {
                return $callback();
            });

        // Mock settings provider
        $this->settingsProvider
            ->shouldReceive('getSalesContributionSettings')
            ->once()
            ->andReturn($salesContributionBaseOn);

        // Mock validation failure
        $this->limitCalculator
            ->shouldReceive('exceedsLimit')
            ->once()
            ->andReturn(false);

        // Act
        $result = $this->strategy->recalculateAndDistributeDifferences($ceilingSales);

        // Assert
        $this->assertTrue($result); // Should still return true as it continues processing
    }

    /**
     * Test recalculate and distribute differences with transaction failure
     */
    public function test_recalculate_and_distribute_differences_transaction_failure(): void
    {
        // Arrange
        $ceilingSales = collect([
            $this->createMockCeilingSale(),
        ]);

        // Mock transaction manager to throw exception
        $this->transactionManager
            ->shouldReceive('executeInTransaction')
            ->once()
            ->andThrow(new \Exception('Transaction failed'));

        // Act
        $result = $this->strategy->recalculateAndDistributeDifferences($ceilingSales);

        // Assert
        $this->assertFalse($result);
    }

    /**
     * Test create and distribute excess sale success
     */
    public function test_create_and_distribute_excess_sale_success(): void
    {
        // Arrange
        $ceilingSale = $this->createMockCeilingSale();
        $originalSale = $this->createMockSale();
        $excessSale = $this->createMockSale();
        $salesContributionBaseOn = [1, 2, 3];

        // Mock excess distributor
        $this->excessDistributor
            ->shouldReceive('calculateExcessQuantity')
            ->once()
            ->with($ceilingSale)
            ->andReturn(25.0);

        $this->excessDistributor
            ->shouldReceive('distributeExcessSale')
            ->once()
            ->with($excessSale, $salesContributionBaseOn, $originalSale, DistributionType::LOCAL_CHAINS)
            ->andReturn(true);

        // Mock sale creator
        $this->saleCreator
            ->shouldReceive('createExcessSale')
            ->once()
            ->with($ceilingSale, 25.0)
            ->andReturn($excessSale);

        $this->saleCreator
            ->shouldReceive('loadRelationships')
            ->once()
            ->with($excessSale)
            ->andReturn($excessSale);

        $this->saleCreator
            ->shouldReceive('attachMapping')
            ->once()
            ->with($excessSale, $ceilingSale->mapping_id);

        // Act
        $result = $this->mockStrategyCreateAndDistributeExcessSale($this->strategy, $ceilingSale, $originalSale, $salesContributionBaseOn);

        // Assert
        $this->assertTrue($result);
    }

    /**
     * Test create and distribute excess sale with distribution failure
     */
    public function test_create_and_distribute_excess_sale_distribution_failure(): void
    {
        // Arrange
        $ceilingSale = $this->createMockCeilingSale();
        $originalSale = $this->createMockSale();
        $excessSale = $this->createMockSale();
        $salesContributionBaseOn = [1, 2, 3];

        // Mock excess distributor
        $this->excessDistributor
            ->shouldReceive('calculateExcessQuantity')
            ->once()
            ->with($ceilingSale)
            ->andReturn(25.0);

        $this->excessDistributor
            ->shouldReceive('distributeExcessSale')
            ->once()
            ->with($excessSale, $salesContributionBaseOn, $originalSale, DistributionType::LOCAL_CHAINS)
            ->andReturn(false);

        // Mock sale creator
        $this->saleCreator
            ->shouldReceive('createExcessSale')
            ->once()
            ->with($ceilingSale, 25.0)
            ->andReturn($excessSale);

        $this->saleCreator
            ->shouldReceive('loadRelationships')
            ->once()
            ->with($excessSale)
            ->andReturn($excessSale);

        // Should not call attachMapping when distribution fails
        $this->saleCreator
            ->shouldNotReceive('attachMapping');

        // Act
        $result = $this->mockStrategyCreateAndDistributeExcessSale($this->strategy, $ceilingSale, $originalSale, $salesContributionBaseOn);

        // Assert
        $this->assertFalse($result);
    }

    /**
     * Test that strategy implements DistributionStrategy interface
     */
    public function test_implements_distribution_strategy_interface(): void
    {
        $this->assertInstanceOf(DistributionStrategy::class, $this->strategy);
    }

    /**
     * Test strategy extends AbstractDistributionStrategy
     */
    public function test_extends_abstract_distribution_strategy(): void
    {
        $this->assertInstanceOf(
            \App\Services\Sales\Ceiling\Strategies\Distribution\AbstractDistributionStrategy::class,
            $this->strategy
        );
    }

    /**
     * Test strategy with empty ceiling sales collection
     */
    public function test_recalculate_and_distribute_differences_empty_collection(): void
    {
        // Arrange
        $ceilingSales = collect([]);
        $salesContributionBaseOn = [1, 2, 3];

        // Mock transaction manager
        $this->transactionManager
            ->shouldReceive('executeInTransaction')
            ->once()
            ->andReturnUsing(function ($callback) {
                return $callback();
            });

        // Mock settings provider
        $this->settingsProvider
            ->shouldReceive('getSalesContributionSettings')
            ->once()
            ->andReturn($salesContributionBaseOn);

        // Act
        $result = $this->strategy->recalculateAndDistributeDifferences($ceilingSales);

        // Assert
        $this->assertTrue($result);
    }

    /**
     * Helper method to mock createLimitedSaleDistribution
     */
    private function mockCreateLimitedSaleDistribution(bool $success): void
    {
        $this->limitCalculator
            ->shouldReceive('calculateLimit')
            ->once()
            ->andReturn(100.0);

        $limitedSale = $this->createMockSale();

        $this->saleCreator
            ->shouldReceive('createLimitedSale')
            ->once()
            ->andReturn($limitedSale);

        // LocalChainStrategy calls attachMapping in createLimitedSaleDistribution
        $this->saleCreator
            ->shouldReceive('attachMapping')
            ->once()
            ->with($limitedSale, Mockery::any());

        $this->saleDetailFactory
            ->shouldReceive('createLimitedSaleDetails')
            ->once()
            ->andReturn($success);
    }

    /**
     * Helper method to mock createAndDistributeExcessSale success
     */
    private function mockCreateAndDistributeExcessSaleSuccess(): void
    {
        $excessSale = $this->createMockSale();

        $this->excessDistributor
            ->shouldReceive('calculateExcessQuantity')
            ->once()
            ->andReturn(25.0);

        $this->excessDistributor
            ->shouldReceive('distributeExcessSale')
            ->once()
            ->andReturn(true);

        $this->saleCreator
            ->shouldReceive('createExcessSale')
            ->once()
            ->andReturn($excessSale);

        $this->saleCreator
            ->shouldReceive('loadRelationships')
            ->once()
            ->andReturn($excessSale);

        $this->saleCreator
            ->shouldReceive('attachMapping')
            ->once();
    }

    /**
     * Helper method to invoke protected createAndDistributeExcessSale method
     */
    private function mockStrategyCreateAndDistributeExcessSale($strategy, $ceilingSale, Sale $originalSale, array $salesContributionBaseOn): bool
    {
        return (new \ReflectionMethod($strategy, 'createAndDistributeExcessSale'))
            ->invoke($strategy, $ceilingSale, $originalSale, $salesContributionBaseOn);
    }

    /**
     * Create a mock ceiling sale object
     */
    private function createMockCeilingSale(): object
    {
        return (object) [
            'id' => 1,
            'sale_ids' => '1,2,3',
            'number_of_units' => 125,
            'mapping_id' => 100,
            'distributor_id' => 1,
            'date' => '2023-01-01'
        ];
    }

    /**
     * Create a mock Sale object
     */
    private function createMockSale(): Sale
    {
        $sale = Mockery::mock(Sale::class);

        // Allow setAttribute calls for property assignments
        $sale->shouldReceive('setAttribute')->andReturnSelf();
        $sale->shouldReceive('getAttribute')->andReturnUsing(function ($key) {
            switch ($key) {
                case 'id':
                    return 1;
                case 'quantity':
                    return 100;
                case 'value':
                    return 1000;
                case 'bonus':
                    return 50;
                case 'region':
                    return 5;
                default:
                    return null;
            }
        });

        // Allow other common Eloquent methods that might be called
        $sale->shouldReceive('load')->andReturnSelf();
        $sale->shouldReceive('mappings')->andReturnSelf();
        $sale->shouldReceive('attach')->andReturn(true);
        $sale->shouldReceive('with')->andReturnSelf();
        $sale->shouldReceive('details')->andReturn(collect([]));

        // Set up public properties that can be accessed directly
        $sale->id = 1;
        $sale->quantity = 100;
        $sale->value = 1000;
        $sale->bonus = 50;
        $sale->region = 5;

        return $sale;
    }
}
