<?php

namespace Tests\Unit\Services\Sales\Ceiling\Strategies\Distribution;

use App\Sale;
use App\Services\Sales\Ceiling\Strategies\Distribution\{
    DistributionStrategyFactory,
    DistributionType,
    PrivatePharmacyStrategy,
    StoreStrategy,
    LocalChainStrategy
};
use App\Services\SalesDistributionService;
use App\Services\Enums\SaleDistribution;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\{DB, Log};
use Tests\TestCase;
use Mockery;
use ReflectionClass;

/**
 * Test class for Laravel Octane compatibility of Distribution strategies
 *
 * Tests for data races, memory leaks, and state isolation issues that could
 * occur in a persistent memory environment like Laravel Octane.
 *
 * @covers \App\Services\Sales\Ceiling\Strategies\Distribution\DistributionStrategyFactory
 * @covers \App\Services\Sales\Ceiling\Strategies\Distribution\PrivatePharmacyStrategy
 * @covers \App\Services\Sales\Ceiling\Strategies\Distribution\StoreStrategy
 * @covers \App\Services\Sales\Ceiling\Strategies\Distribution\LocalChainStrategy
 * @covers \App\Services\SalesDistributionService
 */
class DistributionOctaneCompatibilityTest extends TestCase
{
    private DistributionStrategyFactory $factory;
    private array $memorySnapshots = [];

    protected function setUp(): void
    {
        parent::setUp();

        // Take initial memory snapshot
        $this->memorySnapshots['initial'] = memory_get_usage(true);

        $this->factory = $this->app->make(DistributionStrategyFactory::class);
    }

    protected function tearDown(): void
    {
        // Force garbage collection
        gc_collect_cycles();

        parent::tearDown();
    }

    /**
     * Test that SalesService instances don't share state between requests
     */
    public function test_sales_service_state_isolation(): void
    {
        // Arrange - Create multiple SalesService instances
        $service1 = SalesDistributionService::make(SaleDistribution::NORMAL);
        $service2 = SalesDistributionService::make(SaleDistribution::DIRECT);
        $service3 = SalesDistributionService::make(SaleDistribution::NORMAL);

        // Act - Modify state on first service
        $service1->addSelect('custom_field_1');
        $service1->forNegativeCeiling();

        // Modify state on second service
        $service2->addSelect('custom_field_2');

        // Assert - Services should have isolated state
        $reflection1 = new ReflectionClass($service1);
        $reflection2 = new ReflectionClass($service2);
        $reflection3 = new ReflectionClass($service3);

        $selections1 = $reflection1->getProperty('selections');
        $selections1->setAccessible(true);
        $selections2 = $reflection2->getProperty('selections');
        $selections2->setAccessible(true);
        $selections3 = $reflection3->getProperty('selections');
        $selections3->setAccessible(true);

        $isNegative1 = $reflection1->getProperty('isNegativeCeiling');
        $isNegative1->setAccessible(true);
        $isNegative2 = $reflection2->getProperty('isNegativeCeiling');
        $isNegative2->setAccessible(true);
        $isNegative3 = $reflection3->getProperty('isNegativeCeiling');
        $isNegative3->setAccessible(true);

        // Service 1 should have custom field and negative ceiling
        $this->assertContains('custom_field_1', $selections1->getValue($service1));
        $this->assertTrue($isNegative1->getValue($service1));

        // Service 2 should have different custom field and no negative ceiling
        $this->assertContains('custom_field_2', $selections2->getValue($service2));
        $this->assertFalse($isNegative2->getValue($service2));

        // Service 3 should be clean (same type as service1 but different instance)
        $this->assertNotContains('custom_field_1', $selections3->getValue($service3));
        $this->assertFalse($isNegative3->getValue($service3));
    }

    /**
     * Test concurrent strategy creation doesn't cause data races
     */
    public function test_concurrent_strategy_creation(): void
    {
        // Arrange
        $strategies = [];
        $types = [
            DistributionType::PRIVATE_PHARMACY,
            DistributionType::STORES,
            DistributionType::LOCAL_CHAINS
        ];

        // Act - Simulate concurrent strategy creation
        for ($i = 0; $i < 10; $i++) {
            foreach ($types as $type) {
                $strategies[] = $this->factory->create($type);
            }
        }

        // Assert - All strategies should be properly instantiated and isolated
        $this->assertCount(30, $strategies);

        // Group strategies by type
        $privatePharmacyStrategies = array_filter($strategies, fn($s) => $s instanceof PrivatePharmacyStrategy);
        $storeStrategies = array_filter($strategies, fn($s) => $s instanceof StoreStrategy);
        $localChainStrategies = array_filter($strategies, fn($s) => $s instanceof LocalChainStrategy);

        $this->assertCount(10, $privatePharmacyStrategies);
        $this->assertCount(10, $storeStrategies);
        $this->assertCount(10, $localChainStrategies);

        // Each strategy should be a separate instance
        $privatePharmacyInstances = array_map('spl_object_id', $privatePharmacyStrategies);
        $storeInstances = array_map('spl_object_id', $storeStrategies);
        $localChainInstances = array_map('spl_object_id', $localChainStrategies);

        $this->assertCount(10, array_unique($privatePharmacyInstances));
        $this->assertCount(10, array_unique($storeInstances));
        $this->assertCount(10, array_unique($localChainInstances));
    }

    /**
     * Test memory usage doesn't grow excessively with repeated operations
     */
    public function test_memory_leak_detection(): void
    {
        // Arrange
        $initialMemory = memory_get_usage(true);
        $memoryReadings = [];

        // Act - Perform multiple strategy operations
        for ($i = 0; $i < 50; $i++) {
            $strategy = $this->factory->create(DistributionType::PRIVATE_PHARMACY);

            // Simulate some work
            $this->assertInstanceOf(PrivatePharmacyStrategy::class, $strategy);

            // Take memory reading every 10 iterations
            if ($i % 10 === 0) {
                gc_collect_cycles(); // Force garbage collection
                $memoryReadings[] = memory_get_usage(true);
            }

            // Explicitly unset to help garbage collection
            unset($strategy);
        }

        // Assert - Memory should not grow significantly
        $finalMemory = end($memoryReadings);
        $memoryGrowth = $finalMemory - $initialMemory;

        // Allow for some memory growth but not excessive (less than 5MB)
        $this->assertLessThan(5 * 1024 * 1024, $memoryGrowth,
            "Memory grew by " . number_format($memoryGrowth / 1024 / 1024, 2) . "MB, which may indicate a memory leak");

        // Memory readings should not show continuous growth
        $growthTrend = 0;
        for ($i = 1; $i < count($memoryReadings); $i++) {
            if ($memoryReadings[$i] > $memoryReadings[$i - 1]) {
                $growthTrend++;
            }
        }

        // Not all readings should show growth (some should be stable or decrease)
        $this->assertLessThan(count($memoryReadings) - 1, $growthTrend,
            "Memory appears to be continuously growing, indicating a potential memory leak");
    }

    /**
     * Test cache key isolation prevents data races
     */
    public function test_cache_key_isolation(): void
    {
        // Arrange - Test cache key generation without actual database calls
        $service1 = SalesDistributionService::make(SaleDistribution::NORMAL);
        $service2 = SalesDistributionService::make(SaleDistribution::DIRECT);
        $service3 = SalesDistributionService::make(SaleDistribution::NORMAL);

        // Modify state to create different cache keys
        $service1->addSelect('custom_field_1');
        $service3->addSelect('custom_field_3');

        // Act - Generate cache keys using reflection to test uniqueness
        // Simulate the actual cache key generation that happens in getRatiosForDistribution
        $cacheKeys = [];
        $services = [$service1, $service2, $service3];

        foreach ($services as $service) {
            $reflection = new ReflectionClass($service);
            $method = $reflection->getMethod('generateCacheKey');
            $method->setAccessible(true);

            // Get service state properties
            $selectionsProperty = $reflection->getProperty('selections');
            $selectionsProperty->setAccessible(true);
            $selections = $selectionsProperty->getValue($service);

            $negativeCeilingProperty = $reflection->getProperty('isNegativeCeiling');
            $negativeCeilingProperty->setAccessible(true);
            $isNegativeCeiling = $negativeCeilingProperty->getValue($service);

            $distributionProperty = $reflection->getProperty('saleDistribution');
            $distributionProperty->setAccessible(true);
            $saleDistribution = $distributionProperty->getValue($service);

            // Simulate the parameters that getRatiosForDistribution would pass
            $functionParams = [$saleDistribution, $isNegativeCeiling, ...$selections, '2024-01-01', 1, [1, 2, 3]];
            $cacheKeys[] = $method->invoke($service, 'getRatiosForDistribution', $functionParams);
        }

        // Assert - Each service should generate different cache keys due to different state
        $this->assertCount(3, $cacheKeys);

        // Debug: Let's see what the cache keys actually are
        $uniqueKeys = array_unique($cacheKeys);
        if (count($uniqueKeys) !== 3) {
            $this->fail(sprintf(
                'Expected 3 unique cache keys but got %d. Keys: %s',
                count($uniqueKeys),
                implode(', ', $cacheKeys)
            ));
        }

        $this->assertCount(3, $uniqueKeys,
            'Cache keys should be unique to prevent data races between different service states');
    }

    /**
     * Test database state isolation (SQL mode changes don't persist)
     */
    public function test_database_state_isolation(): void
    {
        // Arrange
        $sqlModeChanges = [];

        // Mock DB facade completely to avoid any database calls
        DB::partialMock()
            ->shouldReceive('statement')
            ->withArgs(function ($sql) use (&$sqlModeChanges) {
                if (str_contains($sql, 'sql_mode')) {
                    $sqlModeChanges[] = $sql;
                }
                return true;
            })
            ->andReturn(true);

        // Act - Simulate the SQL mode change that happens in SalesService
        DB::statement("set sql_mode=''");

        // Assert - SQL mode change should be tracked
        $this->assertCount(1, $sqlModeChanges);
        $this->assertEquals("set sql_mode=''", $sqlModeChanges[0]);
        $this->assertTrue(true, 'SQL mode change detected - ensure it does not persist between requests');
    }

    /**
     * Test service provider registration creates fresh instances
     */
    public function test_service_provider_creates_fresh_instances(): void
    {
        // Arrange & Act - Create multiple instances of the same strategy type
        $strategy1 = $this->factory->create(DistributionType::PRIVATE_PHARMACY);
        $strategy2 = $this->factory->create(DistributionType::PRIVATE_PHARMACY);
        $strategy3 = $this->factory->create(DistributionType::PRIVATE_PHARMACY);

        // Assert - Each should be a separate instance
        $this->assertNotSame($strategy1, $strategy2);
        $this->assertNotSame($strategy2, $strategy3);
        $this->assertNotSame($strategy1, $strategy3);

        // All should be the same type
        $this->assertInstanceOf(PrivatePharmacyStrategy::class, $strategy1);
        $this->assertInstanceOf(PrivatePharmacyStrategy::class, $strategy2);
        $this->assertInstanceOf(PrivatePharmacyStrategy::class, $strategy3);

        // Object IDs should be different
        $this->assertNotEquals(spl_object_id($strategy1), spl_object_id($strategy2));
        $this->assertNotEquals(spl_object_id($strategy2), spl_object_id($strategy3));
        $this->assertNotEquals(spl_object_id($strategy1), spl_object_id($strategy3));
    }

    /**
     * Test that factory supports method is stateless
     */
    public function test_factory_supports_method_stateless(): void
    {
        // Arrange
        $types = [
            DistributionType::PRIVATE_PHARMACY,
            DistributionType::STORES,
            DistributionType::LOCAL_CHAINS,
            DistributionType::UNKNOWN
        ];

        // Act & Assert - Multiple calls should return consistent results
        for ($i = 0; $i < 10; $i++) {
            foreach ($types as $type) {
                $result1 = $this->factory->supports($type);
                $result2 = $this->factory->supports($type);

                $this->assertEquals($result1, $result2,
                    "Factory supports method should be stateless and return consistent results");

                // Known results
                if ($type === DistributionType::UNKNOWN) {
                    $this->assertFalse($result1);
                } else {
                    $this->assertTrue($result1);
                }
            }
        }
    }

    /**
     * Test cache memory accumulation over time
     */
    public function test_cache_memory_accumulation(): void
    {
        // Arrange - Test cache key generation patterns
        $cacheKeys = [];

        // Act - Generate many unique cache keys to test for patterns
        for ($i = 0; $i < 20; $i++) {
            $service = SalesDistributionService::make(SaleDistribution::NORMAL);
            $service->addSelect("custom_field_$i"); // Create unique cache keys

            // Generate cache key using reflection
            $reflection = new ReflectionClass($service);
            $method = $reflection->getMethod('generateCacheKey');
            $method->setAccessible(true);

            $params = ['testFunction', ['param1', 'param2', $i]];
            $cacheKeys[] = $method->invoke($service, ...$params);
        }

        // Assert - All cache keys should be unique
        $this->assertCount(20, $cacheKeys);
        $this->assertCount(20, array_unique($cacheKeys),
            'All cache keys should be unique to prevent cache collisions');
    }


}
