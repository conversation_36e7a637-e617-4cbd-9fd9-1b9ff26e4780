<?php

namespace Tests\Unit\Services\Sales\Ceiling\Strategies\Distribution\Services;

use App\SalesSetting;
use App\Services\Sales\Ceiling\Strategies\Distribution\Services\SalesSettingsProvider;
use App\Services\Sales\Ceiling\Strategies\Distribution\Contracts\SettingsProviderInterface;
use Illuminate\Support\Facades\Cache;
use Tests\TestCase;
use Mockery;

/**
 * Test class for SalesSettingsProvider
 * 
 * Tests the sales settings provider service that retrieves sales contribution settings
 * and other configuration values with caching support
 * 
 * @covers \App\Services\Sales\Ceiling\Strategies\Distribution\Services\SalesSettingsProvider
 */
class SalesSettingsProviderTest extends TestCase
{
    private SalesSettingsProvider $settingsProvider;

    protected function setUp(): void
    {
        parent::setUp();
        $this->settingsProvider = new SalesSettingsProvider();
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    /**
     * Test get sales contribution settings from cache
     */
    public function test_get_sales_contribution_settings_from_cache(): void
    {
        // Arrange
        $expectedSettings = ['1', '2', '3'];
        
        Cache::shouldReceive('remember')
            ->once()
            ->with('sales_contribution_base_on', 3600, Mockery::type('callable'))
            ->andReturn($expectedSettings);
        
        // Act
        $result = $this->settingsProvider->getSalesContributionSettings();
        
        // Assert
        $this->assertEquals($expectedSettings, $result);
    }

    /**
     * Test get sales contribution settings from database
     */
    public function test_get_sales_contribution_settings_from_database(): void
    {
        // Arrange
        $mockSetting = Mockery::mock('alias:' . SalesSetting::class);
        $mockSetting->shouldReceive('where')
            ->with('key', 'sales_contribution_base_on')
            ->andReturnSelf();
        $mockSetting->shouldReceive('value')
            ->with('value')
            ->andReturn('1,2,3');

        Cache::shouldReceive('remember')
            ->once()
            ->with('sales_contribution_base_on', 3600, Mockery::type('callable'))
            ->andReturnUsing(function ($key, $ttl, $callback) {
                return $callback();
            });

        // Act
        $result = $this->settingsProvider->getSalesContributionSettings();
        
        // Assert
        $this->assertEquals(['1', '2', '3'], $result);
    }

    /**
     * Test get sales contribution settings with empty value
     */
    public function test_get_sales_contribution_settings_empty_value(): void
    {
        // Arrange
        $mockSetting = Mockery::mock('alias:' . SalesSetting::class);
        $mockSetting->shouldReceive('where')
            ->with('key', 'sales_contribution_base_on')
            ->andReturnSelf();
        $mockSetting->shouldReceive('value')
            ->with('value')
            ->andReturn(null);

        Cache::shouldReceive('remember')
            ->once()
            ->with('sales_contribution_base_on', 3600, Mockery::type('callable'))
            ->andReturnUsing(function ($key, $ttl, $callback) {
                return $callback();
            });

        // Act
        $result = $this->settingsProvider->getSalesContributionSettings();
        
        // Assert
        $this->assertEquals([''], $result);
    }

    /**
     * Test get sales contribution settings with complex comma-separated values
     */
    public function test_get_sales_contribution_settings_complex_values(): void
    {
        // Arrange
        $mockSetting = Mockery::mock('alias:' . SalesSetting::class);
        $mockSetting->shouldReceive('where')
            ->with('key', 'sales_contribution_base_on')
            ->andReturnSelf();
        $mockSetting->shouldReceive('value')
            ->with('value')
            ->andReturn('10,20,30,40,50');

        Cache::shouldReceive('remember')
            ->once()
            ->with('sales_contribution_base_on', 3600, Mockery::type('callable'))
            ->andReturnUsing(function ($key, $ttl, $callback) {
                return $callback();
            });

        // Act
        $result = $this->settingsProvider->getSalesContributionSettings();
        
        // Assert
        $this->assertEquals(['10', '20', '30', '40', '50'], $result);
    }

    /**
     * Test get setting with existing value
     */
    public function test_get_setting_with_value(): void
    {
        // Arrange
        $mockSetting = Mockery::mock('alias:' . SalesSetting::class);
        $mockSetting->shouldReceive('where')
            ->with('key', 'test_key')
            ->andReturnSelf();
        $mockSetting->shouldReceive('value')
            ->with('value')
            ->andReturn('test_value');

        Cache::shouldReceive('remember')
            ->once()
            ->with('sales_setting_test_key', 3600, Mockery::type('callable'))
            ->andReturnUsing(function ($key, $ttl, $callback) {
                return $callback();
            });

        // Act
        $result = $this->settingsProvider->getSetting('test_key', 'default');
        
        // Assert
        $this->assertEquals('test_value', $result);
    }

    /**
     * Test get setting with default value when setting not found
     */
    public function test_get_setting_with_default(): void
    {
        // Arrange
        $mockSetting = Mockery::mock('alias:' . SalesSetting::class);
        $mockSetting->shouldReceive('where')
            ->with('key', 'test_key')
            ->andReturnSelf();
        $mockSetting->shouldReceive('value')
            ->with('value')
            ->andReturn(null);

        Cache::shouldReceive('remember')
            ->once()
            ->with('sales_setting_test_key', 3600, Mockery::type('callable'))
            ->andReturnUsing(function ($key, $ttl, $callback) {
                return $callback();
            });

        // Act
        $result = $this->settingsProvider->getSetting('test_key', 'default');
        
        // Assert
        $this->assertEquals('default', $result);
    }

    /**
     * Test get setting without default value
     */
    public function test_get_setting_without_default(): void
    {
        // Arrange
        $mockSetting = Mockery::mock('alias:' . SalesSetting::class);
        $mockSetting->shouldReceive('where')
            ->with('key', 'test_key')
            ->andReturnSelf();
        $mockSetting->shouldReceive('value')
            ->with('value')
            ->andReturn(null);

        Cache::shouldReceive('remember')
            ->once()
            ->with('sales_setting_test_key', 3600, Mockery::type('callable'))
            ->andReturnUsing(function ($key, $ttl, $callback) {
                return $callback();
            });

        // Act
        $result = $this->settingsProvider->getSetting('test_key');
        
        // Assert
        $this->assertNull($result);
    }

    /**
     * Test get setting with numeric value
     */
    public function test_get_setting_with_numeric_value(): void
    {
        // Arrange
        $mockSetting = Mockery::mock('alias:' . SalesSetting::class);
        $mockSetting->shouldReceive('where')
            ->with('key', 'numeric_key')
            ->andReturnSelf();
        $mockSetting->shouldReceive('value')
            ->with('value')
            ->andReturn('123');

        Cache::shouldReceive('remember')
            ->once()
            ->with('sales_setting_numeric_key', 3600, Mockery::type('callable'))
            ->andReturnUsing(function ($key, $ttl, $callback) {
                return $callback();
            });

        // Act
        $result = $this->settingsProvider->getSetting('numeric_key', 0);
        
        // Assert
        $this->assertEquals('123', $result);
    }

    /**
     * Test get setting with boolean default
     */
    public function test_get_setting_with_boolean_default(): void
    {
        // Arrange
        $mockSetting = Mockery::mock('alias:' . SalesSetting::class);
        $mockSetting->shouldReceive('where')
            ->with('key', 'boolean_key')
            ->andReturnSelf();
        $mockSetting->shouldReceive('value')
            ->with('value')
            ->andReturn(null);

        Cache::shouldReceive('remember')
            ->once()
            ->with('sales_setting_boolean_key', 3600, Mockery::type('callable'))
            ->andReturnUsing(function ($key, $ttl, $callback) {
                return $callback();
            });

        // Act
        $result = $this->settingsProvider->getSetting('boolean_key', true);
        
        // Assert
        $this->assertTrue($result);
    }

    /**
     * Test cache key generation for different settings
     */
    public function test_cache_key_generation(): void
    {
        // Arrange
        $mockSetting = Mockery::mock('alias:' . SalesSetting::class);
        $mockSetting->shouldReceive('where')
            ->with('key', 'special_key_123')
            ->andReturnSelf();
        $mockSetting->shouldReceive('value')
            ->with('value')
            ->andReturn('special_value');

        Cache::shouldReceive('remember')
            ->once()
            ->with('sales_setting_special_key_123', 3600, Mockery::type('callable'))
            ->andReturnUsing(function ($key, $ttl, $callback) {
                return $callback();
            });

        // Act
        $result = $this->settingsProvider->getSetting('special_key_123');
        
        // Assert
        $this->assertEquals('special_value', $result);
    }

    /**
     * Test that the class implements SettingsProviderInterface
     */
    public function test_implements_settings_provider_interface(): void
    {
        $this->assertInstanceOf(SettingsProviderInterface::class, $this->settingsProvider);
    }

    /**
     * Test interface method signatures are correctly implemented
     */
    public function test_interface_methods_exist(): void
    {
        $this->assertTrue(method_exists($this->settingsProvider, 'getSalesContributionSettings'));
        $this->assertTrue(method_exists($this->settingsProvider, 'getSetting'));
    }

    /**
     * Test cache TTL constant
     */
    public function test_cache_ttl_constant(): void
    {
        // Arrange & Act
        $reflection = new \ReflectionClass(SalesSettingsProvider::class);
        $constant = $reflection->getConstant('CACHE_TTL');
        
        // Assert
        $this->assertEquals(3600, $constant);
    }
}
