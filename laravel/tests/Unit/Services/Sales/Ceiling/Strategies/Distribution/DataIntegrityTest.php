<?php

namespace Tests\Unit\Services\Sales\Ceiling\Strategies\Distribution;

use App\Sale;
use App\Services\Sales\Ceiling\Strategies\Distribution\Algorithms\SimpleDistributionAlgorithm;
use App\Services\Sales\Ceiling\Strategies\Distribution\Algorithms\SplitDistributionAlgorithm;
use App\Services\Sales\Ceiling\Strategies\Distribution\Services\SaleCreator;
use App\Services\Sales\Ceiling\Strategies\Distribution\Services\SaleDetailFactory;
use App\Services\Sales\Ceiling\Strategies\Distribution\Contracts\SaleRepositoryInterface;
use App\Services\SalesDistributionService;
use App\Services\Enums\Ceiling;
use Illuminate\Support\Facades\Log;
use Mockery;
use Tests\TestCase;

/**
 * Test data integrity for distribution algorithms and sale creation
 *
 * This test verifies that the fixes for the sales total mismatch issue work correctly.
 *
 * @group unit
 * @group distribution
 * @group data-integrity
 */
class DataIntegrityTest extends TestCase
{
    private SaleCreator $saleCreator;
    private SaleRepositoryInterface $saleRepository;

    protected function setUp(): void
    {
        parent::setUp();

        // Mock the Log facade to prevent facade errors
        Log::shouldReceive('debug', 'info', 'warning', 'error')->andReturnNull();

        $this->saleRepository = Mockery::mock(SaleRepositoryInterface::class);
        $this->saleCreator = new SaleCreator($this->saleRepository);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    /**
     * Test that SimpleDistributionAlgorithm calculates excess quantity correctly
     */
    public function test_simple_distribution_algorithm_calculates_excess_correctly(): void
    {
        // Arrange
        $mockSalesService = Mockery::mock(SalesDistributionService::class);
        $mockSaleDetailFactory = Mockery::mock(SaleDetailFactory::class);

        $algorithm = new SimpleDistributionAlgorithm($mockSalesService, $mockSaleDetailFactory);

        $ceilingSale = (object) [
            'number_of_units' => 150,
            'limit' => 100,
            'negative_limit' => -50
        ];

        // Act
        $excessQuantity = $algorithm->calculateExcessQuantity($ceilingSale);

        // Assert
        $this->assertEquals(50, $excessQuantity, 'Excess quantity should be 150 - 100 = 50');
    }

    /**
     * Test that SplitDistributionAlgorithm calculates excess quantity correctly
     */
    public function test_split_distribution_algorithm_calculates_excess_correctly(): void
    {
        // Arrange
        $mockSalesService = Mockery::mock(SalesDistributionService::class);
        $mockSaleDetailFactory = Mockery::mock(SaleDetailFactory::class);

        $algorithm = new SplitDistributionAlgorithm($mockSalesService, $mockSaleDetailFactory);

        $ceilingSale = (object) [
            'number_of_units' => 120,
            'limit' => 80,
            'negative_limit' => -40
        ];

        // Act
        $excessQuantity = $algorithm->calculateExcessQuantity($ceilingSale);

        // Assert
        $this->assertEquals(40, $excessQuantity, 'Excess quantity should be 120 - 80 = 40');
    }

    /**
     * Test that excess quantity calculation works for negative sales
     */
    public function test_excess_quantity_calculation_for_negative_sales(): void
    {
        // Arrange
        $mockSalesService = Mockery::mock(SalesDistributionService::class);
        $mockSaleDetailFactory = Mockery::mock(SaleDetailFactory::class);

        $algorithm = new SimpleDistributionAlgorithm($mockSalesService, $mockSaleDetailFactory);

        $ceilingSale = (object) [
            'number_of_units' => -80,
            'limit' => 50,
            'negative_limit' => -60
        ];

        // Act
        $excessQuantity = $algorithm->calculateExcessQuantity($ceilingSale);

        // Assert
        $this->assertEquals(-20, $excessQuantity, 'Excess quantity should be -80 - (-60) = -20');
    }

    /**
     * Test that SaleCreator creates excess sale with proportional values
     */
    public function test_sale_creator_creates_excess_sale_with_proportional_values(): void
    {
        // Arrange
        $ceilingSale = (object) [
            'id' => 1,
            'number_of_units' => 150,
            'number_of_values' => 7500.00,
            'number_of_bonus' => 15,
            'distributor_id' => 1,
            'date' => '2025-01-15',
            'sale_ids' => '1,2,3'
        ];

        $excessQuantity = 50; // 150 - 100 limit
        $expectedPercentage = 50 / 150; // 1/3
        $expectedValue = 7500.00 * $expectedPercentage; // 2500.00
        $expectedBonus = 15 * $expectedPercentage; // 5

        $expectedSaleData = [
            'quantity' => $excessQuantity,
            'value' => $expectedValue,
            'bonus' => $expectedBonus,
            'region' => 0,
            'distributor_id' => 1,
            'product_id' => 1,
            'date' => '2025-01-15',
            'ceiling' => Ceiling::DISTRIBUTED,
            'sale_ids' => '1,2,3'
        ];

        $mockSale = Mockery::mock(Sale::class);
        $mockSale->shouldReceive('getAttribute')
            ->with('id')
            ->andReturn(123);
        $mockSale->shouldReceive('setAttribute')
            ->with('id', 123)
            ->andReturnSelf();

        $this->saleRepository
            ->shouldReceive('create')
            ->once()
            ->with($expectedSaleData)
            ->andReturn($mockSale);

        // Act
        $result = $this->saleCreator->createExcessSale($ceilingSale, $excessQuantity);

        // Assert
        $this->assertSame($mockSale, $result);
    }

    /**
     * Test that SaleCreator creates limited sale with proportional values
     */
    public function test_sale_creator_creates_limited_sale_with_proportional_values(): void
    {
        // Arrange
        $ceilingSale = (object) [
            'id' => 1,
            'number_of_units' => 150,
            'number_of_values' => 7500.00,
            'number_of_bonus' => 15,
            'distributor_id' => 1,
            'date' => '2025-01-15',
            'sale_ids' => '1,2,3'
        ];

        $originalSale = new \stdClass();
        $originalSale->region = 5;

        $limitQuantity = 100;
        $expectedPercentage = 100 / 150; // 2/3
        $expectedValue = 7500.00 * $expectedPercentage; // 5000.00
        $expectedBonus = 15 * $expectedPercentage; // 10

        $expectedSaleData = [
            'quantity' => $limitQuantity,
            'value' => $expectedValue,
            'bonus' => $expectedBonus,
            'region' => 5,
            'distributor_id' => 1,
            'product_id' => 1,
            'date' => '2025-01-15',
            'ceiling' => Ceiling::BELOW,
            'sale_ids' => '1,2,3'
        ];

        $mockSale = Mockery::mock(Sale::class);
        $mockSale->shouldReceive('getAttribute')
            ->with('id')
            ->andReturn(456);
        $mockSale->shouldReceive('setAttribute')
            ->with('id', 456)
            ->andReturnSelf();

        $this->saleRepository
            ->shouldReceive('create')
            ->once()
            ->with($expectedSaleData)
            ->andReturn($mockSale);

        // Act
        $result = $this->saleCreator->createLimitedSale($ceilingSale, $originalSale, $limitQuantity);

        // Assert
        $this->assertSame($mockSale, $result);
    }

    /**
     * Test that total values are preserved when creating limited and excess sales
     */
    public function test_total_values_preserved_in_limited_and_excess_sales(): void
    {
        // Arrange
        $ceilingSale = (object) [
            'id' => 1,
            'number_of_units' => 150,
            'number_of_values' => 7500.00,
            'number_of_bonus' => 15,
            'distributor_id' => 1,
            'date' => '2025-01-15',
            'sale_ids' => '1,2,3'
        ];

        $originalSale = new \stdClass();
        $originalSale->region = 5;

        $limitQuantity = 100;
        $excessQuantity = 50;

        // Calculate expected values
        $limitPercentage = 100 / 150; // 2/3
        $excessPercentage = 50 / 150; // 1/3

        $limitedValue = 7500.00 * $limitPercentage; // 5000.00
        $excessValue = 7500.00 * $excessPercentage; // 2500.00

        $limitedBonus = 15 * $limitPercentage; // 10
        $excessBonus = 15 * $excessPercentage; // 5

        // Mock repository calls - expect two separate calls
        $mockSale1 = Mockery::mock(Sale::class);
        $mockSale1->shouldReceive('getAttribute')
            ->with('id')
            ->andReturn(789);
        $mockSale1->shouldReceive('setAttribute')
            ->with('id', 789)
            ->andReturnSelf();

        $mockSale2 = Mockery::mock(Sale::class);
        $mockSale2->shouldReceive('getAttribute')
            ->with('id')
            ->andReturn(790);
        $mockSale2->shouldReceive('setAttribute')
            ->with('id', 790)
            ->andReturnSelf();

        $this->saleRepository
            ->shouldReceive('create')
            ->twice()
            ->andReturn($mockSale1, $mockSale2);

        // Act
        $this->saleCreator->createLimitedSale($ceilingSale, $originalSale, $limitQuantity);
        $this->saleCreator->createExcessSale($ceilingSale, $excessQuantity);

        // Assert - verify that the sum of limited and excess equals original
        $this->assertEqualsWithDelta(7500.00, $limitedValue + $excessValue, 0.01, 'Total value should be preserved');
        $this->assertEqualsWithDelta(15, $limitedBonus + $excessBonus, 0.01, 'Total bonus should be preserved');
    }
}
