<?php

namespace Tests\Unit\Services\Sales\Ceiling\Strategies\Distribution\Algorithms;

use App\Sale;
use App\Services\Sales\Ceiling\Strategies\Distribution\Algorithms\SimpleDistributionAlgorithm;
use App\Services\Sales\Ceiling\Strategies\Distribution\Services\SaleDetailFactory;
use App\Services\SalesDistributionService;
use Illuminate\Support\Collection;
use Tests\TestCase;
use Mockery;

/**
 * Test class for SimpleDistributionAlgorithm
 *
 * Tests the simple distribution algorithm that distributes 100% of excess sales
 * using normal distribution ratios (used by PrivatePharmacyStrategy)
 *
 * @covers \App\Services\Sales\Ceiling\Strategies\Distribution\Algorithms\SimpleDistributionAlgorithm
 */
class SimpleDistributionAlgorithmTest extends TestCase
{
    private SimpleDistributionAlgorithm $algorithm;
    private SalesDistributionService $salesService;
    private SaleDetailFactory $saleDetailFactory;

    protected function setUp(): void
    {
        parent::setUp();

        $this->salesService = Mockery::mock(SalesDistributionService::class);
        $this->saleDetailFactory = Mockery::mock(SaleDetailFactory::class);

        $this->algorithm = new SimpleDistributionAlgorithm(
            $this->salesService,
            $this->saleDetailFactory
        );
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    /**
     * Test successful distribution of excess sale
     */
    public function test_distribute_excess_sale_success(): void
    {
        // Arrange
        $sale = $this->createMockSale();
        $salesContributionBaseOn = [1, 2, 3];
        $originalSale = null;

        $distributionRatios = collect([
            (object) ['div_id' => 1, 'line_id' => 1, 'brick_id' => 1, 'percentage' => 0.6],
            (object) ['div_id' => 2, 'line_id' => 2, 'brick_id' => 2, 'percentage' => 0.4],
        ]);

        $expectedDetails = [
            ['sale_id' => 1, 'div_id' => 1, 'quantity' => 60],
            ['sale_id' => 1, 'div_id' => 2, 'quantity' => 40],
        ];

        // Mock SalesService
        $this->salesService
            ->shouldReceive('getRatiosForDistribution')
            ->once()
            ->with($sale->date, $sale->product_id, $salesContributionBaseOn)
            ->andReturn($distributionRatios);

        // Mock SaleDetailFactory
        $this->saleDetailFactory
            ->shouldReceive('createDetailsFromRatios')
            ->once()
            ->with($sale, $distributionRatios->toArray())
            ->andReturn($expectedDetails);

        $this->saleDetailFactory
            ->shouldReceive('insertDetails')
            ->once()
            ->with($expectedDetails)
            ->andReturn(true);

        // Act
        $result = $this->algorithm->distributeExcessSale($sale, $salesContributionBaseOn, $originalSale);

        // Assert
        $this->assertTrue($result);
    }

    /**
     * Test distribution failure when insertion fails
     */
    public function test_distribute_excess_sale_insertion_failure(): void
    {
        // Arrange
        $sale = $this->createMockSale();
        $salesContributionBaseOn = [1, 2, 3];
        $originalSale = null;

        $distributionRatios = collect([
            (object) ['div_id' => 1, 'line_id' => 1, 'brick_id' => 1, 'percentage' => 1.0],
        ]);

        $expectedDetails = [
            ['sale_id' => 1, 'div_id' => 1, 'quantity' => 100],
        ];

        // Mock SalesService
        $this->salesService
            ->shouldReceive('getRatiosForDistribution')
            ->once()
            ->with($sale->date, $sale->product_id, $salesContributionBaseOn)
            ->andReturn($distributionRatios);

        // Mock SaleDetailFactory
        $this->saleDetailFactory
            ->shouldReceive('createDetailsFromRatios')
            ->once()
            ->with($sale, $distributionRatios->toArray())
            ->andReturn($expectedDetails);

        $this->saleDetailFactory
            ->shouldReceive('insertDetails')
            ->once()
            ->with($expectedDetails)
            ->andReturn(false);

        // Act
        $result = $this->algorithm->distributeExcessSale($sale, $salesContributionBaseOn, $originalSale);

        // Assert
        $this->assertFalse($result);
    }

    /**
     * Test distribution with empty ratios should fail
     *
     * This test verifies that when no distribution ratios are found,
     * the distribution fails properly to prevent orphaned sales without details.
     */
    public function test_distribute_excess_sale_with_empty_ratios(): void
    {
        // Arrange
        $sale = $this->createMockSale();
        $salesContributionBaseOn = [1, 2, 3];
        $originalSale = null;

        $distributionRatios = collect([]);
        $expectedDetails = [];

        // Mock SalesService
        $this->salesService
            ->shouldReceive('getRatiosForDistribution')
            ->once()
            ->with($sale->date, $sale->product_id, $salesContributionBaseOn)
            ->andReturn($distributionRatios);

        // Mock SaleDetailFactory
        $this->saleDetailFactory
            ->shouldReceive('createDetailsFromRatios')
            ->once()
            ->with($sale, $distributionRatios->toArray())
            ->andReturn($expectedDetails);

        // insertDetails should NOT be called when details are empty
        // The algorithm validates and returns false before calling insertDetails
        $this->saleDetailFactory
            ->shouldNotReceive('insertDetails');

        // Act
        $result = $this->algorithm->distributeExcessSale($sale, $salesContributionBaseOn, $originalSale);

        // Assert - Should return false to indicate distribution failure
        $this->assertFalse($result);
    }

    /**
     * Test distribution with original sale parameter (should be ignored)
     */
    public function test_distribute_excess_sale_ignores_original_sale(): void
    {
        // Arrange
        $sale = $this->createMockSale();
        $originalSale = $this->createMockSale();
        $salesContributionBaseOn = [1, 2, 3];

        $distributionRatios = collect([
            (object) ['div_id' => 1, 'line_id' => 1, 'brick_id' => 1, 'percentage' => 1.0],
        ]);

        $expectedDetails = [
            ['sale_id' => 1, 'div_id' => 1, 'quantity' => 100],
        ];

        // Mock SalesService - should still use the same parameters regardless of originalSale
        $this->salesService
            ->shouldReceive('getRatiosForDistribution')
            ->once()
            ->with($sale->date, $sale->product_id, $salesContributionBaseOn)
            ->andReturn($distributionRatios);

        // Mock SaleDetailFactory
        $this->saleDetailFactory
            ->shouldReceive('createDetailsFromRatios')
            ->once()
            ->with($sale, $distributionRatios->toArray())
            ->andReturn($expectedDetails);

        $this->saleDetailFactory
            ->shouldReceive('insertDetails')
            ->once()
            ->with($expectedDetails)
            ->andReturn(true);

        // Act
        $result = $this->algorithm->distributeExcessSale($sale, $salesContributionBaseOn, $originalSale);

        // Assert
        $this->assertTrue($result);
    }

    /**
     * Test that algorithm implements ExcessDistributorInterface
     */
    public function test_implements_excess_distributor_interface(): void
    {
        $this->assertInstanceOf(
            \App\Services\Sales\Ceiling\Strategies\Distribution\Contracts\ExcessDistributorInterface::class,
            $this->algorithm
        );
    }

    /**
     * Create a mock Sale object for testing
     */
    private function createMockSale(): Sale
    {
        $sale = Mockery::mock(Sale::class);
        $sale->shouldReceive('setAttribute')->andReturnSelf();
        $sale->shouldReceive('getAttribute')->andReturnUsing(function ($key) {
            return match ($key) {
                'id' => 1,
                'date' => '2023-01-01',
                'product_id' => 1,
                'quantity' => 100,
                'value' => 1000,
                'bonus' => 50,
                default => null,
            };
        });

        $sale->id = 1;
        $sale->date = '2023-01-01';
        $sale->product_id = 1;
        $sale->quantity = 100;
        $sale->value = 1000;
        $sale->bonus = 50;

        return $sale;
    }
}
