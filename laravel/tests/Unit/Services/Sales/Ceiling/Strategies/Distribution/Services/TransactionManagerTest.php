<?php

namespace Tests\Unit\Services\Sales\Ceiling\Strategies\Distribution\Services;

use App\Services\Sales\Ceiling\Strategies\Distribution\Services\TransactionManager;
use App\Services\Sales\Ceiling\Strategies\Distribution\Contracts\TransactionManagerInterface;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Tests\TestCase;
use Mockery;

/**
 * Test class for TransactionManager
 *
 * Tests the transaction manager service that handles database transactions
 * with proper error handling and logging
 *
 * @covers \App\Services\Sales\Ceiling\Strategies\Distribution\Services\TransactionManager
 */
class TransactionManagerTest extends TestCase
{
    private TransactionManager $transactionManager;

    protected function setUp(): void
    {
        parent::setUp();
        $this->transactionManager = new TransactionManager();
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    /**
     * Test begin transaction success
     */
    public function test_begin_transaction_success(): void
    {
        // Arrange
        DB::shouldReceive('beginTransaction')->once();

        // Act
        $result = $this->transactionManager->beginTransaction();

        // Assert
        $this->assertTrue($result);
    }

    /**
     * Test begin transaction failure
     */
    public function test_begin_transaction_failure(): void
    {
        // Arrange
        DB::shouldReceive('beginTransaction')->once()->andThrow(new \Exception('DB Error'));
        Log::shouldReceive('error')->once()->with('Failed to begin transaction', [
            'error' => 'DB Error'
        ]);

        // Act
        $result = $this->transactionManager->beginTransaction();

        // Assert
        $this->assertFalse($result);
    }

    /**
     * Test commit transaction success
     */
    public function test_commit_transaction_success(): void
    {
        // Arrange
        DB::shouldReceive('commit')->once();

        // Act
        $result = $this->transactionManager->commitTransaction();

        // Assert
        $this->assertTrue($result);
    }

    /**
     * Test commit transaction failure
     */
    public function test_commit_transaction_failure(): void
    {
        // Arrange
        DB::shouldReceive('commit')->once()->andThrow(new \Exception('Commit Error'));
        Log::shouldReceive('error')->once()->with('Failed to commit transaction', [
            'error' => 'Commit Error'
        ]);

        // Act
        $result = $this->transactionManager->commitTransaction();

        // Assert
        $this->assertFalse($result);
    }

    /**
     * Test rollback transaction success
     */
    public function test_rollback_transaction_success(): void
    {
        // Arrange
        DB::shouldReceive('rollBack')->once();

        // Act
        $result = $this->transactionManager->rollbackTransaction();

        // Assert
        $this->assertTrue($result);
    }

    /**
     * Test rollback transaction failure
     */
    public function test_rollback_transaction_failure(): void
    {
        // Arrange
        DB::shouldReceive('rollBack')->once()->andThrow(new \Exception('Rollback Error'));
        Log::shouldReceive('error')->once()->with('Failed to rollback transaction', [
            'error' => 'Rollback Error'
        ]);

        // Act
        $result = $this->transactionManager->rollbackTransaction();

        // Assert
        $this->assertFalse($result);
    }

    /**
     * Test execute in transaction success
     */
    public function test_execute_in_transaction_success(): void
    {
        // Arrange
        DB::shouldReceive('beginTransaction')->once();
        DB::shouldReceive('commit')->once();

        $callback = function () {
            return 'success';
        };

        // Act
        $result = $this->transactionManager->executeInTransaction($callback);

        // Assert
        $this->assertEquals('success', $result);
    }

    /**
     * Test execute in transaction with callback failure and rollback
     */
    public function test_execute_in_transaction_failure_with_rollback(): void
    {
        // Arrange
        DB::shouldReceive('beginTransaction')->once();
        DB::shouldReceive('rollBack')->once();

        $callback = function () {
            throw new \Exception('Callback error');
        };

        // Act & Assert
        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Callback error');

        $this->transactionManager->executeInTransaction($callback);
    }

    /**
     * Test execute in transaction with begin failure
     */
    public function test_execute_in_transaction_begin_failure(): void
    {
        // Arrange
        DB::shouldReceive('beginTransaction')->once()->andThrow(new \Exception('Begin error'));
        Log::shouldReceive('error')->once();

        $callback = function () {
            return 'success';
        };

        // Act & Assert
        $this->expectException(\RuntimeException::class);
        $this->expectExceptionMessage('Failed to begin transaction');

        $this->transactionManager->executeInTransaction($callback);
    }

    /**
     * Test execute in transaction with commit failure
     */
    public function test_execute_in_transaction_commit_failure(): void
    {
        // Arrange
        DB::shouldReceive('beginTransaction')->once();
        DB::shouldReceive('commit')->once()->andThrow(new \Exception('Commit error'));
        // Note: No rollback should happen when commit fails - transaction state is uncertain
        Log::shouldReceive('error')->once()->with('Failed to commit transaction', [
            'error' => 'Commit error'
        ]);

        $callback = function () {
            return 'success';
        };

        // Act & Assert
        $this->expectException(\RuntimeException::class);
        $this->expectExceptionMessage('Failed to commit transaction');

        $this->transactionManager->executeInTransaction($callback);
    }

    /**
     * Test execute in transaction with callback returning null
     */
    public function test_execute_in_transaction_callback_returns_null(): void
    {
        // Arrange
        DB::shouldReceive('beginTransaction')->once();
        DB::shouldReceive('commit')->once();

        $callback = function () {
            return null;
        };

        // Act
        $result = $this->transactionManager->executeInTransaction($callback);

        // Assert
        $this->assertNull($result);
    }

    /**
     * Test execute in transaction with callback returning array
     */
    public function test_execute_in_transaction_callback_returns_array(): void
    {
        // Arrange
        DB::shouldReceive('beginTransaction')->once();
        DB::shouldReceive('commit')->once();

        $expectedResult = ['key' => 'value', 'count' => 5];
        $callback = function () use ($expectedResult) {
            return $expectedResult;
        };

        // Act
        $result = $this->transactionManager->executeInTransaction($callback);

        // Assert
        $this->assertEquals($expectedResult, $result);
    }

    /**
     * Test execute in transaction with callback returning object
     */
    public function test_execute_in_transaction_callback_returns_object(): void
    {
        // Arrange
        DB::shouldReceive('beginTransaction')->once();
        DB::shouldReceive('commit')->once();

        $expectedResult = (object) ['property' => 'value'];
        $callback = function () use ($expectedResult) {
            return $expectedResult;
        };

        // Act
        $result = $this->transactionManager->executeInTransaction($callback);

        // Assert
        $this->assertEquals($expectedResult, $result);
    }

    /**
     * Test execute in transaction with nested exception handling
     */
    public function test_execute_in_transaction_nested_exception(): void
    {
        // Arrange
        DB::shouldReceive('beginTransaction')->once();
        DB::shouldReceive('rollBack')->once();

        $callback = function () {
            throw new \InvalidArgumentException('Invalid argument provided');
        };

        // Act & Assert
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Invalid argument provided');

        $this->transactionManager->executeInTransaction($callback);
    }

    /**
     * Test that the class implements TransactionManagerInterface
     */
    public function test_implements_transaction_manager_interface(): void
    {
        $this->assertInstanceOf(TransactionManagerInterface::class, $this->transactionManager);
    }

    /**
     * Test interface method signatures are correctly implemented
     */
    public function test_interface_methods_exist(): void
    {
        $this->assertTrue(method_exists($this->transactionManager, 'beginTransaction'));
        $this->assertTrue(method_exists($this->transactionManager, 'commitTransaction'));
        $this->assertTrue(method_exists($this->transactionManager, 'rollbackTransaction'));
        $this->assertTrue(method_exists($this->transactionManager, 'executeInTransaction'));
    }

    /**
     * Test transaction sequence: begin -> commit
     */
    public function test_transaction_sequence_begin_commit(): void
    {
        // Arrange
        DB::shouldReceive('beginTransaction')->once()->ordered();
        DB::shouldReceive('commit')->once()->ordered();

        // Act
        $beginResult = $this->transactionManager->beginTransaction();
        $commitResult = $this->transactionManager->commitTransaction();

        // Assert
        $this->assertTrue($beginResult);
        $this->assertTrue($commitResult);
    }

    /**
     * Test transaction sequence: begin -> rollback
     */
    public function test_transaction_sequence_begin_rollback(): void
    {
        // Arrange
        DB::shouldReceive('beginTransaction')->once()->ordered();
        DB::shouldReceive('rollBack')->once()->ordered();

        // Act
        $beginResult = $this->transactionManager->beginTransaction();
        $rollbackResult = $this->transactionManager->rollbackTransaction();

        // Assert
        $this->assertTrue($beginResult);
        $this->assertTrue($rollbackResult);
    }

    /**
     * Test execute in transaction with commit failure and rollback failure
     * This tests the edge case where both commit and rollback fail
     */
    public function test_execute_in_transaction_commit_failure_no_rollback_attempted(): void
    {
        // Arrange
        DB::shouldReceive('beginTransaction')->once();
        DB::shouldReceive('commit')->once()->andThrow(new \Exception('Commit error'));
        // Note: No rollback mock needed - rollback should not be called when commit fails
        Log::shouldReceive('error')->once()->with('Failed to commit transaction', [
            'error' => 'Commit error'
        ]);

        $callback = function () {
            return 'callback_result';
        };

        // Act & Assert
        $this->expectException(\RuntimeException::class);
        $this->expectExceptionMessage('Failed to commit transaction');

        $this->transactionManager->executeInTransaction($callback);
    }

    /**
     * Test execute in transaction with callback exception and rollback failure
     */
    public function test_execute_in_transaction_callback_failure_with_rollback_failure(): void
    {
        // Arrange
        DB::shouldReceive('beginTransaction')->once();
        DB::shouldReceive('rollBack')->once()->andThrow(new \Exception('Rollback error'));
        Log::shouldReceive('error')->once()->with('Failed to rollback transaction', [
            'error' => 'Rollback error'
        ]);

        $callback = function () {
            throw new \InvalidArgumentException('Callback failed');
        };

        // Act & Assert - Original callback exception should be thrown, not rollback exception
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Callback failed');

        $this->transactionManager->executeInTransaction($callback);
    }

    /**
     * Test that transaction manager handles database connection exceptions properly
     */
    public function test_execute_in_transaction_with_database_connection_error(): void
    {
        // Arrange
        DB::shouldReceive('beginTransaction')->once()->andThrow(new \PDOException('Connection lost'));
        Log::shouldReceive('error')->once()->with('Failed to begin transaction', [
            'error' => 'Connection lost'
        ]);

        $callback = function () {
            return 'success';
        };

        // Act & Assert
        $this->expectException(\RuntimeException::class);
        $this->expectExceptionMessage('Failed to begin transaction');

        $this->transactionManager->executeInTransaction($callback);
    }

    /**
     * Test execute in transaction with complex return types
     */
    public function test_execute_in_transaction_with_complex_return_types(): void
    {
        // Arrange
        DB::shouldReceive('beginTransaction')->once();
        DB::shouldReceive('commit')->once();

        $complexObject = new \stdClass();
        $complexObject->data = ['nested' => ['array' => true]];
        $complexObject->count = 42;

        $callback = function () use ($complexObject) {
            return $complexObject;
        };

        // Act
        $result = $this->transactionManager->executeInTransaction($callback);

        // Assert
        $this->assertEquals($complexObject, $result);
        $this->assertEquals(['nested' => ['array' => true]], $result->data);
        $this->assertEquals(42, $result->count);
    }
}
