<?php

namespace Tests\Unit\Services\Sales\Ceiling\Strategies\Distribution;

use App\Services\Sales\Ceiling\Strategies\Distribution\{
    DistributionStrategyFactory,
    DistributionType,
    DistributionStrategy,
    PrivatePharmacyStrategy,
    StoreStrategy,
    LocalChainStrategy
};
use Illuminate\Container\Container;
use Illuminate\Support\Facades\Log;
use Tests\TestCase;
use Mockery;
use InvalidArgumentException;

/**
 * Test class for DistributionStrategyFactory
 *
 * Tests the factory pattern implementation for creating distribution strategy instances
 * with proper dependency injection and error handling
 *
 * @covers \App\Services\Sales\Ceiling\Strategies\Distribution\DistributionStrategyFactory
 */
class DistributionStrategyFactoryTest extends TestCase
{
    private DistributionStrategyFactory $factory;
    private Container $container;

    protected function setUp(): void
    {
        parent::setUp();

        $this->container = Mockery::mock(Container::class);
        $this->factory = new DistributionStrategyFactory($this->container);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    /**
     * Test create private pharmacy strategy success
     */
    public function test_create_private_pharmacy_strategy_success(): void
    {
        // Arrange
        $strategy = Mockery::mock(PrivatePharmacyStrategy::class);

        $this->container
            ->shouldReceive('make')
            ->once()
            ->with(PrivatePharmacyStrategy::class)
            ->andReturn($strategy);

        // Act
        $result = $this->factory->create(DistributionType::PRIVATE_PHARMACY);

        // Assert
        $this->assertInstanceOf(PrivatePharmacyStrategy::class, $result);
        $this->assertEquals($strategy, $result);
    }

    /**
     * Test create store strategy success
     */
    public function test_create_store_strategy_success(): void
    {
        // Arrange
        $strategy = Mockery::mock(StoreStrategy::class);

        $this->container
            ->shouldReceive('make')
            ->once()
            ->with(StoreStrategy::class)
            ->andReturn($strategy);

        // Act
        $result = $this->factory->create(DistributionType::STORES);

        // Assert
        $this->assertInstanceOf(StoreStrategy::class, $result);
        $this->assertEquals($strategy, $result);
    }

    /**
     * Test create local chain strategy success
     */
    public function test_create_local_chain_strategy_success(): void
    {
        // Arrange
        $strategy = Mockery::mock(LocalChainStrategy::class);

        $this->container
            ->shouldReceive('make')
            ->once()
            ->with(LocalChainStrategy::class)
            ->andReturn($strategy);

        // Act
        $result = $this->factory->create(DistributionType::LOCAL_CHAINS);

        // Assert
        $this->assertInstanceOf(LocalChainStrategy::class, $result);
        $this->assertEquals($strategy, $result);
    }

    /**
     * Test create with unknown distribution type
     */
    public function test_create_with_unknown_distribution_type(): void
    {
        // Arrange & Act & Assert
        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage('Unknown distribution type: Unknown');

        $this->factory->create(DistributionType::UNKNOWN);
    }

    /**
     * Test create with container resolution failure
     */
    public function test_create_with_container_resolution_failure(): void
    {
        // Arrange
        $this->container
            ->shouldReceive('make')
            ->once()
            ->with(PrivatePharmacyStrategy::class)
            ->andThrow(new \Exception('Container resolution failed'));

        Log::shouldReceive('error')
            ->once()
            ->with('Failed to create distribution strategy', [
                'error' => 'Container resolution failed'
            ]);

        // Act & Assert
        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Container resolution failed');

        $this->factory->create(DistributionType::PRIVATE_PHARMACY);
    }

    /**
     * Test supports method with valid types
     */
    public function test_supports_with_valid_types(): void
    {
        // Act & Assert
        $this->assertTrue($this->factory->supports(DistributionType::PRIVATE_PHARMACY));
        $this->assertTrue($this->factory->supports(DistributionType::STORES));
        $this->assertTrue($this->factory->supports(DistributionType::LOCAL_CHAINS));
    }

    /**
     * Test supports method with invalid type
     */
    public function test_supports_with_invalid_type(): void
    {
        // Act & Assert
        $this->assertFalse($this->factory->supports(DistributionType::UNKNOWN));
    }

    /**
     * Test register strategy with valid strategy class
     */
    public function test_register_strategy_with_valid_class(): void
    {
        // Arrange
        $customType = DistributionType::fromValue(999);
        $customStrategy = Mockery::mock(DistributionStrategy::class);
        $customStrategyClass = get_class($customStrategy);

        // Act
        $this->factory->registerStrategy($customType, $customStrategyClass);

        // Assert
        $this->assertTrue($this->factory->supports($customType));
        $this->assertContains($customType->value, $this->factory->getAvailableTypes());
    }

    /**
     * Test register strategy with invalid strategy class
     */
    public function test_register_strategy_with_invalid_class(): void
    {
        // Arrange
        $customType = DistributionType::fromValue(999);
        $invalidClass = \stdClass::class;

        // Act & Assert
        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage('Strategy class must implement DistributionStrategy interface');

        $this->factory->registerStrategy($customType, $invalidClass);
    }

    /**
     * Test get available types
     */
    public function test_get_available_types(): void
    {
        // Act
        $availableTypes = $this->factory->getAvailableTypes();

        // Assert
        $this->assertIsArray($availableTypes);
        $this->assertContains(DistributionType::PRIVATE_PHARMACY->value, $availableTypes);
        $this->assertContains(DistributionType::STORES->value, $availableTypes);
        $this->assertContains(DistributionType::LOCAL_CHAINS->value, $availableTypes);
        $this->assertCount(3, $availableTypes);
    }

    /**
     * Test get strategy information
     */
    public function test_get_strategy_information(): void
    {
        // Act
        $strategyInfo = $this->factory->getStrategyInformation();

        // Assert
        $this->assertIsArray($strategyInfo);
        $this->assertArrayHasKey(DistributionType::PRIVATE_PHARMACY->value, $strategyInfo);
        $this->assertArrayHasKey(DistributionType::STORES->value, $strategyInfo);
        $this->assertArrayHasKey(DistributionType::LOCAL_CHAINS->value, $strategyInfo);

        // Test Private Pharmacy info
        $privatePharmacyInfo = $strategyInfo[DistributionType::PRIVATE_PHARMACY->value];
        $this->assertEquals('Private Pharmacy Strategy', $privatePharmacyInfo['name']);
        $this->assertEquals('Simple Distribution (100%)', $privatePharmacyInfo['algorithm']);
        $this->assertEquals(PrivatePharmacyStrategy::class, $privatePharmacyInfo['class']);

        // Test Store info
        $storeInfo = $strategyInfo[DistributionType::STORES->value];
        $this->assertEquals('Store Strategy', $storeInfo['name']);
        $this->assertEquals('Full Quantity Split Distribution (90/10)', $storeInfo['algorithm']);
        $this->assertEquals(StoreStrategy::class, $storeInfo['class']);

        // Test Local Chain info
        $localChainInfo = $strategyInfo[DistributionType::LOCAL_CHAINS->value];
        $this->assertEquals('Local Chain Strategy', $localChainInfo['name']);
        $this->assertEquals('Hierarchical Chain Distribution (60/25/15)', $localChainInfo['algorithm']);
        $this->assertEquals(LocalChainStrategy::class, $localChainInfo['class']);
    }

    /**
     * Test factory with multiple strategy creations
     */
    public function test_multiple_strategy_creations(): void
    {
        // Arrange
        $privatePharmacyStrategy = Mockery::mock(PrivatePharmacyStrategy::class);
        $storeStrategy = Mockery::mock(StoreStrategy::class);

        $this->container
            ->shouldReceive('make')
            ->with(PrivatePharmacyStrategy::class)
            ->once()
            ->andReturn($privatePharmacyStrategy);

        $this->container
            ->shouldReceive('make')
            ->with(StoreStrategy::class)
            ->once()
            ->andReturn($storeStrategy);

        // Act
        $result1 = $this->factory->create(DistributionType::PRIVATE_PHARMACY);
        $result2 = $this->factory->create(DistributionType::STORES);

        // Assert
        $this->assertInstanceOf(PrivatePharmacyStrategy::class, $result1);
        $this->assertInstanceOf(StoreStrategy::class, $result2);
        $this->assertNotEquals($result1, $result2);
    }

    /**
     * Test factory initialization with strategy map
     */
    public function test_factory_initialization(): void
    {
        // Act
        $availableTypes = $this->factory->getAvailableTypes();

        // Assert - Verify all expected strategies are registered
        $expectedTypes = [
            DistributionType::PRIVATE_PHARMACY->value,
            DistributionType::STORES->value,
            DistributionType::LOCAL_CHAINS->value
        ];

        foreach ($expectedTypes as $expectedType) {
            $this->assertContains($expectedType, $availableTypes);
        }
    }

    /**
     * Test supports method with edge cases
     */
    public function test_supports_edge_cases(): void
    {
        // Test with various distribution types
        $testCases = [
            [DistributionType::PRIVATE_PHARMACY, true],
            [DistributionType::STORES, true],
            [DistributionType::LOCAL_CHAINS, true],
            [DistributionType::UNKNOWN, false],
            [DistributionType::fromValue(999), false],
        ];

        foreach ($testCases as [$type, $expected]) {
            $this->assertEquals($expected, $this->factory->supports($type));
        }
    }

    /**
     * Test that factory properly handles dependency injection
     */
    public function test_dependency_injection_handling(): void
    {
        // Arrange
        $strategy = Mockery::mock(PrivatePharmacyStrategy::class);

        $this->container
            ->shouldReceive('make')
            ->once()
            ->with(PrivatePharmacyStrategy::class)
            ->andReturn($strategy);

        // Act
        $result = $this->factory->create(DistributionType::PRIVATE_PHARMACY);

        // Assert
        $this->assertSame($strategy, $result);
    }
}
