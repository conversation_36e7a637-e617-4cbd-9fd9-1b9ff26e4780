<?php

namespace Tests\Unit\Services\Sales\Ceiling\Strategies\Distribution\Services;

use App\Sale;
use App\Services\Sales\Ceiling\Strategies\Distribution\Services\SaleCreator;
use App\Services\Sales\Ceiling\Strategies\Distribution\Contracts\SaleRepositoryInterface;
use App\Services\Enums\Ceiling;
use Tests\TestCase;
use Mockery;

/**
 * Test class for SaleCreator
 *
 * Tests the sale creation service that creates limited sales, excess sales,
 * and manages sale relationships and mappings
 *
 * @covers \App\Services\Sales\Ceiling\Strategies\Distribution\Services\SaleCreator
 */
class SaleCreatorTest extends TestCase
{
    private SaleCreator $saleCreator;
    private $mockSaleRepository;

    protected function setUp(): void
    {
        parent::setUp();
        $this->mockSaleRepository = Mockery::mock(SaleRepositoryInterface::class);
        $this->saleCreator = new SaleCreator($this->mockSaleRepository);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    /**
     * Test create limited sale with normal quantities
     */
    public function test_create_limited_sale_success(): void
    {
        // Arrange
        $ceilingSale = (object) [
            'distributor_id' => 1,
            'id' => 100,
            'date' => '2023-01-01',
            'sale_ids' => '1,2,3',
            'number_of_units' => 100,
            'number_of_values' => 5000.0,
            'number_of_bonus' => 50
        ];

        $originalSale = $this->createMockOriginalSale();
        $limitQuantity = 80.0;

        // Mock Sale::create
        $expectedSaleData = [
            'quantity' => 80.0,
            'value' => 4000.0, // 5000 * 0.8 (using ceiling sale values)
            'bonus' => 40.0,  // 50 * 0.8
            'region' => 5,
            'distributor_id' => 1,
            'product_id' => 100,
            'date' => '2023-01-01',
            'ceiling' => Ceiling::BELOW,
            'sale_ids' => '1,2,3'
        ];

        $createdSale = $this->createMockSale();
        $this->mockSaleRepository->shouldReceive('create')
            ->once()
            ->with($expectedSaleData)
            ->andReturn($createdSale);

        // Act
        $result = $this->saleCreator->createLimitedSale($ceilingSale, $originalSale, $limitQuantity);

        // Assert
        $this->assertInstanceOf(Sale::class, $result);
        $this->assertEquals($createdSale, $result);
    }

    /**
     * Test create limited sale with zero original quantity
     */
    public function test_create_limited_sale_with_zero_original_quantity(): void
    {
        // Arrange
        $ceilingSale = (object) [
            'distributor_id' => 1,
            'id' => 100,
            'date' => '2023-01-01',
            'sale_ids' => '1,2,3',
            'number_of_units' => 0,
            'number_of_values' => 1000.0,
            'number_of_bonus' => 50
        ];

        $originalSale = $this->createMockOriginalSale();
        $originalSale->quantity = 0; // Zero quantity
        $originalSale->value = 1000; // Keep original value for calculation
        $originalSale->bonus = 50; // Keep original bonus for calculation
        $limitQuantity = 50.0;

        $expectedSaleData = [
            'quantity' => 50.0,
            'value' => 0.0, // 1000 * 0 (percentage is 0)
            'bonus' => 0.0, // 50 * 0 (percentage is 0)
            'region' => 5,
            'distributor_id' => 1,
            'product_id' => 100,
            'date' => '2023-01-01',
            'ceiling' => Ceiling::BELOW,
            'sale_ids' => '1,2,3'
        ];

        $createdSale = $this->createMockSale();
        $this->mockSaleRepository->shouldReceive('create')
            ->once()
            ->with($expectedSaleData)
            ->andReturn($createdSale);

        // Act
        $result = $this->saleCreator->createLimitedSale($ceilingSale, $originalSale, $limitQuantity);

        // Assert
        $this->assertInstanceOf(Sale::class, $result);
    }

    /**
     * Test create excess sale
     */
    public function test_create_excess_sale_success(): void
    {
        // Arrange
        $ceilingSale = (object) [
            'distributor_id' => 1,
            'id' => 100,
            'date' => '2023-01-01',
            'sale_ids' => '1,2,3',
            'number_of_units' => 100,
            'number_of_values' => 2500.0,
            'number_of_bonus' => 25
        ];

        $excessQuantity = 25.0;

        $expectedSaleData = [
            'quantity' => 25.0,
            'value' => 625.0, // 2500 * 0.25 (proportional value)
            'bonus' => 6.25, // 25 * 0.25 (proportional bonus)
            'region' => 0,
            'distributor_id' => 1,
            'product_id' => 100,
            'date' => '2023-01-01',
            'ceiling' => Ceiling::DISTRIBUTED,
            'sale_ids' => '1,2,3'
        ];

        $createdSale = $this->createMockSale();
        $this->mockSaleRepository->shouldReceive('create')
            ->once()
            ->with($expectedSaleData)
            ->andReturn($createdSale);

        // Act
        $result = $this->saleCreator->createExcessSale($ceilingSale, $excessQuantity);

        // Assert
        $this->assertInstanceOf(Sale::class, $result);
        $this->assertEquals($createdSale, $result);
    }

    /**
     * Test update original sales ceiling status success
     */
    public function test_update_original_sales_ceiling_success(): void
    {
        // Arrange
        $ceilingSale = (object) [
            'sale_ids' => '1,2,3'
        ];

        // Mock repository updateByIds method
        $this->mockSaleRepository->shouldReceive('updateByIds')
            ->once()
            ->with(['1', '2', '3'], ['ceiling' => Ceiling::ABOVE])
            ->andReturn(3); // 3 rows affected

        // Act
        $result = $this->saleCreator->updateOriginalSalesCeiling($ceilingSale);

        // Assert
        $this->assertTrue($result);
    }

    /**
     * Test update original sales ceiling status with no affected rows
     */
    public function test_update_original_sales_ceiling_no_rows_affected(): void
    {
        // Arrange
        $ceilingSale = (object) [
            'sale_ids' => '999'
        ];

        // Mock repository updateByIds method
        $this->mockSaleRepository->shouldReceive('updateByIds')
            ->once()
            ->with(['999'], ['ceiling' => Ceiling::ABOVE])
            ->andReturn(0); // 0 rows affected

        // Act
        $result = $this->saleCreator->updateOriginalSalesCeiling($ceilingSale);

        // Assert
        $this->assertFalse($result);
    }

    /**
     * Test update original sales ceiling status to ABOVE success
     */
    public function test_update_original_sales_ceiling_to_above_success(): void
    {
        // Arrange
        $ceilingSale = (object) [
            'sale_ids' => '1,2,3'
        ];

        // Mock repository updateByIds method
        $this->mockSaleRepository->shouldReceive('updateByIds')
            ->once()
            ->with(['1', '2', '3'], ['ceiling' => Ceiling::ABOVE])
            ->andReturn(3); // 3 rows affected

        // Act
        $result = $this->saleCreator->updateOriginalSalesCeiling($ceilingSale);

        // Assert
        $this->assertTrue($result);
    }

    /**
     * Test update original sales ceiling status to ABOVE with no affected rows
     */
    public function test_update_original_sales_ceiling_to_above_no_rows_affected(): void
    {
        // Arrange
        $ceilingSale = (object) [
            'sale_ids' => '999'
        ];

        // Mock repository updateByIds method
        $this->mockSaleRepository->shouldReceive('updateByIds')
            ->once()
            ->with(['999'], ['ceiling' => Ceiling::ABOVE])
            ->andReturn(0); // 0 rows affected

        // Act
        $result = $this->saleCreator->updateOriginalSalesCeiling($ceilingSale);

        // Assert
        $this->assertFalse($result);
    }

    /**
     * Test update original sales ceiling status to ABOVE with single sale ID
     */
    public function test_update_original_sales_ceiling_to_above_single_sale(): void
    {
        // Arrange
        $ceilingSale = (object) [
            'sale_ids' => '4098079'
        ];

        // Mock repository updateByIds method
        $this->mockSaleRepository->shouldReceive('updateByIds')
            ->once()
            ->with(['4098079'], ['ceiling' => Ceiling::ABOVE])
            ->andReturn(1); // 1 row affected

        // Act
        $result = $this->saleCreator->updateOriginalSalesCeiling($ceilingSale);

        // Assert
        $this->assertTrue($result);
    }

    /**
     * Test attach mapping to sale
     */
    public function test_attach_mapping(): void
    {
        // Arrange
        $sale = $this->createMockSale();
        $mappingId = 123;

        $mappingsRelation = Mockery::mock();
        $mappingsRelation->shouldReceive('attach')
            ->once()
            ->with($mappingId);

        $sale->shouldReceive('mappings')
            ->once()
            ->andReturn($mappingsRelation);

        // Act
        $this->saleCreator->attachMapping($sale, $mappingId);

        // Assert - No exception thrown means success
        $this->assertTrue(true);
    }

    /**
     * Test load relationships with default relationships
     */
    public function test_load_relationships_with_defaults(): void
    {
        // Arrange
        $sale = $this->createMockSale();
        $expectedRelationships = ['product:id', 'product.lines:id'];

        $sale->shouldReceive('load')
            ->once()
            ->with($expectedRelationships)
            ->andReturnSelf();

        // Act
        $result = $this->saleCreator->loadRelationships($sale);

        // Assert
        $this->assertEquals($sale, $result);
    }

    /**
     * Test load relationships with custom relationships
     */
    public function test_load_relationships_with_custom_relationships(): void
    {
        // Arrange
        $sale = $this->createMockSale();
        $customRelationships = ['details', 'distributor'];

        $sale->shouldReceive('load')
            ->once()
            ->with($customRelationships)
            ->andReturnSelf();

        // Act
        $result = $this->saleCreator->loadRelationships($sale, $customRelationships);

        // Assert
        $this->assertEquals($sale, $result);
    }

    /**
     * Test create limited sale with negative limit quantity
     */
    public function test_create_limited_sale_with_negative_limit_quantity(): void
    {
        // Arrange
        $ceilingSale = (object) [
            'distributor_id' => 1,
            'id' => 100,
            'date' => '2023-01-01',
            'sale_ids' => '1,2,3',
            'number_of_units' => -100,
            'number_of_values' => -1000.0,
            'number_of_bonus' => -50
        ];

        $originalSale = $this->createMockOriginalSale();
        $limitQuantity = -20.0;

        $expectedSaleData = [
            'quantity' => -20.0,
            'value' => -200.0, // -1000 * 0.2 (using ceiling sale values)
            'bonus' => -10.0,  // -50 * 0.2
            'region' => 5,
            'distributor_id' => 1,
            'product_id' => 100,
            'date' => '2023-01-01',
            'ceiling' => Ceiling::BELOW,
            'sale_ids' => '1,2,3'
        ];

        $createdSale = $this->createMockSale();
        $this->mockSaleRepository->shouldReceive('create')
            ->once()
            ->with($expectedSaleData)
            ->andReturn($createdSale);

        // Act
        $result = $this->saleCreator->createLimitedSale($ceilingSale, $originalSale, $limitQuantity);

        // Assert
        $this->assertInstanceOf(Sale::class, $result);
    }

    /**
     * Test create limited sale with zero limit quantity
     *
     * Verifies that when limitQuantity is 0.0, the system creates a sale with
     * zero quantity, value, and bonus while maintaining proper sale structure
     */
    public function test_create_limited_sale_with_zero_limit_quantity(): void
    {
        // Arrange
        $ceilingSale = (object) [
            'distributor_id' => 1,
            'id' => 100,
            'date' => '2023-01-01',
            'sale_ids' => '1,2,3',
            'number_of_units' => 100,
            'number_of_values' => 5000.0,
            'number_of_bonus' => 50
        ];

        $originalSale = $this->createMockOriginalSale();
        $limitQuantity = 0.0;

        // Expected sale data with zero values due to zero limit quantity
        // Percentage calculation: 0.0 / 100 = 0.0
        $expectedSaleData = [
            'quantity' => 0.0,
            'value' => 0.0, // 5000.0 * 0.0 = 0.0
            'bonus' => 0.0, // 50 * 0.0 = 0.0
            'region' => 5,
            'distributor_id' => 1,
            'product_id' => 100,
            'date' => '2023-01-01',
            'ceiling' => Ceiling::BELOW,
            'sale_ids' => '1,2,3'
        ];

        $createdSale = $this->createMockSale();
        $this->mockSaleRepository->shouldReceive('create')
            ->once()
            ->with($expectedSaleData)
            ->andReturn($createdSale);

        // Act
        $result = $this->saleCreator->createLimitedSale($ceilingSale, $originalSale, $limitQuantity);

        // Assert
        $this->assertInstanceOf(Sale::class, $result);
        $this->assertEquals($createdSale, $result);
    }

    /**
     * Create a mock Sale object for testing
     */
    private function createMockSale(): Sale
    {
        $sale = Mockery::mock(Sale::class);
        $sale->shouldReceive('setAttribute')->andReturnSelf();
        $sale->shouldReceive('getAttribute')->andReturnUsing(function ($key) {
            return match ($key) {
                'id' => 1,
                'quantity' => 100,
                'value' => 1000,
                'bonus' => 50,
                default => null,
            };
        });

        $sale->id = 1;
        $sale->quantity = 100;
        $sale->value = 1000;
        $sale->bonus = 50;

        return $sale;
    }

    /**
     * Create a mock original Sale object for testing
     */
    private function createMockOriginalSale(): Sale
    {
        $sale = new Sale();
        $sale->quantity = 100;
        $sale->value = 1000;
        $sale->bonus = 50;
        $sale->region = 5;

        return $sale;
    }
}
