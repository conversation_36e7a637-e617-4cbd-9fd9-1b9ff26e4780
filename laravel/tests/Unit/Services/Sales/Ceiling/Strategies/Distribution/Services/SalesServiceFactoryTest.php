<?php

namespace Tests\Unit\Services\Sales\Ceiling\Strategies\Distribution\Services;

use App\Services\Enums\SaleDistribution;
use App\Services\Sales\Ceiling\Strategies\Distribution\Contracts\SalesServiceFactoryInterface;
use App\Services\Sales\Ceiling\Strategies\Distribution\DistributionType;
use App\Services\Sales\Ceiling\Strategies\Distribution\Services\SalesServiceFactory;
use App\Services\SalesDistributionService;
use PHPUnit\Framework\TestCase;

/**
 * Test class for SalesServiceFactory
 *
 * Tests the factory pattern implementation for creating SalesService instances
 * with proper configuration and Laravel Octane compatibility.
 *
 * @covers \App\Services\Sales\Ceiling\Strategies\Distribution\Services\SalesServiceFactory
 */
class SalesServiceFactoryTest extends TestCase
{
    private SalesServiceFactory $factory;

    protected function setUp(): void
    {
        $this->factory = new SalesServiceFactory();
    }

    /**
     * Test that factory implements the correct interface
     */
    public function test_factory_implements_interface(): void
    {
        $this->assertInstanceOf(SalesServiceFactoryInterface::class, $this->factory);
    }

    /**
     * Test creating SalesService for normal distribution without distribution type
     */
    public function test_create_for_distribution_normal_without_type(): void
    {
        // Arrange & Act
        $salesService = $this->factory->createForDistribution(SaleDistribution::NORMAL);

        // Assert
        $this->assertInstanceOf(SalesDistributionService::class, $salesService);

        // Use reflection to verify internal state since properties are private
        $reflection = new \ReflectionClass($salesService);

        $saleDistributionProperty = $reflection->getProperty('saleDistribution');
        $saleDistributionProperty->setAccessible(true);
        $this->assertEquals(SaleDistribution::NORMAL, $saleDistributionProperty->getValue($salesService));

        $distributionTypeProperty = $reflection->getProperty('distributionType');
        $distributionTypeProperty->setAccessible(true);
        $this->assertNull($distributionTypeProperty->getValue($salesService));
    }

    /**
     * Test creating SalesService for normal distribution with distribution type
     */
    public function test_create_for_distribution_normal_with_type(): void
    {
        // Arrange & Act
        $salesService = $this->factory->createForDistribution(
            SaleDistribution::NORMAL,
            DistributionType::PRIVATE_PHARMACY
        );

        // Assert
        $this->assertInstanceOf(SalesDistributionService::class, $salesService);

        // Use reflection to verify internal state
        $reflection = new \ReflectionClass($salesService);

        $saleDistributionProperty = $reflection->getProperty('saleDistribution');
        $saleDistributionProperty->setAccessible(true);
        $this->assertEquals(SaleDistribution::NORMAL, $saleDistributionProperty->getValue($salesService));

        $distributionTypeProperty = $reflection->getProperty('distributionType');
        $distributionTypeProperty->setAccessible(true);
        $this->assertEquals(DistributionType::PRIVATE_PHARMACY, $distributionTypeProperty->getValue($salesService));
    }

    /**
     * Test creating SalesService for direct distribution
     */
    public function test_create_for_distribution_direct(): void
    {
        // Arrange & Act
        $salesService = $this->factory->createForDistribution(
            SaleDistribution::DIRECT,
            DistributionType::STORES
        );

        // Assert
        $this->assertInstanceOf(SalesDistributionService::class, $salesService);

        // Use reflection to verify internal state
        $reflection = new \ReflectionClass($salesService);

        $saleDistributionProperty = $reflection->getProperty('saleDistribution');
        $saleDistributionProperty->setAccessible(true);
        $this->assertEquals(SaleDistribution::DIRECT, $saleDistributionProperty->getValue($salesService));

        $distributionTypeProperty = $reflection->getProperty('distributionType');
        $distributionTypeProperty->setAccessible(true);
        $this->assertEquals(DistributionType::STORES, $distributionTypeProperty->getValue($salesService));
    }

    /**
     * Test createNormal convenience method
     */
    public function test_create_normal(): void
    {
        // Arrange & Act
        $salesService = $this->factory->createNormal(DistributionType::LOCAL_CHAINS);

        // Assert
        $this->assertInstanceOf(SalesDistributionService::class, $salesService);

        // Use reflection to verify internal state
        $reflection = new \ReflectionClass($salesService);

        $saleDistributionProperty = $reflection->getProperty('saleDistribution');
        $saleDistributionProperty->setAccessible(true);
        $this->assertEquals(SaleDistribution::NORMAL, $saleDistributionProperty->getValue($salesService));

        $distributionTypeProperty = $reflection->getProperty('distributionType');
        $distributionTypeProperty->setAccessible(true);
        $this->assertEquals(DistributionType::LOCAL_CHAINS, $distributionTypeProperty->getValue($salesService));
    }

    /**
     * Test createDirect convenience method
     */
    public function test_create_direct(): void
    {
        // Arrange & Act
        $salesService = $this->factory->createDirect(DistributionType::PRIVATE_PHARMACY);

        // Assert
        $this->assertInstanceOf(SalesDistributionService::class, $salesService);

        // Use reflection to verify internal state
        $reflection = new \ReflectionClass($salesService);

        $saleDistributionProperty = $reflection->getProperty('saleDistribution');
        $saleDistributionProperty->setAccessible(true);
        $this->assertEquals(SaleDistribution::DIRECT, $saleDistributionProperty->getValue($salesService));

        $distributionTypeProperty = $reflection->getProperty('distributionType');
        $distributionTypeProperty->setAccessible(true);
        $this->assertEquals(DistributionType::PRIVATE_PHARMACY, $distributionTypeProperty->getValue($salesService));
    }

    /**
     * Test that each factory call creates a fresh instance (Octane compatibility)
     */
    public function test_creates_fresh_instances_for_octane_compatibility(): void
    {
        // Arrange & Act
        $service1 = $this->factory->createForDistribution(SaleDistribution::NORMAL);
        $service2 = $this->factory->createForDistribution(SaleDistribution::NORMAL);
        $service3 = $this->factory->createNormal();
        $service4 = $this->factory->createDirect();

        // Assert - Each call should return a different instance
        $this->assertNotSame($service1, $service2);
        $this->assertNotSame($service1, $service3);
        $this->assertNotSame($service1, $service4);
        $this->assertNotSame($service2, $service3);
        $this->assertNotSame($service2, $service4);
        $this->assertNotSame($service3, $service4);

        // Verify they have different object IDs
        $this->assertNotEquals(spl_object_id($service1), spl_object_id($service2));
        $this->assertNotEquals(spl_object_id($service1), spl_object_id($service3));
        $this->assertNotEquals(spl_object_id($service1), spl_object_id($service4));
    }



    /**
     * Test all distribution type combinations
     */
    public function test_all_distribution_type_combinations(): void
    {
        $distributionTypes = [
            DistributionType::PRIVATE_PHARMACY,
            DistributionType::STORES,
            DistributionType::LOCAL_CHAINS
        ];

        $saleDistributions = [
            SaleDistribution::NORMAL,
            SaleDistribution::DIRECT
        ];

        foreach ($saleDistributions as $saleDistribution) {
            foreach ($distributionTypes as $distributionType) {
                // Act
                $salesService = $this->factory->createForDistribution($saleDistribution, $distributionType);

                // Assert
                $this->assertInstanceOf(SalesDistributionService::class, $salesService);

                // Verify configuration
                $reflection = new \ReflectionClass($salesService);

                $saleDistributionProperty = $reflection->getProperty('saleDistribution');
                $saleDistributionProperty->setAccessible(true);
                $this->assertEquals($saleDistribution, $saleDistributionProperty->getValue($salesService));

                $distributionTypeProperty = $reflection->getProperty('distributionType');
                $distributionTypeProperty->setAccessible(true);
                $this->assertEquals($distributionType, $distributionTypeProperty->getValue($salesService));
            }
        }
    }

    /**
     * Test memory usage doesn't accumulate (Octane compatibility)
     */
    public function test_memory_usage_octane_compatibility(): void
    {
        // Arrange
        $initialMemory = memory_get_usage(true);
        $services = [];

        // Act - Create many instances
        for ($i = 0; $i < 50; $i++) {
            $services[] = $this->factory->createForDistribution(
                $i % 2 === 0 ? SaleDistribution::NORMAL : SaleDistribution::DIRECT,
                match($i % 3) {
                    0 => DistributionType::PRIVATE_PHARMACY,
                    1 => DistributionType::STORES,
                    2 => DistributionType::LOCAL_CHAINS,
                }
            );
        }

        // Clear references and force garbage collection
        unset($services);
        gc_collect_cycles();

        $finalMemory = memory_get_usage(true);
        $memoryIncrease = $finalMemory - $initialMemory;

        // Assert - Memory increase should be reasonable (less than 5MB for 50 instances)
        $this->assertLessThan(5 * 1024 * 1024, $memoryIncrease,
            "Memory usage increased by {$memoryIncrease} bytes, which may indicate memory leaks");
    }
}
