<?php

namespace Tests\Unit\Services\Sales\Ceiling\Strategies\Distribution;

use App\Services\Sales\Ceiling\Strategies\Distribution\DistributionType;
use App\Services\Sales\Ceiling\Strategies\Distribution\DistributionStrategyFactory;
use Tests\TestCase;

/**
 * Unit test for Store Strategy to verify the new behavior
 *
 * This test verifies that the Store strategy correctly:
 * 1. Skips limited sale creation
 * 2. Creates only one sale with full quantity
 * 3. Distributes using 90/10 split algorithm
 * 4. Maintains backward compatibility
 */
class StoreStrategyIntegrationTest extends TestCase
{

    private DistributionStrategyFactory $strategyFactory;

    protected function setUp(): void
    {
        parent::setUp();
        $this->strategyFactory = app(DistributionStrategyFactory::class);
    }

    /**
     * Test that Store strategy can be created and has correct information
     */
    public function test_store_strategy_factory_creation(): void
    {
        // Act
        $strategy = $this->strategyFactory->create(DistributionType::STORES);
        $strategyInfo = $this->strategyFactory->getStrategyInformation();

        // Assert
        $this->assertInstanceOf(
            \App\Services\Sales\Ceiling\Strategies\Distribution\StoreStrategy::class,
            $strategy
        );

        $this->assertEquals(
            'Full Quantity Split Distribution (90/10)',
            $strategyInfo[DistributionType::STORES->value]['algorithm']
        );

        $this->assertStringContainsString(
            'skipping limited sale creation',
            $strategyInfo[DistributionType::STORES->value]['description']
        );
    }

    /**
     * Test that other strategies remain unchanged
     */
    public function test_other_strategies_remain_unchanged(): void
    {
        // Act
        $privatePharmacyStrategy = $this->strategyFactory->create(DistributionType::PRIVATE_PHARMACY);
        $localChainStrategy = $this->strategyFactory->create(DistributionType::LOCAL_CHAINS);
        $strategyInfo = $this->strategyFactory->getStrategyInformation();

        // Assert - Private Pharmacy strategy unchanged
        $this->assertInstanceOf(
            \App\Services\Sales\Ceiling\Strategies\Distribution\PrivatePharmacyStrategy::class,
            $privatePharmacyStrategy
        );

        $this->assertEquals(
            'Simple Distribution (100%)',
            $strategyInfo[DistributionType::PRIVATE_PHARMACY->value]['algorithm']
        );

        // Assert - Local Chain strategy unchanged
        $this->assertInstanceOf(
            \App\Services\Sales\Ceiling\Strategies\Distribution\LocalChainStrategy::class,
            $localChainStrategy
        );

        $this->assertEquals(
            'Hierarchical Chain Distribution (60/25/15)',
            $strategyInfo[DistributionType::LOCAL_CHAINS->value]['algorithm']
        );
    }

    /**
     * Test that all distribution types are available
     */
    public function test_all_distribution_types_available(): void
    {
        // Act
        $availableTypes = $this->strategyFactory->getAvailableTypes();

        // Assert
        $this->assertContains(DistributionType::STORES->value, $availableTypes);
        $this->assertContains(DistributionType::PRIVATE_PHARMACY->value, $availableTypes);
        $this->assertContains(DistributionType::LOCAL_CHAINS->value, $availableTypes);
    }

    /**
     * Test Store strategy behavior description
     */
    public function test_store_strategy_behavior_description(): void
    {
        // Act
        $strategyInfo = $this->strategyFactory->getStrategyInformation();
        $storeStrategyInfo = $strategyInfo[DistributionType::STORES->value];

        // Assert
        $this->assertEquals('Store Strategy', $storeStrategyInfo['name']);
        $this->assertEquals('Full Quantity Split Distribution (90/10)', $storeStrategyInfo['algorithm']);
        $this->assertStringContainsString('full original quantity', $storeStrategyInfo['description']);
        $this->assertStringContainsString('90% primary and 10% secondary', $storeStrategyInfo['description']);
        $this->assertStringContainsString('skipping limited sale creation', $storeStrategyInfo['description']);
    }
}
