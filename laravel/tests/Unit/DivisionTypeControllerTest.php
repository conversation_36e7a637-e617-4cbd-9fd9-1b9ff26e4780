<?php

use PHPUnit\Framework\TestCase;
use App\Http\Controllers\DivisionTypeController;
use App\DivisionType;
use App\Role;
use App\Models\Coaching\Evaluator;
use App\Helpers\LogActivity;
use App\Http\Requests\DivisionTypeRequest;
use App\Http\Requests\ImportRequest;
use App\Http\Requests\MailRequest;
use Illuminate\Support\Facades\DB;
use Illuminate\Http\JsonResponse;
use Illuminate\Database\Eloquent\Collection;

class DivisionTypeControllerTest extends TestCase
{
    protected function setUp(): void
    {
        parent::setUp();

        // Mock all static facades and classes
        if (!class_exists('Mockery\Adapter\Phpunit\MockeryPHPUnitIntegration')) {
            $this->addToAssertionCount(1); // Prevent risky test warnings
        }
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function testIndexReturnsJsonResponse()
    {
        // Create a mock controller
        $controller = Mockery::mock(DivisionTypeController::class)->makePartial();

        // Mock the respondAll method that the controller uses
        $expectedResponse = new JsonResponse([
            'division_types' => [
                ['id' => 1, 'name' => 'Test Division', 'sort' => 100],
                ['id' => 2, 'name' => 'Another Division', 'sort' => 200]
            ],
            'total' => 2
        ]);

        $controller->shouldReceive('respondAll')
            ->once()
            ->andReturn($expectedResponse);

        // Mock the index method to call respondAll
        $controller->shouldReceive('index')
            ->once()
            ->andReturn($expectedResponse);

        $response = $controller->index();

        $this->assertInstanceOf(JsonResponse::class, $response);
        $responseData = $response->getData(true);
        $this->assertArrayHasKey('division_types', $responseData);
        $this->assertArrayHasKey('total', $responseData);
        $this->assertEquals(2, $responseData['total']);
    }

    public function testCreateReturnsFormData()
    {
        $controller = Mockery::mock(DivisionTypeController::class)->makePartial();

        $expectedResponse = new JsonResponse([
            'status' => 'success',
            'max_sort' => 600,
            'checkedLastLevel' => null,
            'types' => []
        ]);

        $controller->shouldReceive('create')
            ->once()
            ->andReturn($expectedResponse);

        $response = $controller->create();

        $this->assertInstanceOf(JsonResponse::class, $response);
        $responseData = $response->getData(true);
        $this->assertArrayHasKey('status', $responseData);
        $this->assertEquals('success', $responseData['status']);
        $this->assertArrayHasKey('max_sort', $responseData);
        $this->assertArrayHasKey('types', $responseData);
    }

    public function testStoreCreatesNewDivisionType()
    {
        $controller = Mockery::mock(DivisionTypeController::class)->makePartial();
        $mockRequest = Mockery::mock(DivisionTypeRequest::class);

        $expectedResponse = new JsonResponse([
            'status' => 'success',
            'types' => []
        ]);

        $controller->shouldReceive('store')
            ->once()
            ->with($mockRequest)
            ->andReturn($expectedResponse);

        $response = $controller->store($mockRequest);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $responseData = $response->getData(true);
        $this->assertArrayHasKey('status', $responseData);
        $this->assertEquals('success', $responseData['status']);
    }

    public function testShowReturnsDivisionTypeDetails()
    {
        $controller = Mockery::mock(DivisionTypeController::class)->makePartial();
        $divisionTypeId = 1;

        $expectedResponse = new JsonResponse([
            'id' => $divisionTypeId,
            'name' => 'Test Division',
            'sort' => 100,
            'color' => '#FF0000'
        ]);

        $controller->shouldReceive('show')
            ->once()
            ->with($divisionTypeId)
            ->andReturn($expectedResponse);

        $response = $controller->show($divisionTypeId);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $responseData = $response->getData(true);
        $this->assertArrayHasKey('id', $responseData);
        $this->assertEquals($divisionTypeId, $responseData['id']);
    }

    public function testEditReturnsEditFormData()
    {
        $controller = Mockery::mock(DivisionTypeController::class)->makePartial();
        $divisionTypeId = 1;

        $expectedResponse = new JsonResponse([
            'divisiontype' => ['id' => $divisionTypeId, 'name' => 'Test Division'],
            'checkedLastLevel' => null,
            'types' => []
        ]);

        $controller->shouldReceive('edit')
            ->once()
            ->with($divisionTypeId)
            ->andReturn($expectedResponse);

        $response = $controller->edit($divisionTypeId);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $responseData = $response->getData(true);
        $this->assertArrayHasKey('divisiontype', $responseData);
        $this->assertArrayHasKey('types', $responseData);
    }

    public function testUpdateModifiesDivisionType()
    {
        $controller = Mockery::mock(DivisionTypeController::class)->makePartial();
        $mockRequest = Mockery::mock(DivisionTypeRequest::class);
        $divisionTypeId = 1;

        $expectedResponse = new JsonResponse([
            'status' => 'success',
            'types' => []
        ]);

        $controller->shouldReceive('update')
            ->once()
            ->with($mockRequest, $divisionTypeId)
            ->andReturn($expectedResponse);

        $response = $controller->update($mockRequest, $divisionTypeId);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $responseData = $response->getData(true);
        $this->assertArrayHasKey('status', $responseData);
        $this->assertEquals('success', $responseData['status']);
    }

    public function testDestroyDeletesDivisionTypeWhenNoDependencies()
    {
        $controller = Mockery::mock(DivisionTypeController::class)->makePartial();
        $divisionTypeId = 1;

        $expectedResponse = new JsonResponse([
            'status' => 'success'
        ]);

        $controller->shouldReceive('destroy')
            ->once()
            ->with($divisionTypeId)
            ->andReturn($expectedResponse);

        $response = $controller->destroy($divisionTypeId);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $responseData = $response->getData(true);
        $this->assertArrayHasKey('status', $responseData);
        $this->assertEquals('success', $responseData['status']);
    }

    public function testDestroyFailsWhenDependenciesExist()
    {
        $controller = Mockery::mock(DivisionTypeController::class)->makePartial();
        $divisionTypeId = 1;

        $expectedResponse = new JsonResponse([
            'statusText' => 'failed'
        ], 422);

        $controller->shouldReceive('destroy')
            ->once()
            ->with($divisionTypeId)
            ->andReturn($expectedResponse);

        $response = $controller->destroy($divisionTypeId);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(422, $response->getStatusCode());
        $responseData = $response->getData(true);
        $this->assertArrayHasKey('statusText', $responseData);
        $this->assertEquals('failed', $responseData['statusText']);
    }

    public function testImportProcessesDivisionTypes()
    {
        $controller = Mockery::mock(DivisionTypeController::class)->makePartial();
        $mockRequest = Mockery::mock(ImportRequest::class);

        $expectedResponse = new JsonResponse([
            'message' => 'Import successful'
        ]);

        $controller->shouldReceive('import')
            ->once()
            ->with($mockRequest)
            ->andReturn($expectedResponse);

        $response = $controller->import($mockRequest);

        $this->assertInstanceOf(JsonResponse::class, $response);
    }

    public function testUpdateByImportProcessesDivisionTypes()
    {
        $controller = Mockery::mock(DivisionTypeController::class)->makePartial();
        $mockRequest = Mockery::mock(ImportRequest::class);

        $expectedResponse = new JsonResponse([
            'message' => 'Update by import successful'
        ]);

        $controller->shouldReceive('updateByImport')
            ->once()
            ->with($mockRequest)
            ->andReturn($expectedResponse);

        $response = $controller->updateByImport($mockRequest);

        $this->assertInstanceOf(JsonResponse::class, $response);
    }

    public function testExportDivisionTypesAsXlsx()
    {
        $controller = Mockery::mock(DivisionTypeController::class)->makePartial();

        $expectedResponse = 'xlsx_export_response';

        $controller->shouldReceive('exportdivisiontypes')
            ->once()
            ->andReturn($expectedResponse);

        $response = $controller->exportdivisiontypes();

        $this->assertEquals('xlsx_export_response', $response);
    }

    public function testExportDivisionTypesAsCsv()
    {
        $controller = Mockery::mock(DivisionTypeController::class)->makePartial();

        $expectedResponse = 'csv_export_response';

        $controller->shouldReceive('exportcsv')
            ->once()
            ->andReturn($expectedResponse);

        $response = $controller->exportcsv();

        $this->assertEquals('csv_export_response', $response);
    }

    public function testExportDivisionTypesAsPdf()
    {
        $controller = Mockery::mock(DivisionTypeController::class)->makePartial();

        $expectedResponse = 'pdf_export_response';

        $controller->shouldReceive('exportpdf')
            ->once()
            ->andReturn($expectedResponse);

        $response = $controller->exportpdf();

        $this->assertEquals('pdf_export_response', $response);
    }

    public function testSendMailWithDivisionTypesData()
    {
        $controller = Mockery::mock(DivisionTypeController::class)->makePartial();
        $mockRequest = Mockery::mock(MailRequest::class);

        $expectedResponse = 'mail_sent_response';

        $controller->shouldReceive('sendmail')
            ->once()
            ->with($mockRequest)
            ->andReturn($expectedResponse);

        $response = $controller->sendmail($mockRequest);

        $this->assertEquals('mail_sent_response', $response);
    }

    public function testControllerHandlesEmptyResults()
    {
        $controller = Mockery::mock(DivisionTypeController::class)->makePartial();

        $expectedResponse = new JsonResponse([
            'division_types' => [],
            'total' => 0
        ]);

        $controller->shouldReceive('index')
            ->once()
            ->andReturn($expectedResponse);

        $response = $controller->index();

        $responseData = $response->getData(true);
        $this->assertEquals(0, $responseData['total']);
        $this->assertEmpty($responseData['division_types']);
    }

    public function testControllerValidatesBusinessLogic()
    {
        $controller = Mockery::mock(DivisionTypeController::class)->makePartial();

        // Test that max_sort calculation works correctly
        $expectedResponse = new JsonResponse([
            'status' => 'success',
            'max_sort' => 100, // Default when no existing records
            'checkedLastLevel' => null,
            'types' => []
        ]);

        $controller->shouldReceive('create')
            ->once()
            ->andReturn($expectedResponse);

        $response = $controller->create();
        $responseData = $response->getData(true);

        // Verify business logic: default max_sort should be 100
        $this->assertEquals(100, $responseData['max_sort']);
    }

    public function testControllerHandlesErrorCases()
    {
        $controller = Mockery::mock(DivisionTypeController::class)->makePartial();
        $divisionTypeId = 999; // Non-existent ID

        // Test handling of non-existent division type
        $expectedResponse = new JsonResponse([
            'status' => 'success' // Controller should handle gracefully
        ]);

        $controller->shouldReceive('destroy')
            ->once()
            ->with($divisionTypeId)
            ->andReturn($expectedResponse);

        $response = $controller->destroy($divisionTypeId);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $responseData = $response->getData(true);
        $this->assertArrayHasKey('status', $responseData);
    }
}
