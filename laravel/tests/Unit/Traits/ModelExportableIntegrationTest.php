<?php

use PHPUnit\Framework\TestCase;
use App\Exceptions\CrmException;
use App\Helpers\LogActivity;
use App\Traits\ModelExportable;
use Illuminate\Support\Facades\Excel;
use Mockery;

/**
 * Integration test for ModelExportable trait
 * 
 * Tests the refactored export functionality with real-world scenarios
 * to ensure backward compatibility and proper functionality.
 */
class ModelExportableIntegrationTest extends TestCase
{
    protected function setUp(): void
    {
        parent::setUp();

        // Mock all static facades and classes
        if (!class_exists('Mockery\Adapter\Phpunit\MockeryPHPUnitIntegration')) {
            $this->addToAssertionCount(1); // Prevent risky test warnings
        }
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    /**
     * Test that the refactored code produces the same export class names as the original
     * for various model types that exist in the codebase
     */
    public function testBackwardCompatibilityWithRealModels(): void
    {
        $testCases = [
            // App namespace models
            'App\Product' => 'App\Exports\ProductsExport',
            'App\Brand' => 'App\Exports\BrandsExport',
            'App\Family' => 'App\Exports\FamiliesExport',
            'App\Classification' => 'App\Exports\ClassificationsExport',
            'App\Classes' => 'App\Exports\ClassesExport',
            'App\Brick' => 'App\Exports\BricksExport',
            
            // App\Models namespace models
            'App\Models\VacationBalance' => 'App\Exports\Models\VacationBalancesExport',
            'App\Models\Coaching\Question' => 'App\Exports\Coaching\QuestionsExport',
            'App\Models\Coaching\Category' => 'App\Exports\Coaching\CategoriesExport',
        ];

        foreach ($testCases as $modelClass => $expectedExportClass) {
            $testModel = $this->createTestModel($modelClass);
            
            // Mock class exists for the expected export class
            $this->mockClassExists($expectedExportClass, true);
            
            $reflection = new \ReflectionClass($testModel);
            $method = $reflection->getMethod('resolveExportClassName');
            $method->setAccessible(true);
            
            $result = $method->invoke(null);
            
            $this->assertEquals(
                $expectedExportClass, 
                $result, 
                "Export class name resolution failed for model: {$modelClass}"
            );
        }
    }

    /**
     * Test filename generation for various models
     */
    public function testFilenameGenerationForRealModels(): void
    {
        $testCases = [
            'App\Product' => 'products',
            'App\Brand' => 'brands',
            'App\Family' => 'families',
            'App\Models\VacationBalance' => 'vacationbalances',
            'App\Models\Coaching\Question' => 'questions',
        ];

        foreach ($testCases as $modelClass => $expectedBaseName) {
            $testModel = $this->createTestModel($modelClass);
            
            $reflection = new \ReflectionClass($testModel);
            $method = $reflection->getMethod('generateExportFilename');
            $method->setAccessible(true);
            
            $result = $method->invoke(null, 'xlsx');
            
            $this->assertEquals(
                $expectedBaseName . '.xlsx', 
                $result, 
                "Filename generation failed for model: {$modelClass}"
            );
        }
    }

    /**
     * Test that error handling works correctly for non-existent export classes
     */
    public function testErrorHandlingForMissingExportClasses(): void
    {
        $testModel = $this->createTestModel('App\NonExistentModel');
        
        $reflection = new \ReflectionClass($testModel);
        $method = $reflection->getMethod('resolveExportClassName');
        $method->setAccessible(true);
        
        // Don't mock class_exists, so it will return false for non-existent class
        
        $this->expectException(CrmException::class);
        $this->expectExceptionMessage("Export class 'App\Exports\NonExistentModelsExport' does not exist for model 'NonExistentModel'");
        
        $method->invoke(null);
    }

    /**
     * Test that the original string manipulation logic is preserved
     * This ensures we haven't broken the core functionality
     */
    public function testOriginalLogicPreservation(): void
    {
        // Test App namespace pattern (original: Str::replaceFirst('App', 'App\Exports', $model . 'Export'))
        $testModel = $this->createTestModel('App\Product');
        $this->mockClassExists('App\Exports\ProductsExport', true);
        
        $reflection = new \ReflectionClass($testModel);
        $method = $reflection->getMethod('resolveExportClassName');
        $method->setAccessible(true);
        
        $result = $method->invoke(null);
        
        // This should match the original logic: 'App\Products' -> 'App\Exports\ProductsExport'
        $this->assertEquals('App\Exports\ProductsExport', $result);
        
        // Test App\Models namespace pattern (original: Str::replaceFirst('App\Models', 'App\Exports', $model . 'Export'))
        $testModel2 = $this->createTestModel('App\Models\Coaching\Question');
        $this->mockClassExists('App\Exports\Coaching\QuestionsExport', true);
        
        $result2 = $method->invoke(null);
        
        // This should match the original logic: 'App\Models\Coaching\Questions' -> 'App\Exports\Coaching\QuestionsExport'
        $this->assertEquals('App\Exports\Coaching\QuestionsExport', $result2);
    }

    /**
     * Test that filename generation matches original logic
     */
    public function testOriginalFilenameLogic(): void
    {
        $testModel = $this->createTestModel('App\Product');
        
        $reflection = new \ReflectionClass($testModel);
        $method = $reflection->getMethod('generateExportFilename');
        $method->setAccessible(true);
        
        // Original logic: Str::plural(Str::lower(collect(explode("\\", $model))->last())) . '.' . Str::lower($writerType)
        // For 'App\Product' -> 'products.xlsx'
        $result = $method->invoke(null, 'XLSX');
        $this->assertEquals('products.xlsx', $result);
        
        // Test case sensitivity handling
        $result2 = $method->invoke(null, 'CSV');
        $this->assertEquals('products.csv', $result2);
    }

    /**
     * Create a test model class that uses the ModelExportable trait
     */
    private function createTestModel(string $className): string
    {
        $safeClassName = "IntegrationTestModel_" . str_replace(['\\', '.'], '_', $className) . '_' . uniqid();
        
        $classCode = "
        class {$safeClassName} {
            use " . ModelExportable::class . ";
            
            public static function class() { 
                return '{$className}'; 
            }
        }";
        
        eval($classCode);
        
        return $safeClassName;
    }

    /**
     * Mock the class_exists function by creating dummy classes
     */
    private function mockClassExists(string $className, bool $exists): void
    {
        if ($exists && !class_exists($className)) {
            // Create a dummy class to simulate existence
            $safeClassName = str_replace(['\\', '.'], '_', $className);
            eval("class {$safeClassName} {}");
            
            // Create an alias if needed
            if (!class_exists($className)) {
                class_alias($safeClassName, $className);
            }
        }
    }
}
