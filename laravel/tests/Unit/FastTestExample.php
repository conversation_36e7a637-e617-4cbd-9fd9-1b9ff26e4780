<?php
namespace Tests\Unit;

it('can perform fast calculations', function () {
    expect(1 + 1)->toBe(2);
    expect(10 * 10)->toBe(100);
});

it('can work with arrays quickly', function () {
    $array = ['a', 'b', 'c'];
    expect($array)->toHaveCount(3);
    expect($array[0])->toBe('a');
});

it('can mock dependencies without database', function () {
    // Example of mocking a service
    $mock = mock('App\Services\SomeService')
        ->shouldReceive('doSomething')
        ->andReturn('mocked result')
        ->getMock();
    
    expect($mock->doSomething())->toBe('mocked result');
});
