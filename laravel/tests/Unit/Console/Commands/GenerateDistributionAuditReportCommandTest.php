<?php

namespace Tests\Unit\Console\Commands;

use App\Console\Commands\GenerateDistributionAuditReportCommand;
use Carbon\Carbon;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Storage;
use Tests\TestCase;

/**
 * Test class for GenerateDistributionAuditReportCommand
 * 
 * Tests the comprehensive distribution audit report generation including
 * validation of distribution integrity, hierarchy checks, split analysis,
 * ceiling processing, and referential integrity.
 * 
 * @covers \App\Console\Commands\GenerateDistributionAuditReportCommand
 */
class GenerateDistributionAuditReportCommandTest extends TestCase
{
    private string $fromDate;
    private string $toDate;

    protected function setUp(): void
    {
        parent::setUp();

        $this->fromDate = Carbon::now()->subDays(30)->format('Y-m-d');
        $this->toDate = Carbon::now()->format('Y-m-d');

        // Mock storage for file exports
        Storage::fake('local');
    }

    protected function tearDown(): void
    {
        parent::tearDown();
    }

    /**
     * Test command signature and basic structure
     */
    public function test_command_signature(): void
    {
        $command = new GenerateDistributionAuditReportCommand();
        
        $this->assertStringContainsString('distribution:generate-audit-report', $command->getName());
        $this->assertStringContainsString('Generate comprehensive audit report', $command->getDescription());
    }

    /**
     * Test command requires date parameters
     */
    public function test_command_requires_date_parameters(): void
    {
        $exitCode = Artisan::call('distribution:generate-audit-report');
        
        $this->assertEquals(1, $exitCode);
        $this->assertStringContainsString('Both --from-date and --to-date are required', Artisan::output());
    }

    /**
     * Test command validates date format
     */
    public function test_command_validates_date_format(): void
    {
        $exitCode = Artisan::call('distribution:generate-audit-report', [
            '--from-date' => 'invalid-date',
            '--to-date' => '2024-01-31'
        ]);
        
        $this->assertEquals(1, $exitCode);
        $this->assertStringContainsString('Invalid date format', Artisan::output());
    }

    /**
     * Test command validates date range
     */
    public function test_command_validates_date_range(): void
    {
        $exitCode = Artisan::call('distribution:generate-audit-report', [
            '--from-date' => '2024-01-31',
            '--to-date' => '2024-01-01'
        ]);
        
        $this->assertEquals(1, $exitCode);
        $this->assertStringContainsString('from-date cannot be after to-date', Artisan::output());
    }

    /**
     * Test command validates distribution type
     */
    public function test_command_validates_distribution_type(): void
    {
        $exitCode = Artisan::call('distribution:generate-audit-report', [
            '--from-date' => $this->fromDate,
            '--to-date' => $this->toDate,
            '--distribution-type' => '5'
        ]);
        
        $this->assertEquals(1, $exitCode);
        $this->assertStringContainsString('distribution-type must be 1, 2, or 3', Artisan::output());
    }

    /**
     * Test command validates output format
     */
    public function test_command_validates_output_format(): void
    {
        $exitCode = Artisan::call('distribution:generate-audit-report', [
            '--from-date' => $this->fromDate,
            '--to-date' => $this->toDate,
            '--format' => 'invalid'
        ]);
        
        $this->assertEquals(1, $exitCode);
        $this->assertStringContainsString('format must be table, json, or csv', Artisan::output());
    }

    /**
     * Test basic command instantiation and structure
     */
    public function test_basic_command_execution(): void
    {
        // Test that the command can be instantiated
        $command = new GenerateDistributionAuditReportCommand();
        $this->assertInstanceOf(GenerateDistributionAuditReportCommand::class, $command);

        // Test command signature
        $this->assertStringContainsString('distribution:generate-audit-report', $command->getName());
        $this->assertStringContainsString('Generate comprehensive audit report', $command->getDescription());
    }

    /**
     * Test command options are properly defined
     */
    public function test_json_output_format(): void
    {
        $command = new GenerateDistributionAuditReportCommand();
        $signature = $command->getDefinition();

        // Test that format option exists and accepts json
        $this->assertTrue($signature->hasOption('format'));

        // Test that all expected options are defined
        $expectedOptions = [
            'from-date',
            'to-date',
            'distribution-type',
            'format',
            'export-file'
        ];

        foreach ($expectedOptions as $option) {
            $this->assertTrue($signature->hasOption($option), "Option '{$option}' should be defined");
        }
    }

    /**
     * Test command analysis flags
     */
    public function test_csv_output_format(): void
    {
        $command = new GenerateDistributionAuditReportCommand();
        $signature = $command->getDefinition();

        // Test analysis flag options
        $analysisFlags = [
            'include-hierarchy',
            'include-split-analysis',
            'include-ceiling-analysis',
            'include-referential',
            'summary-only'
        ];

        foreach ($analysisFlags as $flag) {
            $this->assertTrue($signature->hasOption($flag), "Analysis flag '{$flag}' should be defined");
        }
    }

    /**
     * Test command filter options
     */
    public function test_file_export(): void
    {
        $command = new GenerateDistributionAuditReportCommand();
        $signature = $command->getDefinition();

        // Test filter options
        $filterOptions = [
            'product-ids',
            'distributor-ids',
            'tolerance'
        ];

        foreach ($filterOptions as $option) {
            $this->assertTrue($signature->hasOption($option), "Filter option '{$option}' should be defined");
        }

        // Test that tolerance option has a default value
        $toleranceOption = $signature->getOption('tolerance');
        $this->assertNotNull($toleranceOption);
    }

    /**
     * Test command signature structure
     */
    public function test_csv_file_export(): void
    {
        $command = new GenerateDistributionAuditReportCommand();

        // Test that the command name and description are properly set
        $this->assertStringContainsString('distribution:generate-audit-report', $command->getName());

        // Test that the command has a proper description
        $description = $command->getDescription();
        $this->assertNotEmpty($description);
        $this->assertStringContainsString('audit', $description);
        $this->assertStringContainsString('distribution', $description);

        // Test that the command definition has expected options
        $definition = $command->getDefinition();
        $this->assertTrue($definition->hasOption('from-date'));
        $this->assertTrue($definition->hasOption('to-date'));
        $this->assertTrue($definition->hasOption('format'));
    }

    /**
     * Test command option types and defaults
     */
    public function test_comprehensive_analysis(): void
    {
        $command = new GenerateDistributionAuditReportCommand();
        $definition = $command->getDefinition();

        // Test that boolean flags are properly defined
        $booleanFlags = [
            'include-hierarchy',
            'include-split-analysis',
            'include-ceiling-analysis',
            'include-referential',
            'summary-only'
        ];

        foreach ($booleanFlags as $flag) {
            $option = $definition->getOption($flag);
            $this->assertNotNull($option, "Flag '{$flag}' should exist");
            $this->assertFalse($option->acceptValue(), "Flag '{$flag}' should not accept a value");
        }

        // Test that value options are properly defined
        $valueOptions = ['from-date', 'to-date', 'format', 'export-file'];
        foreach ($valueOptions as $option) {
            $optionDef = $definition->getOption($option);
            $this->assertNotNull($optionDef, "Option '{$option}' should exist");
        }
    }

    /**
     * Test command help and documentation
     */
    public function test_store_strategy_analysis(): void
    {
        $command = new GenerateDistributionAuditReportCommand();

        // Test that the command has proper help text
        $help = $command->getHelp();
        $this->assertIsString($help);

        // Test that the command name follows Laravel conventions
        $name = $command->getName();
        $this->assertStringContainsString(':', $name, 'Command should follow namespace:action pattern');
        $this->assertStringStartsWith('distribution:', $name, 'Command should be in distribution namespace');

        // Test that the command is properly configured
        $this->assertNotEmpty($command->getDescription());
        $this->assertNotEmpty($command->getName());
    }




}
