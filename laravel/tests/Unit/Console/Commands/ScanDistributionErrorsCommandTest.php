<?php

namespace Tests\Unit\Console\Commands;

use App\Console\Commands\ScanDistributionErrorsCommand;
use App\Services\Enums\Ceiling;
use Mockery;
use PHPUnit\Framework\TestCase;

class ScanDistributionErrorsCommandTest extends TestCase
{
    protected function setUp(): void
    {
        parent::setUp();
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    /**
     * Test date validation logic
     */
    public function test_date_validation_logic(): void
    {
        // Test missing dates
        $command = new ScanDistributionErrorsCommand();
        $reflection = new \ReflectionClass($command);
        $validateMethod = $reflection->getMethod('validateInput');
        $validateMethod->setAccessible(true);

        // Mock the option method to return null for missing dates
        $commandMock = new class extends ScanDistributionErrorsCommand {
            private array $options = [];

            public function setOptions(array $options): void {
                $this->options = $options;
            }

            public function option($key = null) {
                return $this->options[$key] ?? null;
            }

            public function error($string, $verbosity = null) {
                // Mock error output
            }

            public function line($string, $style = null, $verbosity = null) {
                // Mock line output
            }
        };

        // Test missing dates
        $commandMock->setOptions(['from-date' => null, 'to-date' => null]);
        $result = $validateMethod->invoke($commandMock);
        $this->assertFalse($result);

        // Test valid dates
        $commandMock->setOptions(['from-date' => '2024-01-01', 'to-date' => '2024-01-31']);
        $result = $validateMethod->invoke($commandMock);
        $this->assertTrue($result);

        // Test invalid date range
        $commandMock->setOptions(['from-date' => '2024-01-31', 'to-date' => '2024-01-01']);
        $result = $validateMethod->invoke($commandMock);
        $this->assertFalse($result);
    }

    /**
     * Test invalid date format validation
     */
    public function test_invalid_date_format_validation(): void
    {
        $commandMock = new class extends ScanDistributionErrorsCommand {
            private array $options = [];

            public function setOptions(array $options): void {
                $this->options = $options;
            }

            public function option($key = null) {
                return $this->options[$key] ?? null;
            }

            public function error($string, $verbosity = null) {
                // Mock error output
            }

            public function line($string, $style = null, $verbosity = null) {
                // Mock line output
            }
        };

        $reflection = new \ReflectionClass($commandMock);
        $validateMethod = $reflection->getMethod('validateInput');
        $validateMethod->setAccessible(true);

        // Test invalid date format
        $commandMock->setOptions(['from-date' => 'invalid-date', 'to-date' => '2024-01-31']);
        $result = $validateMethod->invoke($commandMock);
        $this->assertFalse($result);
    }

    /**
     * Test tolerance parameter conversion
     */
    public function test_tolerance_parameter_conversion(): void
    {
        // Test float conversion logic
        $tolerance1 = (float) '0.001';
        $this->assertEquals(0.001, $tolerance1);

        $tolerance2 = (float) null;
        $this->assertEquals(0.0, $tolerance2);

        $tolerance3 = (float) '0.1';
        $this->assertEquals(0.1, $tolerance3);

        // Test that tolerance is used for comparison
        $value1 = 100.0;
        $value2 = 100.0005;
        $tolerance = 0.001;

        $this->assertTrue(abs($value1 - $value2) < $tolerance);

        $value3 = 100.002;
        $this->assertFalse(abs($value1 - $value3) < $tolerance);
    }

    /**
     * Test Ceiling enum validation
     */
    public function test_ceiling_enum_validation(): void
    {
        // Test valid ceiling values
        $validCeilings = [
            Ceiling::BELOW->value,
            Ceiling::ABOVE->value,
            Ceiling::DISTRIBUTED->value
        ];

        foreach ($validCeilings as $ceiling) {
            $ceilingEnum = Ceiling::from($ceiling);
            $this->assertInstanceOf(Ceiling::class, $ceilingEnum);
        }

        // Test invalid ceiling value
        $this->expectException(\ValueError::class);
        Ceiling::from(999);
    }

    /**
     * Test boolean option conversion
     */
    public function test_boolean_option_conversion(): void
    {
        // Test boolean conversion logic similar to what the command does
        $detailed1 = (bool) true;
        $this->assertTrue($detailed1);

        $detailed2 = (bool) false;
        $this->assertFalse($detailed2);

        $detailed3 = (bool) null;
        $this->assertFalse($detailed3);

        $fixErrors1 = (bool) 'true';
        $this->assertTrue($fixErrors1);

        $fixErrors2 = (bool) '';
        $this->assertFalse($fixErrors2);
    }
}
