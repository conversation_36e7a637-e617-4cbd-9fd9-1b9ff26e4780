<?php

namespace Tests\Unit;

use App\Sale;
use App\Services\Enums\Ceiling;
use Illuminate\Database\Eloquent\Builder;
use Mockery;
use Tests\TestCase;

/**
 * Test class for Sale model query scopes
 *
 * Tests the distribution-related query scopes for filtering sales by their distribution status
 *
 * @covers \App\Sale
 */
class SaleScopesTest extends TestCase
{
    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    /**
     * Test scopeDistributed filters sales with DISTRIBUTED ceiling status
     */
    public function test_scope_distributed_filters_distributed_sales(): void
    {
        // Arrange
        $query = Mockery::mock(Builder::class);
        $sale = new Sale();

        // Assert that the query is filtered by DISTRIBUTED ceiling status
        $query->shouldReceive('where')
            ->once()
            ->with('ceiling', Ceiling::DISTRIBUTED->value)
            ->andReturnSelf();

        // Act
        $result = $sale->scopeDistributed($query);

        // Assert
        $this->assertInstanceOf(Builder::class, $result);
    }

    /**
     * Test scopeNormal filters sales with BELOW ceiling status
     */
    public function test_scope_normal_filters_normal_sales(): void
    {
        // Arrange
        $query = Mockery::mock(Builder::class);
        $sale = new Sale();

        // Assert that the query is filtered by BELOW ceiling status
        $query->shouldReceive('where')
            ->once()
            ->with('ceiling', Ceiling::BELOW->value)
            ->andReturnSelf();

        // Act
        $result = $sale->scopeNormal($query);

        // Assert
        $this->assertInstanceOf(Builder::class, $result);
    }

    /**
     * Test scopeOriginal filters original sales correctly
     */
    public function test_scope_original_filters_original_sales(): void
    {
        // Arrange
        $query = Mockery::mock(Builder::class);
        $innerQuery = Mockery::mock(Builder::class);
        $sale = new Sale();

        // Mock the nested where clause
        $query->shouldReceive('where')
            ->once()
            ->with(Mockery::type('callable'))
            ->andReturnUsing(function ($callback) use ($innerQuery) {
                $callback($innerQuery);
                return $innerQuery;
            });

        // Mock the inner query conditions
        $innerQuery->shouldReceive('where')
            ->once()
            ->with('ceiling', Ceiling::ABOVE->value)
            ->andReturnSelf();

        $innerQuery->shouldReceive('orWhereNull')
            ->once()
            ->with('sale_ids')
            ->andReturnSelf();

        $innerQuery->shouldReceive('orWhereRaw')
            ->once()
            ->with('FIND_IN_SET(id, sale_ids) > 0')
            ->andReturnSelf();

        // Act
        $result = $sale->scopeOriginal($query);

        // Assert
        $this->assertInstanceOf(Builder::class, $result);
    }

    /**
     * Test that scopes can be chained together
     */
    public function test_scopes_can_be_chained(): void
    {
        // This test verifies that our scopes follow Laravel conventions
        // and can be used in method chaining

        // Arrange
        $query = Mockery::mock(Builder::class);
        $sale = new Sale();

        // Mock chaining behavior
        $query->shouldReceive('where')
            ->with('ceiling', Ceiling::DISTRIBUTED->value)
            ->andReturnSelf();

        $query->shouldReceive('where')
            ->with('created_at', '>', '2023-01-01')
            ->andReturnSelf();

        // Act - simulate chaining scopes
        $result = $sale->scopeDistributed($query);
        $result->where('created_at', '>', '2023-01-01');

        // Assert
        $this->assertInstanceOf(Builder::class, $result);
    }

    /**
     * Test ceiling enum values are correctly used
     */
    public function test_ceiling_enum_values_are_correct(): void
    {
        // This test ensures our scopes use the correct enum values
        
        // Assert enum values match expected string values
        $this->assertEquals("2", Ceiling::DISTRIBUTED->value);
        $this->assertEquals("0", Ceiling::BELOW->value);
        $this->assertEquals("1", Ceiling::ABOVE->value);
    }

    /**
     * Test scope method signatures follow Laravel conventions
     */
    public function test_scope_method_signatures(): void
    {
        // Verify that our scope methods exist and have correct signatures
        $sale = new Sale();
        
        $this->assertTrue(method_exists($sale, 'scopeDistributed'));
        $this->assertTrue(method_exists($sale, 'scopeNormal'));
        $this->assertTrue(method_exists($sale, 'scopeOriginal'));

        // Verify method signatures using reflection
        $distributedMethod = new \ReflectionMethod($sale, 'scopeDistributed');
        $normalMethod = new \ReflectionMethod($sale, 'scopeNormal');
        $originalMethod = new \ReflectionMethod($sale, 'scopeOriginal');

        // All scope methods should have exactly one parameter (the query builder)
        $this->assertEquals(1, $distributedMethod->getNumberOfParameters());
        $this->assertEquals(1, $normalMethod->getNumberOfParameters());
        $this->assertEquals(1, $originalMethod->getNumberOfParameters());

        // All scope methods should return Builder
        $this->assertEquals('Illuminate\Database\Eloquent\Builder', $distributedMethod->getReturnType()->getName());
        $this->assertEquals('Illuminate\Database\Eloquent\Builder', $normalMethod->getReturnType()->getName());
        $this->assertEquals('Illuminate\Database\Eloquent\Builder', $originalMethod->getReturnType()->getName());
    }
}
