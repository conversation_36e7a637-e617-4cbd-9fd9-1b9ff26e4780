# Distribution Check Sale Status Command

## Overview

The `distribution:check-sale-status` command allows you to check whether a specific sale has been distributed or not, with detailed analysis of distribution criteria.

## Command Signature

```bash
php artisan distribution:check-sale-status {sale_id} [--detailed]
```

### Parameters

- `sale_id` (required): The ID of the sale to check
- `--detailed` (optional): Show detailed analysis and criteria

## Usage Examples

### Basic Usage

```bash
# Check distribution status of sale ID 521132
./vendor/bin/sail artisan distribution:check-sale-status 521132
```

### Detailed Analysis

```bash
# Check with detailed analysis
./vendor/bin/sail artisan distribution:check-sale-status 521132 --detailed
```

## Output Explanation

### Sale Information
The command displays basic sale information including:
- Sale ID
- Product name and ID
- Distributor name and ID
- Date, quantity, value, bonus, and region

### Distribution Status
Shows whether the sale has been distributed with one of three possible states:

1. **BELOW (0)**: Sale is in normal state (below ceiling limit)
2. **ABOVE (1)**: Sale exceeded ceiling and was marked as original (likely replaced by distributed sales)
3. **DISTRIBUTED (2)**: Sale is a distributed sale created from ceiling violation

### Eligibility Analysis
The command analyzes whether the sale meets distribution criteria:

- **Ceiling Below**: Sale must have ceiling = "0" to be eligible
- **Has Mappings**: Sale must have mappings attached
- **Non Exception Mappings**: Mappings must not be marked as exceptions
- **Has Product Ceiling**: Product must have active ceiling limits
- **Exceeds Ceiling**: Sale quantity must exceed ceiling limit
- **Valid Distribution Type**: Must have valid distribution type from mappings

### Detailed Analysis (--detailed flag)
When using the `--detailed` flag, additional information is shown:

- **Mapping Details**: Information about attached mappings
- **Sales Details Breakdown**: Breakdown of sales details and validation
- **Distribution Service Query Simulation**: Simulates what the distribution service would find

## Example Outputs

### Non-Distributed Sale (BELOW)
```
=== SALE DISTRIBUTION STATUS CHECK ===
Sale ID: 521132
Product: Arbateg 100 ml Susp Tender (ID: 90)
Distributor: POS Tender (ID: 7)
Date: 2024-01-01
Quantity: -55.00000
Value: 0.00000
Bonus: 0.0
Region: 1

=== CURRENT DISTRIBUTION STATUS ===
Distributed: ❌ NO
Ceiling Status: BELOW (0)
Description: Sale is in normal state (below ceiling limit)

=== DISTRIBUTION ELIGIBILITY ANALYSIS ===
Eligible for Distribution: ❌ NO

Reasons why sale is NOT eligible:
  ❌ Sale quantity (-55.00000) does not exceed ceiling limit (10000.000)

=== CONCLUSION ===
📋 This sale HAS NOT BEEN DISTRIBUTED
   → Sale does not meet distribution criteria
```

### Distributed Sale (DISTRIBUTED)
```
=== SALE DISTRIBUTION STATUS CHECK ===
Sale ID: 1334437
Product: Gincostazen 30 cap (ID: 8)
Distributor: POS (ID: 2)
Date: 2024-01-01
Quantity: 34.00000
Value: 0.00000
Bonus: 0.0
Region: 0

=== CURRENT DISTRIBUTION STATUS ===
Distributed: ✅ YES
Ceiling Status: DISTRIBUTED (2)
Description: Sale is a distributed sale created from ceiling violation

=== CONCLUSION ===
🎯 This sale HAS BEEN DISTRIBUTED
   → This is a distributed sale created from ceiling violation
```

### Original Sale That Was Distributed (ABOVE)
```
=== SALE DISTRIBUTION STATUS CHECK ===
Sale ID: 1566431
Product: Gynomonix 0.8% Vaginal 30 gm Cream (ID: 25)
Distributor: EGY (ID: 3)
Date: 2024-08-01
Quantity: 54.00000
Value: 0.00000
Bonus: 0.0
Region: 1

=== CURRENT DISTRIBUTION STATUS ===
Distributed: ✅ YES
Ceiling Status: ABOVE (1)
Description: Sale exceeded ceiling and has been distributed (original sale marked as ABOVE, distributed sales created)

Related Sales:
  - Sale ID 2301019 (distributed): Qty=4.00000, Value=0.00000, Bonus=0.0

=== CONCLUSION ===
🎯 This sale HAS BEEN DISTRIBUTED
   → This is an original sale that was distributed (marked as ABOVE)
```

## Error Handling

### Invalid Sale ID
```bash
./vendor/bin/sail artisan distribution:check-sale-status abc
# Output: Sale ID must be a positive integer
```

### Non-existent Sale
```bash
./vendor/bin/sail artisan distribution:check-sale-status 99999
# Output: Sale with ID 99999 not found
```

## Technical Details

### Distribution Logic
The command uses the same logic as the `DistributionService` to determine:
1. Current distribution status based on the `ceiling` field
2. Eligibility criteria for distribution
3. Related sales (original or distributed)

### Database Tables Used
- `sales`: Main sales table with ceiling status
- `mapping_sale`: Sale-mapping relationships
- `mappings`: Mapping information and distribution types
- `product_ceilings`: Product ceiling limits
- `sales_details`: Sales detail breakdown

### Laravel Octane Compatibility
The command is fully compatible with Laravel Octane:
- Uses dependency injection for services
- No static state or memory leaks
- Proper resource cleanup

## Integration with Distribution System

This command provides insight into the distribution system by:
- Showing current distribution status
- Explaining why sales are or aren't distributed
- Validating distribution criteria
- Simulating distribution service queries

It's particularly useful for:
- Debugging distribution issues
- Understanding why certain sales weren't distributed
- Validating distribution logic
- Auditing distribution results
