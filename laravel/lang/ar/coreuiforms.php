<?php

return [
    'notes' => [  
        'note' => 'Note',
        'notes' => 'Notes',
        'create_note' => 'Create Note',
        'add_note' => 'Add Note',
        'author' => 'Author',
        'title' => 'Title',
        'content' => 'Content',
        'applies_to_date' => 'Applies to date',
        'status' => 'Status',
        'note_type' => 'Note type',
    ],
    'users' => [
        'user' => 'User',
        'users' => 'Users',
        'username' => 'Username',
        'email' => 'E-mail',
        'roles' => 'Roles',
        'email_verified_at' => 'Email verified at',
    ],
    'menu' => [
        'menu_list' => 'Menu list',
        'add_new_menu' => 'Add new menu',
        'name' => 'Name',
        'create_menu' => 'Create menu',
        'menu_elements' => 'Menu Elements',
        'add_new_menu_element' => 'Add new menu element',
        'change_menu' => 'Change menu',
        'type' => 'Type',
        'href' => 'href',
        'sequence' => 'Sequence',
        'show_menu_element' => 'Show menu element',
        'menu' => 'Menu',
        'user_roles' => 'User Roles',
        'translations' => 'Translations',
        'dropdown_parent' => 'Dropdown parent',
        'icon' => 'Icon',
        'edit_menu_element' => 'Edit menu element',
        'other' => 'Other',
        'find_icon_class_in' => 'Find icon class in',
        'coreui_icons_docs' => 'CoreUI icons documentation',
        'create_menu_element' => 'Create menu element',
    ],
    'roles' => [
        'menu_roles' => 'Menu roles',
        'add_new_role' => 'Add new role',
        'name' => 'Name',
        'hierarchy' => 'Hierarchy',
        'created_at' => 'Created at',
        'updated_at' => 'Updated at',
        'create_new_role' => 'Create new role',
        'edit_role' => 'Edit role',
        'show_role' => 'Show role',
    ],
    'media' => [
        'media' => 'Media',
        'back' => 'Back',
        'new_folder' => 'New folder',
        'new_file' => 'New file',
        'rename' => 'Rename',
        'move' => 'Move',
        'delete' => 'Delete',
        'open' => 'Open',
        'copy' => 'Copy',
        'move' => 'Move',
        'cropp' => 'Cropp',
        'move_folder' => 'Move folder',
        'move_up' => 'Move up',
        'move_file' => 'Move file',
        'rename_file' => 'Rename file',
        'rename_folder' => 'Rename folder',
        'file_info' => 'File info',
        'name' => 'Name',
        'real_name' => 'Real Name',
        'url' => 'URL',
        'mime_type' => 'mime type',
        'size' => 'Size',
        'created_at' => 'Created at',
        'updated_at' => 'Updated at',
        'delete_file' => 'Delete file',
        'are_you_sure' => 'Are you sure?',
        'delete_folder' => 'Delete folder',
        'cropp_image' => 'Cropp image',
        'delete_folder_warn' => 'If you delete a folder, all subfolders and files will also be deleted.', 
    ],
    'bread' => [
        'add_new_bread' => 'Add new BREAD',
        'go_to_resource' => 'Go to resource',
        'create_bread' => 'Create BREAD',
        'table_name_db' => 'Table name in database',
        'form_name' => 'Form name',
        'pagination' => 'Records on one page of table',
        'enable_show' => 'Enable Show button in table',
        'enable_edit' => 'Enable Edit button in table',
        'enable_add' => 'Enable Add button in table',
        'enable_delete' => 'Enable Delete button in table',
        'assign_to_roles' => 'Assign to roles',
        'visible_name' => 'Visible name',
        'field_type' => 'Field type',
        'relation_table' => 'Optional relation table name',
        'relation_column' => 'Optional column name in relation table - to print',
        'show_bread' => 'Show BREAD',
        'edit_bread' => 'Edit BREAD',
        'delete_bread' => 'Delete BREAD',
    ],
    'resource' => [
        'add_new' => 'Add new',
        'add' => 'Add',
        'show' => 'Show',
    ],
    'select' => 'Select',
    'view' => 'View',
    'edit' => 'Edit',
    'delete' => 'Delete',
    'return' => 'Return',
    'save' => 'Save',
    'cancel' => 'Cancel',
    'are_you_sure' => 'Are you sure?',
];