<?xml version="1.0" encoding="UTF-8" ?>
<testsuites errors="0"
            failures="0"
            tests="0">

  <testsuite name="e2e.specs.test"
    errors="0" failures="0" hostname="" id="" package="specs" skipped="1"
    tests="0" time="0" timestamp="">
  

  
    <system-err>
      [0;31m  Error: An error occurred while retrieving a new session: &#34;Connection refused to 127.0.0.1:4444&#34;. If the Webdriver/Selenium service is managed by Nightwatch, check if &#34;start_process&#34; is set to &#34;true&#34;.[0m
[0;90m       at Socket.socketErrorListener (_http_client.js:387:9)[0m
    </system-err>
  

  
    
    <testcase
      name="CoreUI Vue e2e tests" classname="e2e.specs.test">
      <skipped />
    </testcase>
    
  
  </testsuite>
</testsuites>
