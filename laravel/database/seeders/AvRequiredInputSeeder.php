<?php

namespace Database\Seeders;
use App\AvRequiredInput;
use Illuminate\Database\Seeder;

class AvRequiredInputSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $row = AvRequiredInput::create([
            'name' => 'giveaway',
            'select' => 0,
            'created_at' => now(),
            'updated_at' => now()
        ]);

        $row = AvRequiredInput::create([
            'name' => 'product_comment',
            'select' => 0,
            'created_at' => now(),
            'updated_at' => now()
        ]);

        $row = AvRequiredInput::create([
            'name' => 'product_followup',
            'select' => 0,
            'created_at' => now(),
            'updated_at' => now()
        ]);

        $row = AvRequiredInput::create([
            'name' => 'product_market_feedback',
            'select' => 0,
            'created_at' => now(),
            'updated_at' => now()
        ]);

        $row = AvRequiredInput::create([
            'name' => 'attachment',
            'select' => 0,
            'created_at' => now(),
            'updated_at' => now()
        ]);

    }
}
