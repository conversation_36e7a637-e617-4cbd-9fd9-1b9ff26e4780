<?php

namespace Database\Seeders;
use App\Chart;
use Illuminate\Database\Seeder;

class ChartSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $row = Chart::create([
            'name' => 'List Coverage Doctor Level',
            'widget_id' => 1,
            'created_at' => now(),
            'updated_at' => now()
        ]);

        $row = Chart::create([
            'name' => 'List Coverage Account Level',
            'widget_id' => 2,
            'created_at' => now(),
            'updated_at' => now()
        ]);

        $row = Chart::create([
            'name' => 'AM List Coverage Doctor Level',
            'widget_id' => 3,
            'created_at' => now(),
            'updated_at' => now()
        ]);

        $row = Chart::create([
            'name' => 'AM List Coverage Account Level',
            'widget_id' => 4,
            'created_at' => now(),
            'updated_at' => now()
        ]);

        $row = Chart::create([
            'name' => 'PM List Coverage Doctor Level',
            'widget_id' => 5,
            'created_at' => now(),
            'updated_at' => now()
        ]);

        $row = Chart::create([
            'name' => 'PM List Coverage Account Level',
            'widget_id' => 6,
            'created_at' => now(),
            'updated_at' => now()
        ]);

        $row = Chart::create([
            'name' => 'List Account Types Coverage',
            'widget_id' => 7,
            'created_at' => now(),
            'updated_at' => now()
        ]);
    }
}
