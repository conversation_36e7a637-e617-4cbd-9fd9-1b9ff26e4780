<?php

namespace Database\Seeders;

use App\LinkedParmaciesSetting;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Schema;

class LinkedParmaciesSettingSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $plan_settings =
            [
                [
                    'name' => 'Number Of Linked Pharmacies',
                    'key' => 'number_of_linked_pharmacies',
                    'value' => '5',
                    'type' => 'select',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'name' => 'Number Of Month Before',
                    'key' => 'number_of_month_befor',
                    'value' => '2',
                    'type' => 'select',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'name' => 'Number Of Month After',
                    'key' => 'number_of_month_after',
                    'value' => '3',
                    'type' => 'select',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'name' => 'Type',
                    'key' => 'type',
                    'value' => 'test',
                    'type' => 'select',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                
            ];

        Schema::disableForeignKeyConstraints();
        LinkedParmaciesSetting::truncate();
        Schema::enableForeignKeyConstraints();

        $chunked_plan_settings = array_chunk($plan_settings, 5);


        foreach ($chunked_plan_settings as $value) {
            LinkedParmaciesSetting::insert($value);
        }
    }
}
