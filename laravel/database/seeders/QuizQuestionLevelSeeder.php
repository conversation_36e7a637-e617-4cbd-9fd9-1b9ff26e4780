<?php

namespace Database\Seeders;

use App\Models\QuizQuestionLevel;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class QuizQuestionLevelSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $question_level =
            [
                [
                    'id' => 1,
                    'name' => 'Easy',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'id' => 2,
                    'name' => 'Normal',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'id' => 3,
                    'name' => 'Hard',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'id' => 4,
                    'name' => 'Extreme',
                    'created_at' => now(),
                    'updated_at' => now()
                ]
            ];

        Schema::disableForeignKeyConstraints();
        QuizQuestionLevel::truncate();
        Schema::enableForeignKeyConstraints();

        $chunked_question_levels = array_chunk($question_level, 5);


        foreach ($chunked_question_levels as $value) {
            QuizQuestionLevel::insert($value);
        }
    }
}
