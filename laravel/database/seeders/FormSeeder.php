<?php

namespace Database\Seeders;

use App\Form;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Schema;

class FormSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $forms = [
            [
                'id' => '1',
                'form' => 'users',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id' => '2',
                'form' => 'brands',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id' => '3',
                'form' => 'classifications',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id' => '4',
                'form' => 'distributors',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id' => '5',
                'form' => 'families',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id' => '6',
                'form' => 'manufacturers',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id' => '7',
                'form' => 'pricetypes',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id' => '8',
                'form' => 'products',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id' => '9',
                'form' => 'producttypes',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id' => '10',
                'form' => 'countries',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id' => '11',
                'form' => 'currencies',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id' => '12',
                'form' => 'division_types',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id' => '13',
                'form' => 'lines',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id' => '14',
                'form' => 'line_division_types',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id' => '15',
                'form' => 'line_divisions',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id' => '16',
                'form' => 'line_div_parents',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id' => '17',
                'form' => 'line_products',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id' => '18',
                'form' => 'line_users',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id' => '19',
                'form' => 'user_details',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id' => '20',
                'form' => 'line_user_divisions',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id' => '21',
                'form' => 'product_brands',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id' => '22',
                'form' => 'product_manufacturers',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id' => '23',
                'form' => 'product_prices',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id' => '24',
                'form' => 'error_messages',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id' => '25',
                'form' => 'permissions',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id' => '26',
                'form' => 'roles',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id' => '27',
                'form' => 'settings',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id' => '28',
                'form' => 'modules',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id' => '29',
                'form' => 'classes',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id' => '30',
                'form' => 'line_classes',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id' => '31',
                'form' => 'bricks',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id' => '32',
                'form' => 'line_bricks',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id' => '33',
                'form' => 'specialities',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id' => '34',
                'form' => 'line_specialities',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id' => '35',
                'form' => 'shifts',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id' => '36',
                'form' => 'account_types',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id' => '37',
                'form' => 'accounts',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id' => '38',
                'form' => 'account_lines',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id' => '39',
                'form' => 'doctors',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id' => '40',
                'form' => 'account_doctors',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id' => '41',
                'form' => 'levels',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id' => '42',
                'form' => 'personalitytypes',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id' => '43',
                'form' => 'imports',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id' => '44',
                'form' => 'socials',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id' => '45',
                'form' => 'doctor_socials',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id' => '46',
                'form' => 'account_socials',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id' => '47',
                'form' => 'reports',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id' => '48',
                'form' => 'class_frequencies',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id' => '49',
                'form' => 'plan_visits',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id' => '50',
                'form' => 'actual_visits',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id' => '51',
                'form' => 'plan_visit_settings',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id' => '52',
                'form' => 'actual_visit_settings',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id' => '53',
                'form' => 'start_plan_day_users',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id' => '54',
                'form' => 'start_actual_day_users',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id' => '55',
                'form' => 'plan_visit_columns',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id' => '56',
                'form' => 'visit_feedbacks',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id' => '57',
                'form' => 'giveaways',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id' => '58',
                'form' => 'vacation_types',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id' => '59',
                'form' => 'public_holidays',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id' => '60',
                'form' => 'av_required_inputs',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id' => '61',
                'form' => 'office_work_types',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id' => '62',
                'form' => 'calendar',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id' => '63',
                'form' => 'vacations',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id' => '64',
                'form' => 'ow_plan_visits',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id' => '65',
                'form' => 'ow_actual_visits',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id' => '66',
                'form' => 'line_giveaways',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id' => '67',
                'form' => 'visits_widgets',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id' => '68',
                'form' => 'sales_widgets',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id' => '69',
                'form' => 'sales_settings',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id' => '70',
                'form' => 'sales_mapping',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id' => '71',
                'form' => 'contribution',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id' => '72',
                'form' => 'target',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id' => '73',
                'form' => 'target_details',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id' => '74',
                'form' => 'sales',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id' => '75',
                'form' => 'unified_codes',
                'created_at' => now(),
                'updated_at' => now()
            ],

            // positions module forms

            [
                'id' => '86',
                'form' => 'positions',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id' => '87',
                'form' => 'employee_positions',
                'created_at' => now(),
                'updated_at' => now()
            ],

            // log activity
            [
                'id' => '88',
                'form' => 'log_activities',
                'created_at' => now(),
                'updated_at' => now()
            ],

            // Reports
            [
                'id' => '89',
                'form' => 'general_reports',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id' => '90',
                'form' => 'sales_reports',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id' => '91',
                'form' => 'visits_reports',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id' => '92',
                'form' => 'gps_reports',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id' => '93',
                'form' => 'coaching_reports',
                'created_at' => now(),
                'updated_at' => now()
            ],

            // profile
            // profile forms
            [
                'id' => '94',
                'form' => 'profile_data',
                'created_at' => now(),
                'updated_at' => now()
            ],

            //help form
            [
                'id' => '95',
                'form' => 'help',
                'created_at' => now(),
                'updated_at' => now()
            ],
            //reports
            [
                'id' => '96',
                'form' => 'reports',
                'created_at' => now(),
                'updated_at' => now()
            ],


            // Notes
            [
                'id' => '99',
                'form' => 'notes',
                'created_at' => now(),
                'updated_at' => now()
            ],

            // download templates

            [
                'id' => '100',
                'form' => 'export_files',
                'created_at' => now(),
                'updated_at' => now()
            ],

            // Mail Sender
            [
                'id' => '101',
                'form' => 'mail_sender',
                'created_at' => now(),
                'updated_at' => now()
            ],

            // // Tasks
            // [
            //     'id' => '102',
            //     'form' => 'Tasks',
            //     'created_at' => now(),
            //     'updated_at' => now()
            // ],

            // Personal Requests
            [
                'id' => '103',
                'form' => 'personal_requests',
                'created_at' => now(),
                'updated_at' => now()
            ],

            // personal request types
            [
                'id' => '104',
                'form' => 'personal_request_types',
                'created_at' => now(),
                'updated_at' => now()
            ],



            // position setting
            [
                'id' => '107',
                'form' => 'position_settings',
                'created_at' => now(),
                'updated_at' => now()
            ],
            // position manager
            [
                'id' => '108',
                'form' => 'position_managers',
                'created_at' => now(),
                'updated_at' => now()
            ],

            // articles
            [
                'id' => '109',
                'form' => 'articles',
                'created_at' => now(),
                'updated_at' => now()
            ],
            // Main Topics
            [
                'id' => '110',
                'form' => 'main_topics',
                'created_at' => now(),
                'updated_at' => now()
            ],
            // Sub Topics
            [
                'id' => '111',
                'form' => 'sub_topics',
                'created_at' => now(),
                'updated_at' => now()
            ],
            // Keywords
            [
                'id' => '112',
                'form' => 'keywords',
                'created_at' => now(),
                'updated_at' => now()
            ],

            // Topics
            [
                'id' => '113',
                'form' => 'topics',
                'created_at' => now(),
                'updated_at' => now()
            ],

            // cost types
            [
                'id' => '114',
                'form' => 'cost_types',
                'created_at' => now(),
                'updated_at' => now()
            ],

            // request types
            [
                'id' => '115',
                'form' => 'request_types',
                'created_at' => now(),
                'updated_at' => now()
            ],

            // commercial and branding
            [
                'id' => '116',
                'form' => 'commercial_request',
                'created_at' => now(),
                'updated_at' => now()
            ],

            // expense types
            [
                'id' => '117',
                'form' => 'expense_types',
                'created_at' => now(),
                'updated_at' => now()
            ],
            // expenses
            [
                'id' => '118',
                'form' => 'expenses',
                'created_at' => now(),
                'updated_at' => now()
            ],

            // // expenses
            // [
            //     'id' => '119',
            //     'form' => 'announcements',
            //     'created_at' => now(),
            //     'updated_at' => now()
            // ],

            // company details
            [
                'id' => '120',
                'form' => 'company_details',
                'created_at' => now(),
                'updated_at' => now()
            ],


            // call rate
            [
                'id' => '121',
                'form' => 'call_rate',
                'created_at' => now(),
                'updated_at' => now()
            ],

            // speciality_frequency
            [
                'id' => '122',
                'form' => 'specialities_frequency',
                'created_at' => now(),
                'updated_at' => now()
            ],

            // doctor frequency
            [
                'id' => '123',
                'form' => 'doctor_frequency',
                'created_at' => now(),
                'updated_at' => now()
            ],

            // disapproval plan reasons
            [
                'id' => '124',
                'form' => 'approve_and_disapprove_plan_reasons',
                'created_at' => now(),
                'updated_at' => now()
            ],

            // double plan
            [
                'id' => '125',
                'form' => 'double_plans',
                'created_at' => now(),
                'updated_at' => now()
            ],

            // product speciality
            [
                'id' => '126',
                'form' => 'product_specialities',
                'created_at' => now(),
                'updated_at' => now()
            ],

            // product offers
            [
                'id' => '127',
                'form' => 'product_offers',
                'created_at' => now(),
                'updated_at' => now()
            ],


            // supports
            [
                'id' => '128',
                'form' => 'supports',
                'created_at' => now(),
                'updated_at' => now()
            ],

            // mails
            [
                'id' => '129',
                'form' => 'mails',
                'created_at' => now(),
                'updated_at' => now()
            ],


            // coaching types
            [
                'id' => '130',
                'form' => 'types',
                'created_at' => now(),
                'updated_at' => now()
            ],
            // coaching categories
            [
                'id' => '131',
                'form' => 'categories',
                'created_at' => now(),
                'updated_at' => now()
            ],

            // coaching questions
            [
                'id' => '132',
                'form' => 'questions',
                'created_at' => now(),
                'updated_at' => now()
            ],
            // coaching answers
            [
                'id' => '133',
                'form' => 'answers',
                'created_at' => now(),
                'updated_at' => now()
            ],

            // list type

            [
                'id' => '134',
                'form' => 'list_type_settings',
                'created_at' => now(),
                'updated_at' => now()
            ],

            // sidebar menu
            [
                'id' => '135',
                'form' => 'menu',
                'created_at' => now(),
                'updated_at' => now()
            ],

            // Sidebar Submenu items
            [
                'id' => '144',
                'form' => 'communication_menu',
                'created_at' => now(),
                'updated_at' => now()
            ],

            [
                'id' => '145',
                'form' => 'visits_menu',
                'created_at' => now(),
                'updated_at' => now()
            ],

            [
                'id' => '146',
                'form' => 'requests_menu',
                'created_at' => now(),
                'updated_at' => now()
            ],

            [
                'id' => '147',
                'form' => 'sales_menu',
                'created_at' => now(),
                'updated_at' => now()
            ],

            [
                'id' => '148',
                'form' => 'training_menu',
                'created_at' => now(),
                'updated_at' => now()
            ],

            [
                'id' => '149',
                'form' => 'tools_menu',
                'created_at' => now(),
                'updated_at' => now()
            ],

            [
                'id' => '150',
                'form' => 'reports_menu',
                'created_at' => now(),
                'updated_at' => now()
            ],

            // setting_links

            [
                'id' => '97',
                'form' => 'main_settings',
                'created_at' => now(),
                'updated_at' => now()
            ],

            [
                'id' => '151',
                'form' => 'settings_general',
                'created_at' => now(),
                'updated_at' => now()
            ],

            [
                'id' => '152',
                'form' => 'settings_user',
                'created_at' => now(),
                'updated_at' => now()
            ],

            [
                'id' => '153',
                'form' => 'settings_structure',
                'created_at' => now(),
                'updated_at' => now()
            ],

            [
                'id' => '154',
                'form' => 'settings_dashboard',
                'created_at' => now(),
                'updated_at' => now()
            ],

            [
                'id' => '155',
                'form' => 'settings_calendar',
                'created_at' => now(),
                'updated_at' => now()
            ],

            [
                'id' => '156',
                'form' => 'settings_visits',
                'created_at' => now(),
                'updated_at' => now()
            ],

            [
                'id' => '157',
                'form' => 'settings_requests',
                'created_at' => now(),
                'updated_at' => now()
            ],

            [
                'id' => '158',
                'form' => 'settings_sales',
                'created_at' => now(),
                'updated_at' => now()
            ],

            [
                'id' => '159',
                'form' => 'settings_training',
                'created_at' => now(),
                'updated_at' => now()
            ],

            [
                'id' => '160',
                'form' => 'settings_positions',
                'created_at' => now(),
                'updated_at' => now()
            ],

            [
                'id' => '161',
                'form' => 'settings_coaching',
                'created_at' => now(),
                'updated_at' => now()
            ],

            // [
            //     'id' => '162',
            //     'form' => 'communication_live',
            //     'created_at' => now(),
            //     'updated_at' => now()
            // ],


        ];
        Schema::disableForeignKeyConstraints();
        Form::truncate();
        Schema::enableForeignKeyConstraints();
        $forms_chunk = array_chunk($forms, 20);

        foreach ($forms_chunk as $value) {
            Form::insert($value);
        }
    }
}
