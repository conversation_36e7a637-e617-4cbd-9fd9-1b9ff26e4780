<?php

namespace Database\Seeders;

use App\PlanVisitColumn;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Schema;

class PlanVisitColumnSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $columns =
            [
                [
                    'name' => 'brick',
                    'select' => 0,
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'name' => 'speciality',
                    'select' => 0,
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'name' => 'class',
                    'select' => 0,
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'name' => 'account_type',
                    'select' => 0,
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'name' => 'monthly_plan',
                    'select' => 0,
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'name' => 'monthly_actual',
                    'select' => 0,
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'name' => 'frequency',
                    'select' => 0,
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'name' => 'follow_up',
                    'select' => 0,
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'name' => 'pharmacies',
                    'select' => 0,
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'name' => 'group_name',
                    'select' => 0,
                    'created_at' => now(),
                    'updated_at' => now()
                ],
            ];

        Schema::disableForeignKeyConstraints();
        PlanVisitColumn::truncate();
        Schema::enableForeignKeyConstraints();

        $chunked_columns = array_chunk($columns, 5);


        foreach ($chunked_columns as $value) {
            PlanVisitColumn::insert($value);
        }
    }
}
