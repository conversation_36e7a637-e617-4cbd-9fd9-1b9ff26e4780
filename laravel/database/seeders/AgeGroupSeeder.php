<?php

namespace Database\Seeders;

use App\Models\PV\AgeGroup;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Schema;

class AgeGroupSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $age_groups =
            [
                [
                    'id' => 1,
                    'name' => 'Infant (0-12 Month)',
                    'notes' => 'Infant',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'id' => 2,
                    'name' => 'Child (1-12 Y)',
                    'notes' => 'Child',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'id' => 3,
                    'name' => 'Adolescent (13-16 Y)',
                    'notes' => 'Adolescent',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'id' => 4,
                    'name' => 'Adult (17-64 Y)',
                    'notes' => 'Adult',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'id' => 5,
                    'name' => 'Elderly (65 Y and above)',
                    'notes' => 'Elderly',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
            ];

        Schema::disableForeignKeyConstraints();
        AgeGroup::truncate();
        Schema::enableForeignKeyConstraints();

        $chunked_age_groups = array_chunk($age_groups, 5);


        foreach ($chunked_age_groups as $value) {
            AgeGroup::insert($value);
        }
    }
}
