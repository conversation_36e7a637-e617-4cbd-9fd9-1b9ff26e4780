<?php

namespace Database\Seeders;

use App\Models\PV\Qualification;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Schema;

class DoctorQualificationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $qualifications =
            [
                [
                    'id' => 1,
                    'name' => 'Nurse',
                    'notes' => 'Nurse',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'id' => 2,
                    'name' => 'Physician',
                    'notes' => 'Physician',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'id' => 3,
                    'name' => 'Pharmacist',
                    'notes' => 'Pharmacist',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'id' => 4,
                    'name' => 'Other',
                    'notes' => 'Other',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
            ];

        Schema::disableForeignKeyConstraints();
        Qualification::truncate();
        Schema::enableForeignKeyConstraints();

        $chunked_qualifications = array_chunk($qualifications, 5);


        foreach ($chunked_qualifications as $value) {
            Qualification::insert($value);
        }
    }
}
