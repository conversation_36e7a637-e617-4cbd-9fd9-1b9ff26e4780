<?php

namespace Database\Seeders;

use App\Models\UnifiedPharmacyType;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Schema;

class UnifiedPharmacyTypeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $unifiedTypes =
            [
                [
                    'id' => 1,
                    'name' => 'PRIVATE PHARMACIES',
                    'sort' => 100,
                ],
                [
                    'id' => 2,
                    'name' => 'MINISTRY OF HEALTH',
                    'sort' => 200,
                ],
                [
                    'id' => 3,
                    'name' => 'NATIONAL HEALTH INSURANCE',
                    'sort' => 300,
                ],
                [
                    'id' => 4,
                    'name' => 'EDUCATIONAL HOSPITALS',
                    'sort' => 400,
                ],
                [
                    'id' => 5,
                    'name' => 'TREATING HOSPITALS',
                    'sort' => 500,
                ],
                [
                    'id' => 6,
                    'name' => 'POLY CLINICS',
                    'sort' => 600,
                ],
                [
                    'id' => 7,
                    'name' => 'PRIVATE CLINICS',
                    'sort' => 700,
                ],
                [
                    'id' => 8,
                    'name' => 'PRIVATE HOSPITALS',
                    'sort' => 800,
                ],
                [
                    'id' => 9,
                    'name' => 'UNIVERSETY HOSPITALS',
                    'sort' => 900,
                ],
                [
                    'id' => 10,
                    'name' => 'OTHER HOSTPITALS " Army, Police, ... ETC"',
                    'sort' => 1000,
                ],
                [
                    'id' => 11,
                    'name' => 'OTHERS " BANKS , COMPANIES … ETC"',
                    'sort' => 1100,
                ],
                [
                    'id' => 12,
                    'name' => 'EGY DRUG PHARAMCIES',
                    'sort' => 1200,
                ],
                [
                    'id' => 13,
                    'name' => 'LIVER INSTITUTES',
                    'sort' => 1300,
                ],
                [
                    'id' => 14,
                    'name' => 'ARMY HOSPITALS',
                    'sort' => 1400,
                ],
                [
                    'id' => 15,
                    'name' => 'POLICE HOSPITALS',
                    'sort' => 1500,
                ],
                [
                    'id' => 16,
                    'name' => 'MEDICINE STORES',
                    'sort' => 1600,
                ],
                [
                    'id' => 17,
                    'name' => 'CHAIN PHARMACIES',
                    'sort' => 1700,
                ],
                [
                    'id' => 18,
                    'name' => 'PHARMACY CONTRACTED WITH HEALTH INSURANCE',
                    'sort' => 1800,
                ],
                
                [
                    'id' => 19,
                    'name' => 'LOCAL CHAIN',
                    'sort' => 1900,
                ],
            ];

        Schema::disableForeignKeyConstraints();
        UnifiedPharmacyType::truncate();
        Schema::enableForeignKeyConstraints();

        $chunked_unifiedTypes = array_chunk($unifiedTypes, 20);


        foreach ($chunked_unifiedTypes as $value) {
            UnifiedPharmacyType::insert($value);
        }
    }
}
