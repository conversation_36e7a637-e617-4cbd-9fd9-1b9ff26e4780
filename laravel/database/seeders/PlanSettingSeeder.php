<?php

namespace Database\Seeders;

use App\PlanSetting;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Schema;

class PlanSettingSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $plan_settings =
            [
                [
                    'name' => 'Start Plan Day',
                    'key' => 'start_plan_day',
                    'value' => 'today',
                    'type' => 'select',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'name' => 'Plan Level',
                    'key' => 'plan_level',
                    'value' => 'Doctor',
                    'type' => 'select',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'name' => 'Delete Plan',
                    'key' => 'delete_plan',
                    'value' => 'Before Approval',
                    'type' => 'select',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'name' => 'Plan Visit Specific Start Day',
                    'key' => 'specific_plan_start_day',
                    'value' => '',
                    'type' => 'date',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'name' => 'Daily Plan Visit',
                    'key' => 'daily_plan_visit',
                    'value' => 'no',
                    'type' => 'select',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'name' => 'Plan SHift',
                    'key' => 'plan_shift',
                    'value' => 'yes',
                    'type' => 'select',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'name' => 'Plan Time',
                    'key' => 'plan_time',
                    'value' => 'yes',
                    'type' => 'select',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'name' => 'If Plan Disapproved user can\'t visit Doctor as Unplanned Visit at same Day',
                    'key' => 'plan_disapproved_close_actual',
                    'value' => 'no',
                    'type' => 'select',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'name' => 'Delete Plans If User Create Vacation',
                    'key' => 'delete_plans_if_user_create_vacation',
                    'value' => 'no',
                    'type' => 'select',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'name' => 'Delete Plans If Manager disapprove them',
                    'key' => 'delete_plans_if_manager_disapprove',
                    'value' => 'no',
                    'type' => 'select',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'name' => 'Close Plan If User Meet Frequency',
                    'key' => 'close_plan_if_user_meet_frequency',
                    'value' => 'no',
                    'type' => 'select',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'name' => 'Accept Officework With Plans',
                    'key' => 'accept_officework_with_plans',
                    'value' => 'yes',
                    'type' => 'select',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'name' => 'Delete Plans at public holidays',
                    'key' => 'delete_plans_at_public_holidays',
                    'value' => 'no',
                    'type' => 'select',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'name' => 'Convert Plan Ow Within Approval',
                    'key' => 'convert_plan_ow_within_approval',
                    'value' => 'no',
                    'type' => 'select',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'name' => 'Delete Plans If User Create OW',
                    'key' => 'delete_plans_if_user_create_ow',
                    'value' => 'no',
                    'type' => 'select',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'name' => 'Plan Limit',
                    'key' => 'plan_limit',
                    'value' => 'no',
                    'type' => 'select',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'name' => 'Mendatory Linked Pharmacies',
                    'key' => 'linked_pharmacy_required',
                    'value' => 'no',
                    'type' => 'select',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
            ];

        Schema::disableForeignKeyConstraints();
        PlanSetting::truncate();
        Schema::enableForeignKeyConstraints();

        $chunked_plan_settings = array_chunk($plan_settings, 5);


        foreach ($chunked_plan_settings as $value) {
            PlanSetting::insert($value);
        }
    }
}
