<?php

namespace Database\Seeders;
use Illuminate\Database\Seeder;
use App\SalesTypes;
class SalesTypesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $row = SalesTypes::create([
            'name' => 'Bricks',
            'created_at' => now(),
            'updated_at' => now()
        ]);
        $row = SalesTypes::create([
            'name' => 'Pharmacies',
            'created_at' => now(),
            'updated_at' => now()
        ]);
        $row = SalesTypes::create([
            'name' => 'Branches',
            'created_at' => now(),
            'updated_at' => now()
        ]);
    }
}
