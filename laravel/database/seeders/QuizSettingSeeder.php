<?php

namespace Database\Seeders;

use App\Models\QuizSetting;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Schema;

class QuizSettingSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {

        $quiz_settings =
            [
                [
                    'name' => 'Show Answers for Quiz',
                    'key' => 'show_answers',
                    'value' => 'Yes',
                    'type' => 'select',
                    'options' => json_encode(["values" => ['Yes', 'No']]),
                ],
            ];

        Schema::disableForeignKeyConstraints();
        QuizSetting::truncate();
        Schema::enableForeignKeyConstraints();

        $chunked_quiz_settings = array_chunk($quiz_settings, 5);


        foreach ($chunked_quiz_settings as $value) {
            QuizSetting::insert($value);
        }
    }
}
