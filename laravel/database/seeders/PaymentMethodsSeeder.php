<?php

namespace Database\Seeders;

use App\Models\PaymentMethod;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Schema;

class PaymentMethodsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $payment_methods =
            [
                [
                    'name' => 'Marketing Expense',
                    'sort' => '100',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'name' => 'Direct Settlement',
                    'sort' => '200',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'name' => 'Marketing Custody',
                    'sort' => '300',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'name' => 'Sales Custody',
                    'sort' => '400',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
            ];

        Schema::disableForeignKeyConstraints();
        PaymentMethod::truncate();
        Schema::enableForeignKeyConstraints();

        $chunked_payment_methods = array_chunk($payment_methods, 5);


        foreach ($chunked_payment_methods as $value) {
            PaymentMethod::insert($value);
        }
    }
}
