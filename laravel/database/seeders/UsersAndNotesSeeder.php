<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use App\User;
use App\Models\RoleHierarchy;
use App\Models\UnhashedPassword;
use App\Role;
use Illuminate\Support\Facades\Schema;

class UsersAndNotesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        Schema::disableForeignKeyConstraints();
        $tableNames = config('permission.table_names');
        DB::table($tableNames['model_has_roles'])->truncate();
        DB::table($tableNames['model_has_permissions'])->truncate();
        User::truncate();
        RoleHierarchy::truncate();
        Schema::enableForeignKeyConstraints();

        /* Create roles */
        $adminRole = Role::firstOrCreate(['name' => 'admin']);
        $adminRole->setRoleHierarchy();

        $userRole = Role::firstOrCreate(['name' => 'user']);
        $userRole->setRoleHierarchy();

        $guestRole = Role::firstOrCreate(['name' => 'guest']);
        $guestRole->setRoleHierarchy();

        /*  insert users   */
        $user = User::create([
            'name' => 'admin',
            'fullname' => 'Puplo Admin',
            'email' => '<EMAIL>',
            'email_verified_at' => now(),
            'password' => '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', // password
            'remember_token' => Str::random(10),
            'menuroles' => 'user,admin',
            'status' => 'Active'
        ]);
        $user->assignRole('admin');
        $user->assignRole('user');

        $adminRole->givePermissionTo('all_permissions');
        UnhashedPassword::create([
            'user_id' => $user->id,
            'password' => 'password'
        ]);
    }
}
