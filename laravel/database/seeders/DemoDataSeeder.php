<?php

namespace Database\Seeders;

use App\DivisionType;
use App\Models\Coaching\Evaluator;
use App\Role;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Schema;

class DemoDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {

        $countryDivisionType = DivisionType::firstOrCreate([
            'name' => 'Country',
            'notes' => 'Country',
            'level' => 1,
            'sort' => '100',
            'color' => 'black',
            'padding' => '0',
            'key' => 'C',
        ]);

        //'name', 'notes', 'sort'
        $areaDivisionType = DivisionType::firstOrCreate([
            'name' => 'Area',
            'notes' => 'Area',
            'level' => 2,
            'sort' => '200',
            'color' => 'red',
            'padding' => '1.5',
            'key' => 'A',
            'parent_id' => $countryDivisionType->id,
        ]);

        //'name', 'notes', 'sort'
        $districtDivisionType = DivisionType::firstOrCreate([
            'name' => 'District',
            'notes' => 'District',
            'level' => 3,
            'sort' => '300',
            'color' => 'blue',
            'padding' => '3',
            'key' => 'D',
            'parent_id' => $areaDivisionType->id,
        ]);

        //'name', 'notes', 'sort'
        $territoryDivisionType = DivisionType::firstOrCreate([
            'name' => 'Territory',
            'notes' => 'Territory',
            'level' => 4,
            'sort' => '400',
            'color' => 'green',
            'padding' => '4.5',
            'key' => 'T',
            'parent_id' => $districtDivisionType->id,
            'last_level' => 1,
        ]);

        Role::firstOrCreate([
            'name' => 'Country Manager',
            'guard_name' => 'api',
            'created_at' => now(),
            'updated_at' => now()
        ]);
        Role::firstOrCreate([
            'name' => 'Area Manager',
            'guard_name' => 'api',
            'created_at' => now(),
            'updated_at' => now()
        ]);
        Role::firstOrCreate([
            'name' => 'District Manager',
            'guard_name' => 'api',
            'created_at' => now(),
            'updated_at' => now()
        ]);
        Role::firstOrCreate([
            'name' => 'Medical Rep',
            'guard_name' => 'api',
            'created_at' => now(),
            'updated_at' => now()
        ]);

        // coaching evaluators corresponding to division types demo data

        // Country
        Evaluator::firstOrCreate([
            'name' => 'Country',
            'evaluatorable_id' => $countryDivisionType->id,
            'evaluatorable_type' => get_class($countryDivisionType),
        ]);

        // Area
        Evaluator::firstOrCreate([
            'name' => 'Area',
            'evaluatorable_id' => $areaDivisionType->id,
            'evaluatorable_type' => get_class($areaDivisionType),
        ]);

        // District
        Evaluator::firstOrCreate([
            'name' => 'District',
            'evaluatorable_id' => $districtDivisionType->id,
            'evaluatorable_type' => get_class($districtDivisionType),
        ]);
    }
}
