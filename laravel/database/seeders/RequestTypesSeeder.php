<?php
namespace Database\Seeders;
use App\RequestType;
use Illuminate\Database\Seeder;

class RequestTypesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        if (!RequestType::where('key', 'request_type')->first()) {
            RequestType::firstOrCreate([
                'name' => 'Request Type',
                'key' => 'request_type',
                'value' => 'single', 
                'type' => 'select',
                'created_at' => now(),
                'updated_at' => now()
            ]);
        }
    }
}
