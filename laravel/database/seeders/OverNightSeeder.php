<?php

namespace Database\Seeders;

use App\Models\OverNight;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Schema;

class OverNightSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $OverNights =
            [
                [
                    'id' => 1,
                    'name' => 'Same Day',
                    'sort' => '100',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'id' => 2,
                    'name' => 'Over Night',
                    'sort' => '200',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
            ];

        Schema::disableForeignKeyConstraints();
        OverNight::truncate();
        Schema::enableForeignKeyConstraints();

        $chunked_OverNights = array_chunk($OverNights, 5);


        foreach ($chunked_OverNights as $value) {
            OverNight::insert($value);
        }
    }
}
