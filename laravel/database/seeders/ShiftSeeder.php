<?php

namespace Database\Seeders;

use App\Shift;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Schema;

class ShiftSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $shifts =
            [
                [
                    'name' => 'AM',
                    'sort' => 100
                ],
                [
                    'name' => 'PM',
                    'sort' => 200
                ],
            ];

        Schema::disableForeignKeyConstraints();
        Shift::truncate();
        Schema::enableForeignKeyConstraints();

        $chunked_shifts = array_chunk($shifts, 5);


        foreach ($chunked_shifts as $value) {
            Shift::insert($value);
        }
    }
}
