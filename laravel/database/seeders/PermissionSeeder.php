<?php

namespace Database\Seeders;

use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class PermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $permissions = [
            [
                'name'          => 'all_permissions',
                'guard_name'    => 'api',
                'form_id'       => null,
                'action_id'     => null,
                'module_id'     => null,
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // Users
            [
                'name'          => 'show_all_users',
                'guard_name'    => 'api',
                'form_id'       => '1',
                'action_id'     => '1',
                'module_id'     => '1',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'create_users',
                'guard_name'    => 'api',
                'form_id'       => '1',
                'action_id'     => '2',
                'module_id'     => '1',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_single_users',
                'guard_name'    => 'api',
                'form_id'       => '1',
                'action_id'     => '3',
                'module_id'     => '1',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_users',
                'guard_name'    => 'api',
                'form_id'       => '1',
                'action_id'     => '4',
                'module_id'     => '1',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'delete_users',
                'guard_name'    => 'api',
                'form_id'       => '1',
                'action_id'     => '5',
                'module_id'     => '1',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'restore_users',
                'guard_name'    => 'api',
                'form_id'       => '1',
                'action_id'     => '6',
                'module_id'     => '1',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'show_all_archive_users',
                'guard_name'    => 'api',
                'form_id'       => '1',
                'action_id'     => '7',
                'module_id'     => '1',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'import_users',
                'guard_name'    => 'api',
                'form_id'       => '1',
                'action_id'     => '10',
                'module_id'     => '1',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_xlsx_users',
                'guard_name'    => 'api',
                'form_id'       => '1',
                'action_id'     => '11',
                'module_id'     => '1',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_csv_users',
                'guard_name'    => 'api',
                'form_id'       => '1',
                'action_id'     => '22',
                'module_id'     => '1',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_pdf_users',
                'guard_name'    => 'api',
                'form_id'       => '1',
                'action_id'     => '12',
                'module_id'     => '1',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_email_users',
                'guard_name'    => 'api',
                'form_id'       => '1',
                'action_id'     => '13',
                'module_id'     => '1',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'login',
                'guard_name'    => 'api',
                'form_id'       => '1',
                'action_id'     => '16',
                'module_id'     => '1',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'logout',
                'guard_name'    => 'api',
                'form_id'       => '1',
                'action_id'     => '17',
                'module_id'     => '1',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_view_user',
                'guard_name'    => 'api',
                'form_id'       => '1',
                'action_id'     => '18',
                'module_id'     => '1',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // brands

            [
                'name'          => 'show_all_brands',
                'guard_name'    => 'api',
                'form_id'       => '2',
                'action_id'     => '1',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'create_brands',
                'guard_name'    => 'api',
                'form_id'       => '2',
                'action_id'     => '2',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_single_brands',
                'guard_name'    => 'api',
                'form_id'       => '2',
                'action_id'     => '3',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_brands',
                'guard_name'    => 'api',
                'form_id'       => '2',
                'action_id'     => '4',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'delete_brands',
                'guard_name'    => 'api',
                'form_id'       => '2',
                'action_id'     => '5',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'restore_brands',
                'guard_name'    => 'api',
                'form_id'       => '2',
                'action_id'     => '6',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_all_archive_brands',
                'guard_name'    => 'api',
                'form_id'       => '2',
                'action_id'     => '7',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ],  [
                'name'          => 'import_brands',
                'guard_name'    => 'api',
                'form_id'       => '2',
                'action_id'     => '10',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_xlsx_brands',
                'guard_name'    => 'api',
                'form_id'       => '2',
                'action_id'     => '11',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_csv_brands',
                'guard_name'    => 'api',
                'form_id'       => '2',
                'action_id'     => '22',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_pdf_brands',
                'guard_name'    => 'api',
                'form_id'       => '2',
                'action_id'     => '12',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_email_brands',
                'guard_name'    => 'api',
                'form_id'       => '2',
                'action_id'     => '13',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_view_brand',
                'guard_name'    => 'api',
                'form_id'       => '2',
                'action_id'     => '18',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // Classifications
            [
                'name'          => 'show_all_classifications',
                'guard_name'    => 'api',
                'form_id'       => '3',
                'action_id'     => '1',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'create_classifications',
                'guard_name'    => 'api',
                'form_id'       => '3',
                'action_id'     => '2',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_single_classifications',
                'guard_name'    => 'api',
                'form_id'       => '3',
                'action_id'     => '3',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_classifications',
                'guard_name'    => 'api',
                'form_id'       => '3',
                'action_id'     => '4',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'delete_classifications',
                'guard_name'    => 'api',
                'form_id'       => '3',
                'action_id'     => '5',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'restore_classifications',
                'guard_name'    => 'api',
                'form_id'       => '3',
                'action_id'     => '6',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_all_archive_classifications',
                'guard_name'    => 'api',
                'form_id'       => '3',
                'action_id'     => '7',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'import_classifications',
                'guard_name'    => 'api',
                'form_id'       => '3',
                'action_id'     => '10',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_xlsx_classifications',
                'guard_name'    => 'api',
                'form_id'       => '3',
                'action_id'     => '11',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_csv_classifications',
                'guard_name'    => 'api',
                'form_id'       => '3',
                'action_id'     => '22',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_pdf_classifications',
                'guard_name'    => 'api',
                'form_id'       => '3',
                'action_id'     => '12',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_email_classifications',
                'guard_name'    => 'api',
                'form_id'       => '3',
                'action_id'     => '13',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_view_classification',
                'guard_name'    => 'api',
                'form_id'       => '3',
                'action_id'     => '18',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // Distributors
            [
                'name'          => 'show_all_distributors',
                'guard_name'    => 'api',
                'form_id'       => '4',
                'action_id'     => '1',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'show_all_distributor_lines',
                'guard_name'    => 'api',
                'form_id'       => '4',
                'action_id'     => '1',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'show_all_distributor_products',
                'guard_name'    => 'api',
                'form_id'       => '4',
                'action_id'     => '1',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'create_distributors',
                'guard_name'    => 'api',
                'form_id'       => '4',
                'action_id'     => '2',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'create_distributor_lines',
                'guard_name'    => 'api',
                'form_id'       => '4',
                'action_id'     => '2',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'create_distributor_products',
                'guard_name'    => 'api',
                'form_id'       => '4',
                'action_id'     => '2',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'show_single_distributors',
                'guard_name'    => 'api',
                'form_id'       => '4',
                'action_id'     => '3',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'show_single_distributor_lines',
                'guard_name'    => 'api',
                'form_id'       => '4',
                'action_id'     => '3',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'show_single_distributor_products',
                'guard_name'    => 'api',
                'form_id'       => '4',
                'action_id'     => '3',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_distributors',
                'guard_name'    => 'api',
                'form_id'       => '4',
                'action_id'     => '4',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_distributor_lines',
                'guard_name'    => 'api',
                'form_id'       => '4',
                'action_id'     => '4',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_distributor_products',
                'guard_name'    => 'api',
                'form_id'       => '4',
                'action_id'     => '4',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'delete_distributors',
                'guard_name'    => 'api',
                'form_id'       => '4',
                'action_id'     => '5',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'delete_distributor_lines',
                'guard_name'    => 'api',
                'form_id'       => '4',
                'action_id'     => '5',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'delete_distributor_products',
                'guard_name'    => 'api',
                'form_id'       => '4',
                'action_id'     => '5',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'restore_distributors',
                'guard_name'    => 'api',
                'form_id'       => '4',
                'action_id'     => '6',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'restore_distributor_lines',
                'guard_name'    => 'api',
                'form_id'       => '4',
                'action_id'     => '6',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'restore_distributor_products',
                'guard_name'    => 'api',
                'form_id'       => '4',
                'action_id'     => '6',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'show_all_archive_distributors',
                'guard_name'    => 'api',
                'form_id'       => '4',
                'action_id'     => '7',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'import_distributors',
                'guard_name'    => 'api',
                'form_id'       => '4',
                'action_id'     => '10',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_xlsx_distributors',
                'guard_name'    => 'api',
                'form_id'       => '4',
                'action_id'     => '11',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_csv_distributors',
                'guard_name'    => 'api',
                'form_id'       => '4',
                'action_id'     => '22',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_pdf_distributors',
                'guard_name'    => 'api',
                'form_id'       => '4',
                'action_id'     => '12',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_email_distributors',
                'guard_name'    => 'api',
                'form_id'       => '4',
                'action_id'     => '13',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_view_distributor',
                'guard_name'    => 'api',
                'form_id'       => '4',
                'action_id'     => '18',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // Families
            [
                'name'          => 'show_all_families',
                'guard_name'    => 'api',
                'form_id'       => '5',
                'action_id'     => '1',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'create_families',
                'guard_name'    => 'api',
                'form_id'       => '5',
                'action_id'     => '2',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_single_families',
                'guard_name'    => 'api',
                'form_id'       => '5',
                'action_id'     => '3',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_families',
                'guard_name'    => 'api',
                'form_id'       => '5',
                'action_id'     => '4',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'delete_families',
                'guard_name'    => 'api',
                'form_id'       => '5',
                'action_id'     => '5',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'restore_families',
                'guard_name'    => 'api',
                'form_id'       => '5',
                'action_id'     => '6',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_all_archive_families',
                'guard_name'    => 'api',
                'form_id'       => '5',
                'action_id'     => '7',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ],  [
                'name'          => 'import_families',
                'guard_name'    => 'api',
                'form_id'       => '5',
                'action_id'     => '10',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_xlsx_families',
                'guard_name'    => 'api',
                'form_id'       => '5',
                'action_id'     => '11',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_csv_families',
                'guard_name'    => 'api',
                'form_id'       => '5',
                'action_id'     => '22',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_pdf_families',
                'guard_name'    => 'api',
                'form_id'       => '5',
                'action_id'     => '12',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_email_families',
                'guard_name'    => 'api',
                'form_id'       => '5',
                'action_id'     => '13',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_view_family',
                'guard_name'    => 'api',
                'form_id'       => '5',
                'action_id'     => '18',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // Manufacturers
            [
                'name'          => 'show_all_manufacturers',
                'guard_name'    => 'api',
                'form_id'       => '6',
                'action_id'     => '1',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'create_manufacturers',
                'guard_name'    => 'api',
                'form_id'       => '6',
                'action_id'     => '2',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_single_manufacturers',
                'guard_name'    => 'api',
                'form_id'       => '6',
                'action_id'     => '3',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_manufacturers',
                'guard_name'    => 'api',
                'form_id'       => '6',
                'action_id'     => '4',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'delete_manufacturers',
                'guard_name'    => 'api',
                'form_id'       => '6',
                'action_id'     => '5',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'restore_manufacturers',
                'guard_name'    => 'api',
                'form_id'       => '6',
                'action_id'     => '6',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_all_archive_manufacturers',
                'guard_name'    => 'api',
                'form_id'       => '6',
                'action_id'     => '7',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ],  [
                'name'          => 'import_manufacturers',
                'guard_name'    => 'api',
                'form_id'       => '6',
                'action_id'     => '10',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_xlsx_manufacturers',
                'guard_name'    => 'api',
                'form_id'       => '6',
                'action_id'     => '22',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_csv_manufacturers',
                'guard_name'    => 'api',
                'form_id'       => '6',
                'action_id'     => '11',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_pdf_manufacturers',
                'guard_name'    => 'api',
                'form_id'       => '6',
                'action_id'     => '12',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_email_manufacturers',
                'guard_name'    => 'api',
                'form_id'       => '6',
                'action_id'     => '13',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_view_manufacturer',
                'guard_name'    => 'api',
                'form_id'       => '6',
                'action_id'     => '18',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // Price Types
            [
                'name'          => 'show_all_pricetypes',
                'guard_name'    => 'api',
                'form_id'       => '7',
                'action_id'     => '1',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'create_pricetypes',
                'guard_name'    => 'api',
                'form_id'       => '7',
                'action_id'     => '2',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_single_pricetypes',
                'guard_name'    => 'api',
                'form_id'       => '7',
                'action_id'     => '3',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_pricetypes',
                'guard_name'    => 'api',
                'form_id'       => '7',
                'action_id'     => '4',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'delete_pricetypes',
                'guard_name'    => 'api',
                'form_id'       => '7',
                'action_id'     => '5',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'restore_pricetypes',
                'guard_name'    => 'api',
                'form_id'       => '7',
                'action_id'     => '6',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_all_archive_pricetypes',
                'guard_name'    => 'api',
                'form_id'       => '7',
                'action_id'     => '7',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'import_pricetypes',
                'guard_name'    => 'api',
                'form_id'       => '7',
                'action_id'     => '10',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_xlsx_pricetypes',
                'guard_name'    => 'api',
                'form_id'       => '7',
                'action_id'     => '11',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_csv_pricetypes',
                'guard_name'    => 'api',
                'form_id'       => '7',
                'action_id'     => '22',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_pdf_pricetypes',
                'guard_name'    => 'api',
                'form_id'       => '7',
                'action_id'     => '12',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_email_pricetypes',
                'guard_name'    => 'api',
                'form_id'       => '7',
                'action_id'     => '13',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_view_pricetype',
                'guard_name'    => 'api',
                'form_id'       => '7',
                'action_id'     => '18',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // Products
            [
                'name'          => 'show_all_products',
                'guard_name'    => 'api',
                'form_id'       => '8',
                'action_id'     => '1',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'create_products',
                'guard_name'    => 'api',
                'form_id'       => '8',
                'action_id'     => '2',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_single_products',
                'guard_name'    => 'api',
                'form_id'       => '8',
                'action_id'     => '3',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_products',
                'guard_name'    => 'api',
                'form_id'       => '8',
                'action_id'     => '4',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'delete_products',
                'guard_name'    => 'api',
                'form_id'       => '8',
                'action_id'     => '5',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'restore_products',
                'guard_name'    => 'api',
                'form_id'       => '8',
                'action_id'     => '6',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_all_archive_products',
                'guard_name'    => 'api',
                'form_id'       => '8',
                'action_id'     => '7',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            [
                'name'          => 'import_products',
                'guard_name'    => 'api',
                'form_id'       => '8',
                'action_id'     => '10',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_xlsx_products',
                'guard_name'    => 'api',
                'form_id'       => '8',
                'action_id'     => '11',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_csv_products',
                'guard_name'    => 'api',
                'form_id'       => '8',
                'action_id'     => '22',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_pdf_products',
                'guard_name'    => 'api',
                'form_id'       => '8',
                'action_id'     => '12',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_email_products',
                'guard_name'    => 'api',
                'form_id'       => '8',
                'action_id'     => '13',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            [
                'name'          => 'edit_view_product',
                'guard_name'    => 'api',
                'form_id'       => '8',
                'action_id'     => '18',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // Product Types

            [
                'name'          => 'show_all_producttypes',
                'guard_name'    => 'api',
                'form_id'       => '9',
                'action_id'     => '1',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'create_producttypes',
                'guard_name'    => 'api',
                'form_id'       => '9',
                'action_id'     => '2',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_single_producttypes',
                'guard_name'    => 'api',
                'form_id'       => '9',
                'action_id'     => '3',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_producttypes',
                'guard_name'    => 'api',
                'form_id'       => '9',
                'action_id'     => '4',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'delete_producttypes',
                'guard_name'    => 'api',
                'form_id'       => '9',
                'action_id'     => '5',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'restore_producttypes',
                'guard_name'    => 'api',
                'form_id'       => '9',
                'action_id'     => '6',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_all_archive_producttypes',
                'guard_name'    => 'api',
                'form_id'       => '9',
                'action_id'     => '7',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ],  [
                'name'          => 'import_producttypes',
                'guard_name'    => 'api',
                'form_id'       => '9',
                'action_id'     => '10',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_xlsx_producttypes',
                'guard_name'    => 'api',
                'form_id'       => '9',
                'action_id'     => '11',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_csv_producttypes',
                'guard_name'    => 'api',
                'form_id'       => '9',
                'action_id'     => '22',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_pdf_producttypes',
                'guard_name'    => 'api',
                'form_id'       => '9',
                'action_id'     => '12',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_email_producttypes',
                'guard_name'    => 'api',
                'form_id'       => '9',
                'action_id'     => '13',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_view_producttype',
                'guard_name'    => 'api',
                'form_id'       => '9',
                'action_id'     => '18',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // Countries
            [
                'name'          => 'show_all_countries',
                'guard_name'    => 'api',
                'form_id'       => '10',
                'action_id'     => '1',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'create_countries',
                'guard_name'    => 'api',
                'form_id'       => '10',
                'action_id'     => '2',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_single_countries',
                'guard_name'    => 'api',
                'form_id'       => '10',
                'action_id'     => '3',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_countries',
                'guard_name'    => 'api',
                'form_id'       => '10',
                'action_id'     => '4',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'delete_countries',
                'guard_name'    => 'api',
                'form_id'       => '10',
                'action_id'     => '5',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'restore_countries',
                'guard_name'    => 'api',
                'form_id'       => '10',
                'action_id'     => '6',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_all_archive_countries',
                'guard_name'    => 'api',
                'form_id'       => '10',
                'action_id'     => '7',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'import_countries',
                'guard_name'    => 'api',
                'form_id'       => '10',
                'action_id'     => '10',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_xlsx_countries',
                'guard_name'    => 'api',
                'form_id'       => '10',
                'action_id'     => '11',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_csv_countries',
                'guard_name'    => 'api',
                'form_id'       => '10',
                'action_id'     => '22',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_pdf_countries',
                'guard_name'    => 'api',
                'form_id'       => '10',
                'action_id'     => '12',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_email_countries',
                'guard_name'    => 'api',
                'form_id'       => '10',
                'action_id'     => '13',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_view_country',
                'guard_name'    => 'api',
                'form_id'       => '10',
                'action_id'     => '18',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // Currencies
            [
                'name'          => 'show_all_currencies',
                'guard_name'    => 'api',
                'form_id'       => '11',
                'action_id'     => '1',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'create_currencies',
                'guard_name'    => 'api',
                'form_id'       => '11',
                'action_id'     => '2',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_single_currencies',
                'guard_name'    => 'api',
                'form_id'       => '11',
                'action_id'     => '3',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_currencies',
                'guard_name'    => 'api',
                'form_id'       => '11',
                'action_id'     => '4',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'delete_currencies',
                'guard_name'    => 'api',
                'form_id'       => '11',
                'action_id'     => '5',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'restore_currencies',
                'guard_name'    => 'api',
                'form_id'       => '11',
                'action_id'     => '6',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_all_archive_currencies',
                'guard_name'    => 'api',
                'form_id'       => '11',
                'action_id'     => '7',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'import_currencies',
                'guard_name'    => 'api',
                'form_id'       => '11',
                'action_id'     => '10',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_xlsx_currencies',
                'guard_name'    => 'api',
                'form_id'       => '11',
                'action_id'     => '11',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_csv_currencies',
                'guard_name'    => 'api',
                'form_id'       => '11',
                'action_id'     => '22',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_pdf_currencies',
                'guard_name'    => 'api',
                'form_id'       => '11',
                'action_id'     => '12',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_email_currencies',
                'guard_name'    => 'api',
                'form_id'       => '11',
                'action_id'     => '13',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            [
                'name'          => 'edit_view_currency',
                'guard_name'    => 'api',
                'form_id'       => '11',
                'action_id'     => '18',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // Division Types

            [
                'name'          => 'show_all_divisiontypes',
                'guard_name'    => 'api',
                'form_id'       => '12',
                'action_id'     => '1',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'create_divisiontypes',
                'guard_name'    => 'api',
                'form_id'       => '12',
                'action_id'     => '2',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_single_divisiontypes',
                'guard_name'    => 'api',
                'form_id'       => '12',
                'action_id'     => '3',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_divisiontypes',
                'guard_name'    => 'api',
                'form_id'       => '12',
                'action_id'     => '4',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'delete_divisiontypes',
                'guard_name'    => 'api',
                'form_id'       => '12',
                'action_id'     => '5',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'restore_divisiontypes',
                'guard_name'    => 'api',
                'form_id'       => '12',
                'action_id'     => '6',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_all_archive_divisiontypes',
                'guard_name'    => 'api',
                'form_id'       => '12',
                'action_id'     => '7',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'destroy_divisiontypes',
                'guard_name'    => 'api',
                'form_id'       => '12',
                'action_id'     => '8',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'import_divisiontypes',
                'guard_name'    => 'api',
                'form_id'       => '12',
                'action_id'     => '10',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_xlsx_divisiontypes',
                'guard_name'    => 'api',
                'form_id'       => '12',
                'action_id'     => '11',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_csv_divisiontypes',
                'guard_name'    => 'api',
                'form_id'       => '12',
                'action_id'     => '22',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_pdf_divisiontypes',
                'guard_name'    => 'api',
                'form_id'       => '12',
                'action_id'     => '12',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_email_divisiontypes',
                'guard_name'    => 'api',
                'form_id'       => '12',
                'action_id'     => '13',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_view_divisiontype',
                'guard_name'    => 'api',
                'form_id'       => '12',
                'action_id'     => '18',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // Lines
            [
                'name'          => 'show_all_lines',
                'guard_name'    => 'api',
                'form_id'       => '13',
                'action_id'     => '1',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'create_lines',
                'guard_name'    => 'api',
                'form_id'       => '13',
                'action_id'     => '2',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_single_lines',
                'guard_name'    => 'api',
                'form_id'       => '13',
                'action_id'     => '3',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_lines',
                'guard_name'    => 'api',
                'form_id'       => '13',
                'action_id'     => '4',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'delete_lines',
                'guard_name'    => 'api',
                'form_id'       => '13',
                'action_id'     => '5',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'restore_lines',
                'guard_name'    => 'api',
                'form_id'       => '13',
                'action_id'     => '6',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_all_archive_lines',
                'guard_name'    => 'api',
                'form_id'       => '13',
                'action_id'     => '7',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'import_lines',
                'guard_name'    => 'api',
                'form_id'       => '13',
                'action_id'     => '10',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_xlsx_lines',
                'guard_name'    => 'api',
                'form_id'       => '13',
                'action_id'     => '11',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_csv_lines',
                'guard_name'    => 'api',
                'form_id'       => '13',
                'action_id'     => '22',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_pdf_lines',
                'guard_name'    => 'api',
                'form_id'       => '13',
                'action_id'     => '12',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_email_lines',
                'guard_name'    => 'api',
                'form_id'       => '13',
                'action_id'     => '13',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_view_line',
                'guard_name'    => 'api',
                'form_id'       => '13',
                'action_id'     => '18',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // line division types
            [
                'name'          => 'show_all_line_division_types',
                'guard_name'    => 'api',
                'form_id'       => '14',
                'action_id'     => '1',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'create_line_division_types',
                'guard_name'    => 'api',
                'form_id'       => '14',
                'action_id'     => '2',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_single_line_division_types',
                'guard_name'    => 'api',
                'form_id'       => '14',
                'action_id'     => '3',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_line_division_types',
                'guard_name'    => 'api',
                'form_id'       => '14',
                'action_id'     => '4',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'delete_line_division_types',
                'guard_name'    => 'api',
                'form_id'       => '14',
                'action_id'     => '5',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'restore_line_division_types',
                'guard_name'    => 'api',
                'form_id'       => '14',
                'action_id'     => '6',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_all_archive_line_division_types',
                'guard_name'    => 'api',
                'form_id'       => '14',
                'action_id'     => '7',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'destroy_line_division_types',
                'guard_name'    => 'api',
                'form_id'       => '14',
                'action_id'     => '8',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'import_line_division_types',
                'guard_name'    => 'api',
                'form_id'       => '14',
                'action_id'     => '10',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_xlsx_line_division_types',
                'guard_name'    => 'api',
                'form_id'       => '14',
                'action_id'     => '11',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_csv_line_division_types',
                'guard_name'    => 'api',
                'form_id'       => '14',
                'action_id'     => '22',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_pdf_line_division_types',
                'guard_name'    => 'api',
                'form_id'       => '14',
                'action_id'     => '12',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_email_line_division_types',
                'guard_name'    => 'api',
                'form_id'       => '14',
                'action_id'     => '13',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_view_line_division_types',
                'guard_name'    => 'api',
                'form_id'       => '14',
                'action_id'     => '18',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_view_single_line_division_type',
                'guard_name'    => 'api',
                'form_id'       => '14',
                'action_id'     => '19',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // Line Divisions

            [
                'name'          => 'show_all_line_divisions',
                'guard_name'    => 'api',
                'form_id'       => '15',
                'action_id'     => '1',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'create_line_divisions',
                'guard_name'    => 'api',
                'form_id'       => '15',
                'action_id'     => '2',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_single_line_divisions',
                'guard_name'    => 'api',
                'form_id'       => '15',
                'action_id'     => '3',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_line_divisions',
                'guard_name'    => 'api',
                'form_id'       => '15',
                'action_id'     => '4',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'delete_line_divisions',
                'guard_name'    => 'api',
                'form_id'       => '15',
                'action_id'     => '5',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'restore_line_divisions',
                'guard_name'    => 'api',
                'form_id'       => '15',
                'action_id'     => '6',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_all_archive_line_divisions',
                'guard_name'    => 'api',
                'form_id'       => '15',
                'action_id'     => '7',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'destroy_line_divisions',
                'guard_name'    => 'api',
                'form_id'       => '13',
                'action_id'     => '8',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'import_line_divisions',
                'guard_name'    => 'api',
                'form_id'       => '15',
                'action_id'     => '10',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_xlsx_line_divisions',
                'guard_name'    => 'api',
                'form_id'       => '15',
                'action_id'     => '11',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_csv_line_divisions',
                'guard_name'    => 'api',
                'form_id'       => '15',
                'action_id'     => '22',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_pdf_line_divisions',
                'guard_name'    => 'api',
                'form_id'       => '15',
                'action_id'     => '12',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_email_line_divisions',
                'guard_name'    => 'api',
                'form_id'       => '15',
                'action_id'     => '13',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_view_line_divisions',
                'guard_name'    => 'api',
                'form_id'       => '15',
                'action_id'     => '18',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_view_single_line_division',
                'guard_name'    => 'api',
                'form_id'       => '15',
                'action_id'     => '19',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // Line Division Parents
            [
                'name'          => 'show_all_line_div_parents',
                'guard_name'    => 'api',
                'form_id'       => '16',
                'action_id'     => '1',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'create_line_div_parents',
                'guard_name'    => 'api',
                'form_id'       => '16',
                'action_id'     => '2',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_single_line_div_parents',
                'guard_name'    => 'api',
                'form_id'       => '16',
                'action_id'     => '3',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_line_div_parents',
                'guard_name'    => 'api',
                'form_id'       => '16',
                'action_id'     => '4',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'delete_line_div_parents',
                'guard_name'    => 'api',
                'form_id'       => '16',
                'action_id'     => '5',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'restore_line_div_parents',
                'guard_name'    => 'api',
                'form_id'       => '16',
                'action_id'     => '6',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_all_archive_line_div_parents',
                'guard_name'    => 'api',
                'form_id'       => '16',
                'action_id'     => '7',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'import_line_div_parents',
                'guard_name'    => 'api',
                'form_id'       => '16',
                'action_id'     => '10',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_xlsx_line_div_parents',
                'guard_name'    => 'api',
                'form_id'       => '16',
                'action_id'     => '11',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_csv_line_div_parents',
                'guard_name'    => 'api',
                'form_id'       => '16',
                'action_id'     => '22',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_pdf_line_div_parents',
                'guard_name'    => 'api',
                'form_id'       => '16',
                'action_id'     => '12',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_email_line_div_parents',
                'guard_name'    => 'api',
                'form_id'       => '16',
                'action_id'     => '13',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_view_line_div_parents',
                'guard_name'    => 'api',
                'form_id'       => '16',
                'action_id'     => '18',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_view_single_line_div_parent',
                'guard_name'    => 'api',
                'form_id'       => '16',
                'action_id'     => '19',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // line products
            [
                'name'          => 'show_all_line_products',
                'guard_name'    => 'api',
                'form_id'       => '17',
                'action_id'     => '1',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'create_line_products',
                'guard_name'    => 'api',
                'form_id'       => '17',
                'action_id'     => '2',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_single_line_products',
                'guard_name'    => 'api',
                'form_id'       => '17',
                'action_id'     => '3',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_line_products',
                'guard_name'    => 'api',
                'form_id'       => '17',
                'action_id'     => '4',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'delete_line_products',
                'guard_name'    => 'api',
                'form_id'       => '17',
                'action_id'     => '5',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'restore_line_products',
                'guard_name'    => 'api',
                'form_id'       => '17',
                'action_id'     => '6',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_all_archive_line_products',
                'guard_name'    => 'api',
                'form_id'       => '17',
                'action_id'     => '7',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'import_line_products',
                'guard_name'    => 'api',
                'form_id'       => '17',
                'action_id'     => '10',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_xlsx_line_products',
                'guard_name'    => 'api',
                'form_id'       => '17',
                'action_id'     => '11',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_csv_line_products',
                'guard_name'    => 'api',
                'form_id'       => '17',
                'action_id'     => '22',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_pdf_line_products',
                'guard_name'    => 'api',
                'form_id'       => '17',
                'action_id'     => '12',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_email_line_products',
                'guard_name'    => 'api',
                'form_id'       => '17',
                'action_id'     => '13',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_view_line_products',
                'guard_name'    => 'api',
                'form_id'       => '17',
                'action_id'     => '18',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_view_single_line_product',
                'guard_name'    => 'api',
                'form_id'       => '17',
                'action_id'     => '19',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // line users

            [
                'name'          => 'show_all_line_users',
                'guard_name'    => 'api',
                'form_id'       => '18',
                'action_id'     => '1',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'create_line_users',
                'guard_name'    => 'api',
                'form_id'       => '18',
                'action_id'     => '2',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_single_line_users',
                'guard_name'    => 'api',
                'form_id'       => '18',
                'action_id'     => '3',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_line_users',
                'guard_name'    => 'api',
                'form_id'       => '18',
                'action_id'     => '4',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'delete_line_users',
                'guard_name'    => 'api',
                'form_id'       => '18',
                'action_id'     => '5',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'restore_line_users',
                'guard_name'    => 'api',
                'form_id'       => '18',
                'action_id'     => '6',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_all_archive_line_users',
                'guard_name'    => 'api',
                'form_id'       => '18',
                'action_id'     => '7',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'import_line_users',
                'guard_name'    => 'api',
                'form_id'       => '18',
                'action_id'     => '10',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_xlsx_line_users',
                'guard_name'    => 'api',
                'form_id'       => '18',
                'action_id'     => '11',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_csv_line_users',
                'guard_name'    => 'api',
                'form_id'       => '18',
                'action_id'     => '22',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_pdf_line_users',
                'guard_name'    => 'api',
                'form_id'       => '18',
                'action_id'     => '12',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_email_line_users',
                'guard_name'    => 'api',
                'form_id'       => '18',
                'action_id'     => '13',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_view_line_users',
                'guard_name'    => 'api',
                'form_id'       => '18',
                'action_id'     => '18',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_view_single_line_user',
                'guard_name'    => 'api',
                'form_id'       => '18',
                'action_id'     => '19',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // user details

            [
                'name'          => 'show_all_user_details',
                'guard_name'    => 'api',
                'form_id'       => '19',
                'action_id'     => '1',
                'module_id'     => '1',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'create_user_details',
                'guard_name'    => 'api',
                'form_id'       => '19',
                'action_id'     => '2',
                'module_id'     => '1',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_single_user_details',
                'guard_name'    => 'api',
                'form_id'       => '19',
                'action_id'     => '3',
                'module_id'     => '1',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_user_details',
                'guard_name'    => 'api',
                'form_id'       => '19',
                'action_id'     => '4',
                'module_id'     => '1',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'delete_user_details',
                'guard_name'    => 'api',
                'form_id'       => '19',
                'action_id'     => '5',
                'module_id'     => '1',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'restore_user_details',
                'guard_name'    => 'api',
                'form_id'       => '19',
                'action_id'     => '6',
                'module_id'     => '1',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_all_archive_user_details',
                'guard_name'    => 'api',
                'form_id'       => '19',
                'action_id'     => '7',
                'module_id'     => '1',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            [
                'name'          => 'import_user_details',
                'guard_name'    => 'api',
                'form_id'       => '19',
                'action_id'     => '10',
                'module_id'     => '1',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_xlsx_user_details',
                'guard_name'    => 'api',
                'form_id'       => '19',
                'action_id'     => '11',
                'module_id'     => '1',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_csv_user_details',
                'guard_name'    => 'api',
                'form_id'       => '19',
                'action_id'     => '22',
                'module_id'     => '1',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_pdf_user_details',
                'guard_name'    => 'api',
                'form_id'       => '19',
                'action_id'     => '12',
                'module_id'     => '1',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_email_user_details',
                'guard_name'    => 'api',
                'form_id'       => '19',
                'action_id'     => '13',
                'module_id'     => '1',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_view_user_details',
                'guard_name'    => 'api',
                'form_id'       => '19',
                'action_id'     => '18',
                'module_id'     => '1',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_view_single_user_details',
                'guard_name'    => 'api',
                'form_id'       => '19',
                'action_id'     => '19',
                'module_id'     => '1',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // line user divisions

            [
                'name'          => 'show_all_line_user_divisions',
                'guard_name'    => 'api',
                'form_id'       => '20',
                'action_id'     => '1',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'create_line_user_divisions',
                'guard_name'    => 'api',
                'form_id'       => '20',
                'action_id'     => '2',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_single_line_user_divisions',
                'guard_name'    => 'api',
                'form_id'       => '20',
                'action_id'     => '3',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_line_user_divisions',
                'guard_name'    => 'api',
                'form_id'       => '20',
                'action_id'     => '4',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'delete_line_user_divisions',
                'guard_name'    => 'api',
                'form_id'       => '20',
                'action_id'     => '5',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'restore_line_user_divisions',
                'guard_name'    => 'api',
                'form_id'       => '20',
                'action_id'     => '6',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_all_archive_line_user_divisions',
                'guard_name'    => 'api',
                'form_id'       => '20',
                'action_id'     => '7',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'import_line_user_divisions',
                'guard_name'    => 'api',
                'form_id'       => '20',
                'action_id'     => '10',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_xlsx_line_user_divisions',
                'guard_name'    => 'api',
                'form_id'       => '20',
                'action_id'     => '11',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_csv_line_user_divisions',
                'guard_name'    => 'api',
                'form_id'       => '20',
                'action_id'     => '22',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_pdf_line_user_divisions',
                'guard_name'    => 'api',
                'form_id'       => '20',
                'action_id'     => '12',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_email_line_user_divisions',
                'guard_name'    => 'api',
                'form_id'       => '20',
                'action_id'     => '13',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_view_line_user_divisions',
                'guard_name'    => 'api',
                'form_id'       => '20',
                'action_id'     => '18',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_view_single_line_user_division',
                'guard_name'    => 'api',
                'form_id'       => '20',
                'action_id'     => '19',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // Product Brands

            [
                'name'          => 'show_all_product_brands',
                'guard_name'    => 'api',
                'form_id'       => '21',
                'action_id'     => '1',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'create_product_brands',
                'guard_name'    => 'api',
                'form_id'       => '21',
                'action_id'     => '2',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_single_product_brands',
                'guard_name'    => 'api',
                'form_id'       => '21',
                'action_id'     => '3',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_product_brands',
                'guard_name'    => 'api',
                'form_id'       => '21',
                'action_id'     => '4',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'delete_product_brands',
                'guard_name'    => 'api',
                'form_id'       => '21',
                'action_id'     => '5',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'restore_product_brands',
                'guard_name'    => 'api',
                'form_id'       => '21',
                'action_id'     => '6',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_all_archive_product_brands',
                'guard_name'    => 'api',
                'form_id'       => '21',
                'action_id'     => '7',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'import_product_brands',
                'guard_name'    => 'api',
                'form_id'       => '21',
                'action_id'     => '10',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_xlsx_product_brands',
                'guard_name'    => 'api',
                'form_id'       => '21',
                'action_id'     => '11',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_csv_product_brands',
                'guard_name'    => 'api',
                'form_id'       => '21',
                'action_id'     => '22',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_pdf_product_brands',
                'guard_name'    => 'api',
                'form_id'       => '21',
                'action_id'     => '12',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_email_product_brands',
                'guard_name'    => 'api',
                'form_id'       => '21',
                'action_id'     => '13',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_view_product_brands',
                'guard_name'    => 'api',
                'form_id'       => '21',
                'action_id'     => '18',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_view_single_product_brand',
                'guard_name'    => 'api',
                'form_id'       => '21',
                'action_id'     => '19',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // Product Manufacturers

            [
                'name'          => 'show_all_product_manufacturers',
                'guard_name'    => 'api',
                'form_id'       => '22',
                'action_id'     => '1',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'create_product_manufacturers',
                'guard_name'    => 'api',
                'form_id'       => '22',
                'action_id'     => '2',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_single_product_manufacturers',
                'guard_name'    => 'api',
                'form_id'       => '22',
                'action_id'     => '3',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_product_manufacturers',
                'guard_name'    => 'api',
                'form_id'       => '22',
                'action_id'     => '4',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'delete_product_manufacturers',
                'guard_name'    => 'api',
                'form_id'       => '22',
                'action_id'     => '5',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'restore_product_manufacturers',
                'guard_name'    => 'api',
                'form_id'       => '22',
                'action_id'     => '6',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_all_archive_product_manufacturers',
                'guard_name'    => 'api',
                'form_id'       => '22',
                'action_id'     => '7',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            [
                'name'          => 'import_product_manufacturers',
                'guard_name'    => 'api',
                'form_id'       => '22',
                'action_id'     => '10',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_xlsx_product_manufacturers',
                'guard_name'    => 'api',
                'form_id'       => '22',
                'action_id'     => '11',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_csv_product_manufacturers',
                'guard_name'    => 'api',
                'form_id'       => '22',
                'action_id'     => '22',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_pdf_product_manufacturers',
                'guard_name'    => 'api',
                'form_id'       => '22',
                'action_id'     => '12',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_email_product_manufacturers',
                'guard_name'    => 'api',
                'form_id'       => '22',
                'action_id'     => '13',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_view_product_manufacturers',
                'guard_name'    => 'api',
                'form_id'       => '22',
                'action_id'     => '18',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_view_single_product_manufacturer',
                'guard_name'    => 'api',
                'form_id'       => '22',
                'action_id'     => '19',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // Product Prices

            [
                'name'          => 'show_all_product_prices',
                'guard_name'    => 'api',
                'form_id'       => '23',
                'action_id'     => '1',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'create_product_prices',
                'guard_name'    => 'api',
                'form_id'       => '23',
                'action_id'     => '2',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_single_product_prices',
                'guard_name'    => 'api',
                'form_id'       => '23',
                'action_id'     => '3',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_product_prices',
                'guard_name'    => 'api',
                'form_id'       => '23',
                'action_id'     => '4',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'delete_product_prices',
                'guard_name'    => 'api',
                'form_id'       => '23',
                'action_id'     => '5',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'restore_product_prices',
                'guard_name'    => 'api',
                'form_id'       => '23',
                'action_id'     => '6',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_all_archive_product_prices',
                'guard_name'    => 'api',
                'form_id'       => '23',
                'action_id'     => '7',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'import_product_prices',
                'guard_name'    => 'api',
                'form_id'       => '23',
                'action_id'     => '10',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_xlsx_product_prices',
                'guard_name'    => 'api',
                'form_id'       => '23',
                'action_id'     => '11',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_csv_product_prices',
                'guard_name'    => 'api',
                'form_id'       => '23',
                'action_id'     => '22',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_pdf_product_prices',
                'guard_name'    => 'api',
                'form_id'       => '23',
                'action_id'     => '12',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_email_product_prices',
                'guard_name'    => 'api',
                'form_id'       => '23',
                'action_id'     => '13',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_view_product_prices',
                'guard_name'    => 'api',
                'form_id'       => '23',
                'action_id'     => '18',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_view_single_product_price',
                'guard_name'    => 'api',
                'form_id'       => '23',
                'action_id'     => '19',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // product Specialities

            [
                'name'          => 'show_all_product_specialities',
                'guard_name'    => 'api',
                'form_id'       => '126',
                'action_id'     => '1',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'create_product_specialities',
                'guard_name'    => 'api',
                'form_id'       => '126',
                'action_id'     => '2',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_single_product_specialities',
                'guard_name'    => 'api',
                'form_id'       => '126',
                'action_id'     => '3',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_product_specialities',
                'guard_name'    => 'api',
                'form_id'       => '126',
                'action_id'     => '4',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'delete_product_specialities',
                'guard_name'    => 'api',
                'form_id'       => '126',
                'action_id'     => '5',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'restore_product_specialities',
                'guard_name'    => 'api',
                'form_id'       => '126',
                'action_id'     => '6',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_all_archive_product_specialities',
                'guard_name'    => 'api',
                'form_id'       => '126',
                'action_id'     => '7',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'destroy_product_specialities',
                'guard_name'    => 'api',
                'form_id'       => '126',
                'action_id'     => '8',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ],  [
                'name'          => 'import_product_specialities',
                'guard_name'    => 'api',
                'form_id'       => '126',
                'action_id'     => '10',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_xlsx_product_specialities',
                'guard_name'    => 'api',
                'form_id'       => '126',
                'action_id'     => '11',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_csv_product_specialities',
                'guard_name'    => 'api',
                'form_id'       => '126',
                'action_id'     => '22',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_pdf_product_specialities',
                'guard_name'    => 'api',
                'form_id'       => '126',
                'action_id'     => '12',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_email_product_specialities',
                'guard_name'    => 'api',
                'form_id'       => '126',
                'action_id'     => '13',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_view_product_specialities',
                'guard_name'    => 'api',
                'form_id'       => '126',
                'action_id'     => '18',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // Product Offers

            [
                'name'          => 'show_all_product_offers',
                'guard_name'    => 'api',
                'form_id'       => '127',
                'action_id'     => '1',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'create_product_offers',
                'guard_name'    => 'api',
                'form_id'       => '127',
                'action_id'     => '2',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_single_product_offers',
                'guard_name'    => 'api',
                'form_id'       => '127',
                'action_id'     => '3',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_product_offers',
                'guard_name'    => 'api',
                'form_id'       => '127',
                'action_id'     => '4',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'delete_product_offers',
                'guard_name'    => 'api',
                'form_id'       => '127',
                'action_id'     => '5',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'restore_product_offers',
                'guard_name'    => 'api',
                'form_id'       => '127',
                'action_id'     => '6',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_all_archive_product_offers',
                'guard_name'    => 'api',
                'form_id'       => '127',
                'action_id'     => '7',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'destroy_product_offers',
                'guard_name'    => 'api',
                'form_id'       => '127',
                'action_id'     => '8',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ],  [
                'name'          => 'import_product_offers',
                'guard_name'    => 'api',
                'form_id'       => '127',
                'action_id'     => '10',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_xlsx_product_offers',
                'guard_name'    => 'api',
                'form_id'       => '127',
                'action_id'     => '11',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_csv_product_offers',
                'guard_name'    => 'api',
                'form_id'       => '127',
                'action_id'     => '22',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_pdf_product_offers',
                'guard_name'    => 'api',
                'form_id'       => '127',
                'action_id'     => '12',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_email_product_offers',
                'guard_name'    => 'api',
                'form_id'       => '127',
                'action_id'     => '13',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_view_product_offers',
                'guard_name'    => 'api',
                'form_id'       => '127',
                'action_id'     => '18',
                'module_id'     => '2',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // Error Messages

            [
                'name'          => 'show_all_error_messages',
                'guard_name'    => 'api',
                'form_id'       => '24',
                'action_id'     => '1',
                'module_id'     => '15',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'create_error_messages',
                'guard_name'    => 'api',
                'form_id'       => '24',
                'action_id'     => '2',
                'module_id'     => '15',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_single_error_messages',
                'guard_name'    => 'api',
                'form_id'       => '24',
                'action_id'     => '3',
                'module_id'     => '15',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_error_messages',
                'guard_name'    => 'api',
                'form_id'       => '24',
                'action_id'     => '4',
                'module_id'     => '15',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'delete_error_messages',
                'guard_name'    => 'api',
                'form_id'       => '24',
                'action_id'     => '5',
                'module_id'     => '15',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'restore_error_messages',
                'guard_name'    => 'api',
                'form_id'       => '24',
                'action_id'     => '6',
                'module_id'     => '15',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_all_archive_error_messages',
                'guard_name'    => 'api',
                'form_id'       => '24',
                'action_id'     => '7',
                'module_id'     => '15',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            [
                'name'          => 'import_error_messages',
                'guard_name'    => 'api',
                'form_id'       => '24',
                'action_id'     => '10',
                'module_id'     => '15',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_xlsx_error_messages',
                'guard_name'    => 'api',
                'form_id'       => '24',
                'action_id'     => '11',
                'module_id'     => '15',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_csv_error_messages',
                'guard_name'    => 'api',
                'form_id'       => '24',
                'action_id'     => '22',
                'module_id'     => '15',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_pdf_error_messages',
                'guard_name'    => 'api',
                'form_id'       => '24',
                'action_id'     => '12',
                'module_id'     => '15',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_email_error_messages',
                'guard_name'    => 'api',
                'form_id'       => '24',
                'action_id'     => '13',
                'module_id'     => '15',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_view_error_message',
                'guard_name'    => 'api',
                'form_id'       => '24',
                'action_id'     => '18',
                'module_id'     => null,
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // Permissions

            [
                'name'          => 'show_all_permissions',
                'guard_name'    => 'api',
                'form_id'       => '25',
                'action_id'     => '1',
                'module_id'     => '15',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'create_permissions',
                'guard_name'    => 'api',
                'form_id'       => '25',
                'action_id'     => '2',
                'module_id'     => '15',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_single_permissions',
                'guard_name'    => 'api',
                'form_id'       => '25',
                'action_id'     => '3',
                'module_id'     => '15',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_permissions',
                'guard_name'    => 'api',
                'form_id'       => '25',
                'action_id'     => '4',
                'module_id'     => '15',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'delete_permissions',
                'guard_name'    => 'api',
                'form_id'       => '25',
                'action_id'     => '5',
                'module_id'     => '15',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'restore_permissions',
                'guard_name'    => 'api',
                'form_id'       => '25',
                'action_id'     => '6',
                'module_id'     => '15',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_all_archive_permissions',
                'guard_name'    => 'api',
                'form_id'       => '25',
                'action_id'     => '7',
                'module_id'     => '15',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'import_permissions',
                'guard_name'    => 'api',
                'form_id'       => '25',
                'action_id'     => '10',
                'module_id'     => '15',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_xlsx_permissions',
                'guard_name'    => 'api',
                'form_id'       => '25',
                'action_id'     => '11',
                'module_id'     => '15',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_csv_permissions',
                'guard_name'    => 'api',
                'form_id'       => '25',
                'action_id'     => '22',
                'module_id'     => '15',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_pdf_permissions',
                'guard_name'    => 'api',
                'form_id'       => '25',
                'action_id'     => '12',
                'module_id'     => '15',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_email_permissions',
                'guard_name'    => 'api',
                'form_id'       => '25',
                'action_id'     => '13',
                'module_id'     => '15',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_view_permission',
                'guard_name'    => 'api',
                'form_id'       => '25',
                'action_id'     => '18',
                'module_id'     => null,
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // Roles
            [
                'name'          => 'show_all_roles',
                'guard_name'    => 'api',
                'form_id'       => '26',
                'action_id'     => '1',
                'module_id'     => '15',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'create_roles',
                'guard_name'    => 'api',
                'form_id'       => '26',
                'action_id'     => '2',
                'module_id'     => '15',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_single_roles',
                'guard_name'    => 'api',
                'form_id'       => '26',
                'action_id'     => '3',
                'module_id'     => '15',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            [
                'name'          => 'edit_roles',
                'guard_name'    => 'api',
                'form_id'       => '26',
                'action_id'     => '4',
                'module_id'     => '15',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'assign_role_permissions',
                'guard_name'    => 'api',
                'form_id'       => '26',
                'action_id'     => '2',
                'module_id'     => '15',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'delete_roles',
                'guard_name'    => 'api',
                'form_id'       => '26',
                'action_id'     => '5',
                'module_id'     => '15',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'restore_roles',
                'guard_name'    => 'api',
                'form_id'       => '26',
                'action_id'     => '6',
                'module_id'     => '15',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_all_archive_roles',
                'guard_name'    => 'api',
                'form_id'       => '26',
                'action_id'     => '7',
                'module_id'     => '15',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'import_roles',
                'guard_name'    => 'api',
                'form_id'       => '26',
                'action_id'     => '10',
                'module_id'     => '15',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_xlsx_roles',
                'guard_name'    => 'api',
                'form_id'       => '26',
                'action_id'     => '11',
                'module_id'     => '15',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_csv_roles',
                'guard_name'    => 'api',
                'form_id'       => '26',
                'action_id'     => '22',
                'module_id'     => '15',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_pdf_roles',
                'guard_name'    => 'api',
                'form_id'       => '26',
                'action_id'     => '12',
                'module_id'     => '15',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_email_roles',
                'guard_name'    => 'api',
                'form_id'       => '26',
                'action_id'     => '13',
                'module_id'     => '15',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_view_role',
                'guard_name'    => 'api',
                'form_id'       => '26',
                'action_id'     => '18',
                'module_id'     => null,
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // Settings
            [
                'name'          => 'show_all_settings',
                'guard_name'    => 'api',
                'form_id'       => '27',
                'action_id'     => '1',
                'module_id'     => '15',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'create_settings',
                'guard_name'    => 'api',
                'form_id'       => '27',
                'action_id'     => '2',
                'module_id'     => '15',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'show_single_settings',
                'guard_name'    => 'api',
                'form_id'       => '27',
                'action_id'     => '3',
                'module_id'     => '15',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_settings',
                'guard_name'    => 'api',
                'form_id'       => '27',
                'action_id'     => '4',
                'module_id'     => '15',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'delete_settings',
                'guard_name'    => 'api',
                'form_id'       => '27',
                'action_id'     => '5',
                'module_id'     => '15',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'restore_settings',
                'guard_name'    => 'api',
                'form_id'       => '27',
                'action_id'     => '6',
                'module_id'     => '15',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_all_archive_settings',
                'guard_name'    => 'api',
                'form_id'       => '27',
                'action_id'     => '7',
                'module_id'     => '15',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'import_settings',
                'guard_name'    => 'api',
                'form_id'       => '27',
                'action_id'     => '10',
                'module_id'     => '15',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_xlsx_settings',
                'guard_name'    => 'api',
                'form_id'       => '27',
                'action_id'     => '11',
                'module_id'     => '15',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_csv_settings',
                'guard_name'    => 'api',
                'form_id'       => '27',
                'action_id'     => '22',
                'module_id'     => '15',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_pdf_settings',
                'guard_name'    => 'api',
                'form_id'       => '27',
                'action_id'     => '12',
                'module_id'     => '15',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_email_settings',
                'guard_name'    => 'api',
                'form_id'       => '27',
                'action_id'     => '13',
                'module_id'     => '15',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_view_setting',
                'guard_name'    => 'api',
                'form_id'       => '27',
                'action_id'     => '18',
                'module_id'     => null,
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            ///////////////////////// End of edit view actions //////////////////////////////

            // Modules
            [
                'name'          => 'show_all_modules',
                'guard_name'    => 'api',
                'form_id'       => '28',
                'action_id'     => '1',
                'module_id'     => '15',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'create_modules',
                'guard_name'    => 'api',
                'form_id'       => '28',
                'action_id'     => '2',
                'module_id'     => '15',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_single_modules',
                'guard_name'    => 'api',
                'form_id'       => '28',
                'action_id'     => '3',
                'module_id'     => '15',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_modules',
                'guard_name'    => 'api',
                'form_id'       => '28',
                'action_id'     => '4',
                'module_id'     => '15',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'delete_modules',
                'guard_name'    => 'api',
                'form_id'       => '28',
                'action_id'     => '5',
                'module_id'     => '15',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'restore_modules',
                'guard_name'    => 'api',
                'form_id'       => '28',
                'action_id'     => '6',
                'module_id'     => '15',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_all_archive_modules',
                'guard_name'    => 'api',
                'form_id'       => '28',
                'action_id'     => '7',
                'module_id'     => '15',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'destroy_modules',
                'guard_name'    => 'api',
                'form_id'       => '28',
                'action_id'     => '8',
                'module_id'     => '15',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'import_modules',
                'guard_name'    => 'api',
                'form_id'       => '28',
                'action_id'     => '10',
                'module_id'     => '15',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_xlsx_modules',
                'guard_name'    => 'api',
                'form_id'       => '28',
                'action_id'     => '11',
                'module_id'     => '15',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_csv_modules',
                'guard_name'    => 'api',
                'form_id'       => '28',
                'action_id'     => '22',
                'module_id'     => '15',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_pdf_modules',
                'guard_name'    => 'api',
                'form_id'       => '28',
                'action_id'     => '12',
                'module_id'     => '15',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_email_modules',
                'guard_name'    => 'api',
                'form_id'       => '28',
                'action_id'     => '13',
                'module_id'     => '15',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_view_module',
                'guard_name'    => 'api',
                'form_id'       => '28',
                'action_id'     => '18',
                'module_id'     => '15',
                'created_at'    => now(),
                'updated_at'    => now()
            ],


            // Company 

            [
                'name'          => 'show_all_companies',
                'guard_name'    => 'api',
                'form_id'       => '120',
                'action_id'     => '1',
                'module_id'     => '15',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_single_companies',
                'guard_name'    => 'api',
                'form_id'       => '120',
                'action_id'     => '3',
                'module_id'     => '15',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_companies',
                'guard_name'    => 'api',
                'form_id'       => '120',
                'action_id'     => '4',
                'module_id'     => '15',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'delete_companies',
                'guard_name'    => 'api',
                'form_id'       => '120',
                'action_id'     => '5',
                'module_id'     => '15',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'restore_companies',
                'guard_name'    => 'api',
                'form_id'       => '120',
                'action_id'     => '6',
                'module_id'     => '15',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_all_archive_companies',
                'guard_name'    => 'api',
                'form_id'       => '120',
                'action_id'     => '7',
                'module_id'     => '15',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'destroy_companies',
                'guard_name'    => 'api',
                'form_id'       => '120',
                'action_id'     => '8',
                'module_id'     => '15',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // classes
            [
                'name'          => 'show_all_classes',
                'guard_name'    => 'api',
                'form_id'       => '29',
                'action_id'     => '1',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'create_classes',
                'guard_name'    => 'api',
                'form_id'       => '29',
                'action_id'     => '2',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'show_single_classes',
                'guard_name'    => 'api',
                'form_id'       => '29',
                'action_id'     => '3',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_classes',
                'guard_name'    => 'api',
                'form_id'       => '29',
                'action_id'     => '4',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'delete_classes',
                'guard_name'    => 'api',
                'form_id'       => '29',
                'action_id'     => '5',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'restore_classes',
                'guard_name'    => 'api',
                'form_id'       => '29',
                'action_id'     => '6',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'show_all_archive_classes',
                'guard_name'    => 'api',
                'form_id'       => '29',
                'action_id'     => '7',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'destroy_classes',
                'guard_name'    => 'api',
                'form_id'       => '29',
                'action_id'     => '8',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ],



            [
                'name'          => 'import_classes',
                'guard_name'    => 'api',
                'form_id'       => '29',
                'action_id'     => '10',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_xlsx_classes',
                'guard_name'    => 'api',
                'form_id'       => '29',
                'action_id'     => '11',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_csv_classes',
                'guard_name'    => 'api',
                'form_id'       => '29',
                'action_id'     => '22',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_pdf_classes',
                'guard_name'    => 'api',
                'form_id'       => '29',
                'action_id'     => '12',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_email_classes',
                'guard_name'    => 'api',
                'form_id'       => '29',
                'action_id'     => '13',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_view_class',
                'guard_name'    => 'api',
                'form_id'       => '29',
                'action_id'     => '18',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // Line Classes
            [
                'name'          => 'show_all_line_classes',
                'guard_name'    => 'api',
                'form_id'       => '30',
                'action_id'     => '1',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'create_line_classes',
                'guard_name'    => 'api',
                'form_id'       => '30',
                'action_id'     => '2',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'show_single_line_classes',
                'guard_name'    => 'api',
                'form_id'       => '30',
                'action_id'     => '3',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_line_classes',
                'guard_name'    => 'api',
                'form_id'       => '30',
                'action_id'     => '4',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'delete_line_classes',
                'guard_name'    => 'api',
                'form_id'       => '30',
                'action_id'     => '5',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'restore_line_classes',
                'guard_name'    => 'api',
                'form_id'       => '30',
                'action_id'     => '6',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'show_all_archive_line_classes',
                'guard_name'    => 'api',
                'form_id'       => '30',
                'action_id'     => '7',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'destroy_line_classes',
                'guard_name'    => 'api',
                'form_id'       => '30',
                'action_id'     => '8',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ],


            [
                'name'          => 'import_line_classes',
                'guard_name'    => 'api',
                'form_id'       => '30',
                'action_id'     => '10',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_xlsx_line_classes',
                'guard_name'    => 'api',
                'form_id'       => '30',
                'action_id'     => '11',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_csv_line_classes',
                'guard_name'    => 'api',
                'form_id'       => '30',
                'action_id'     => '22',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_pdf_line_classes',
                'guard_name'    => 'api',
                'form_id'       => '30',
                'action_id'     => '12',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_email_line_classes',
                'guard_name'    => 'api',
                'form_id'       => '30',
                'action_id'     => '13',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_view_line_classes',
                'guard_name'    => 'api',
                'form_id'       => '30',
                'action_id'     => '18',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_view_single_line_class',
                'guard_name'    => 'api',
                'form_id'       => '30',
                'action_id'     => '19',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // Bricks
            [
                'name'          => 'show_all_bricks',
                'guard_name'    => 'api',
                'form_id'       => '31',
                'action_id'     => '1',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'show_all_brick_view_plans',
                'guard_name'    => 'api',
                'form_id'       => '31',
                'action_id'     => '1',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'create_bricks',
                'guard_name'    => 'api',
                'form_id'       => '31',
                'action_id'     => '2',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'show_single_bricks',
                'guard_name'    => 'api',
                'form_id'       => '31',
                'action_id'     => '3',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_bricks',
                'guard_name'    => 'api',
                'form_id'       => '31',
                'action_id'     => '4',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'delete_bricks',
                'guard_name'    => 'api',
                'form_id'       => '31',
                'action_id'     => '5',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'restore_bricks',
                'guard_name'    => 'api',
                'form_id'       => '31',
                'action_id'     => '6',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'show_all_archive_bricks',
                'guard_name'    => 'api',
                'form_id'       => '31',
                'action_id'     => '7',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'destroy_bricks',
                'guard_name'    => 'api',
                'form_id'       => '31',
                'action_id'     => '8',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ],


            [
                'name'          => 'import_bricks',
                'guard_name'    => 'api',
                'form_id'       => '31',
                'action_id'     => '10',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'import_bricks_unified',
                'guard_name'    => 'api',
                'form_id'       => '31',
                'action_id'     => '40',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_xlsx_bricks',
                'guard_name'    => 'api',
                'form_id'       => '31',
                'action_id'     => '11',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_csv_bricks',
                'guard_name'    => 'api',
                'form_id'       => '31',
                'action_id'     => '22',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_pdf_bricks',
                'guard_name'    => 'api',
                'form_id'       => '31',
                'action_id'     => '12',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_email_bricks',
                'guard_name'    => 'api',
                'form_id'       => '31',
                'action_id'     => '13',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_view_brick',
                'guard_name'    => 'api',
                'form_id'       => '31',
                'action_id'     => '18',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // Line Bricks
            [
                'name'          => 'show_all_line_bricks',
                'guard_name'    => 'api',
                'form_id'       => '32',
                'action_id'     => '1',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            // line division brick

            [
                'name'          => 'show_all_line_division_bricks',
                'guard_name'    => 'api',
                'form_id'       => '32',
                'action_id'     => '1',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'create_line_bricks',
                'guard_name'    => 'api',
                'form_id'       => '32',
                'action_id'     => '2',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_single_line_bricks',
                'guard_name'    => 'api',
                'form_id'       => '32',
                'action_id'     => '3',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_line_bricks',
                'guard_name'    => 'api',
                'form_id'       => '32',
                'action_id'     => '4',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'delete_line_bricks',
                'guard_name'    => 'api',
                'form_id'       => '32',
                'action_id'     => '5',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'restore_line_bricks',
                'guard_name'    => 'api',
                'form_id'       => '32',
                'action_id'     => '6',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_all_archive_line_bricks',
                'guard_name'    => 'api',
                'form_id'       => '32',
                'action_id'     => '7',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'destroy_line_bricks',
                'guard_name'    => 'api',
                'form_id'       => '32',
                'action_id'     => '8',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'import_line_bricks',
                'guard_name'    => 'api',
                'form_id'       => '32',
                'action_id'     => '10',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_xlsx_line_bricks',
                'guard_name'    => 'api',
                'form_id'       => '32',
                'action_id'     => '11',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_csv_line_bricks',
                'guard_name'    => 'api',
                'form_id'       => '32',
                'action_id'     => '11',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_pdf_line_bricks',
                'guard_name'    => 'api',
                'form_id'       => '32',
                'action_id'     => '12',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_email_line_bricks',
                'guard_name'    => 'api',
                'form_id'       => '32',
                'action_id'     => '13',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_view_line_bricks',
                'guard_name'    => 'api',
                'form_id'       => '32',
                'action_id'     => '18',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_view_single_line_brick',
                'guard_name'    => 'api',
                'form_id'       => '32',
                'action_id'     => '19',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // Specialities
            [
                'name'          => 'show_all_specialities',
                'guard_name'    => 'api',
                'form_id'       => '33',
                'action_id'     => '1',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'create_specialities',
                'guard_name'    => 'api',
                'form_id'       => '33',
                'action_id'     => '2',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_single_specialities',
                'guard_name'    => 'api',
                'form_id'       => '33',
                'action_id'     => '3',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_specialities',
                'guard_name'    => 'api',
                'form_id'       => '33',
                'action_id'     => '4',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'delete_specialities',
                'guard_name'    => 'api',
                'form_id'       => '33',
                'action_id'     => '5',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'restore_specialities',
                'guard_name'    => 'api',
                'form_id'       => '33',
                'action_id'     => '6',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_all_archive_specialities',
                'guard_name'    => 'api',
                'form_id'       => '33',
                'action_id'     => '7',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'destroy_specialities',
                'guard_name'    => 'api',
                'form_id'       => '33',
                'action_id'     => '8',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'import_specialities',
                'guard_name'    => 'api',
                'form_id'       => '33',
                'action_id'     => '10',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_xlsx_specialities',
                'guard_name'    => 'api',
                'form_id'       => '33',
                'action_id'     => '11',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_csv_specialities',
                'guard_name'    => 'api',
                'form_id'       => '33',
                'action_id'     => '22',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_pdf_specialities',
                'guard_name'    => 'api',
                'form_id'       => '33',
                'action_id'     => '12',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_email_specialities',
                'guard_name'    => 'api',
                'form_id'       => '33',
                'action_id'     => '13',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_view_speciality',
                'guard_name'    => 'api',
                'form_id'       => '33',
                'action_id'     => '18',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // Line Specialities
            [
                'name'          => 'show_all_line_specialities',
                'guard_name'    => 'api',
                'form_id'       => '34',
                'action_id'     => '1',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'create_line_specialities',
                'guard_name'    => 'api',
                'form_id'       => '34',
                'action_id'     => '2',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_single_line_specialities',
                'guard_name'    => 'api',
                'form_id'       => '34',
                'action_id'     => '3',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_line_specialities',
                'guard_name'    => 'api',
                'form_id'       => '34',
                'action_id'     => '4',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'delete_line_specialities',
                'guard_name'    => 'api',
                'form_id'       => '34',
                'action_id'     => '5',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'restore_line_specialities',
                'guard_name'    => 'api',
                'form_id'       => '34',
                'action_id'     => '6',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_all_archive_line_specialities',
                'guard_name'    => 'api',
                'form_id'       => '34',
                'action_id'     => '7',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'destroy_line_specialities',
                'guard_name'    => 'api',
                'form_id'       => '34',
                'action_id'     => '8',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'import_line_specialities',
                'guard_name'    => 'api',
                'form_id'       => '34',
                'action_id'     => '10',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_xlsx_line_specialities',
                'guard_name'    => 'api',
                'form_id'       => '34',
                'action_id'     => '11',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_csv_line_specialities',
                'guard_name'    => 'api',
                'form_id'       => '34',
                'action_id'     => '22',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_pdf_line_specialities',
                'guard_name'    => 'api',
                'form_id'       => '34',
                'action_id'     => '12',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_email_line_specialities',
                'guard_name'    => 'api',
                'form_id'       => '34',
                'action_id'     => '13',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_view_line_specialities',
                'guard_name'    => 'api',
                'form_id'       => '34',
                'action_id'     => '18',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_view_single_line_speciality',
                'guard_name'    => 'api',
                'form_id'       => '34',
                'action_id'     => '19',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // Shifts
            [
                'name'          => 'show_all_shifts',
                'guard_name'    => 'api',
                'form_id'       => '35',
                'action_id'     => '1',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'create_shifts',
                'guard_name'    => 'api',
                'form_id'       => '35',
                'action_id'     => '2',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_single_shifts',
                'guard_name'    => 'api',
                'form_id'       => '35',
                'action_id'     => '3',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_shifts',
                'guard_name'    => 'api',
                'form_id'       => '35',
                'action_id'     => '4',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'delete_shifts',
                'guard_name'    => 'api',
                'form_id'       => '35',
                'action_id'     => '5',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'restore_shifts',
                'guard_name'    => 'api',
                'form_id'       => '35',
                'action_id'     => '6',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_all_archive_shifts',
                'guard_name'    => 'api',
                'form_id'       => '35',
                'action_id'     => '7',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'destroy_shifts',
                'guard_name'    => 'api',
                'form_id'       => '35',
                'action_id'     => '8',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'import_shifts',
                'guard_name'    => 'api',
                'form_id'       => '35',
                'action_id'     => '10',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_xlsx_shifts',
                'guard_name'    => 'api',
                'form_id'       => '35',
                'action_id'     => '11',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_csv_shifts',
                'guard_name'    => 'api',
                'form_id'       => '35',
                'action_id'     => '22',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_pdf_shifts',
                'guard_name'    => 'api',
                'form_id'       => '35',
                'action_id'     => '12',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_email_shifts',
                'guard_name'    => 'api',
                'form_id'       => '35',
                'action_id'     => '13',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_view_shift',
                'guard_name'    => 'api',
                'form_id'       => '35',
                'action_id'     => '18',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // Account Types
            [
                'name'          => 'show_all_account_types',
                'guard_name'    => 'api',
                'form_id'       => '36',
                'action_id'     => '1',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // Sub Account Types

            [
                'name'          => 'show_all_sub_types',
                'guard_name'    => 'api',
                'form_id'       => '36',
                'action_id'     => '1',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'create_account_types',
                'guard_name'    => 'api',
                'form_id'       => '36',
                'action_id'     => '2',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_single_account_types',
                'guard_name'    => 'api',
                'form_id'       => '36',
                'action_id'     => '3',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_account_types',
                'guard_name'    => 'api',
                'form_id'       => '36',
                'action_id'     => '4',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'delete_account_types',
                'guard_name'    => 'api',
                'form_id'       => '36',
                'action_id'     => '5',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'restore_account_types',
                'guard_name'    => 'api',
                'form_id'       => '36',
                'action_id'     => '6',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_all_archive_account_types',
                'guard_name'    => 'api',
                'form_id'       => '36',
                'action_id'     => '7',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'destroy_account_types',
                'guard_name'    => 'api',
                'form_id'       => '36',
                'action_id'     => '8',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'import_account_types',
                'guard_name'    => 'api',
                'form_id'       => '36',
                'action_id'     => '10',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_xlsx_account_types',
                'guard_name'    => 'api',
                'form_id'       => '36',
                'action_id'     => '11',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_csv_account_types',
                'guard_name'    => 'api',
                'form_id'       => '36',
                'action_id'     => '22',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_pdf_account_types',
                'guard_name'    => 'api',
                'form_id'       => '36',
                'action_id'     => '12',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_email_account_types',
                'guard_name'    => 'api',
                'form_id'       => '36',
                'action_id'     => '13',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_view_account_type',
                'guard_name'    => 'api',
                'form_id'       => '36',
                'action_id'     => '18',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // Accounts
            [
                'name'          => 'show_all_accounts',
                'guard_name'    => 'api',
                'form_id'       => '37',
                'action_id'     => '1',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'create_accounts',
                'guard_name'    => 'api',
                'form_id'       => '37',
                'action_id'     => '2',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_single_accounts',
                'guard_name'    => 'api',
                'form_id'       => '37',
                'action_id'     => '3',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_accounts',
                'guard_name'    => 'api',
                'form_id'       => '37',
                'action_id'     => '4',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'delete_accounts',
                'guard_name'    => 'api',
                'form_id'       => '37',
                'action_id'     => '5',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'restore_accounts',
                'guard_name'    => 'api',
                'form_id'       => '37',
                'action_id'     => '6',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_all_archive_accounts',
                'guard_name'    => 'api',
                'form_id'       => '37',
                'action_id'     => '7',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'destroy_accounts',
                'guard_name'    => 'api',
                'form_id'       => '37',
                'action_id'     => '8',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'import_accounts',
                'guard_name'    => 'api',
                'form_id'       => '37',
                'action_id'     => '10',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_xlsx_accounts',
                'guard_name'    => 'api',
                'form_id'       => '37',
                'action_id'     => '11',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_csv_accounts',
                'guard_name'    => 'api',
                'form_id'       => '37',
                'action_id'     => '22',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_pdf_accounts',
                'guard_name'    => 'api',
                'form_id'       => '37',
                'action_id'     => '12',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_email_accounts',
                'guard_name'    => 'api',
                'form_id'       => '37',
                'action_id'     => '13',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_view_account',
                'guard_name'    => 'api',
                'form_id'       => '37',
                'action_id'     => '18',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // Account Lines
            [
                'name'          => 'show_all_account_lines',
                'guard_name'    => 'api',
                'form_id'       => '38',
                'action_id'     => '1',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'create_account_lines',
                'guard_name'    => 'api',
                'form_id'       => '38',
                'action_id'     => '2',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_single_account_lines',
                'guard_name'    => 'api',
                'form_id'       => '38',
                'action_id'     => '3',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_account_lines',
                'guard_name'    => 'api',
                'form_id'       => '38',
                'action_id'     => '4',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'delete_account_lines',
                'guard_name'    => 'api',
                'form_id'       => '38',
                'action_id'     => '5',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'restore_account_lines',
                'guard_name'    => 'api',
                'form_id'       => '38',
                'action_id'     => '6',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_all_archive_account_lines',
                'guard_name'    => 'api',
                'form_id'       => '38',
                'action_id'     => '7',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'destroy_account_lines',
                'guard_name'    => 'api',
                'form_id'       => '38',
                'action_id'     => '8',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'import_account_lines',
                'guard_name'    => 'api',
                'form_id'       => '38',
                'action_id'     => '10',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_xlsx_account_lines',
                'guard_name'    => 'api',
                'form_id'       => '38',
                'action_id'     => '11',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_csv_account_lines',
                'guard_name'    => 'api',
                'form_id'       => '38',
                'action_id'     => '22',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_pdf_account_lines',
                'guard_name'    => 'api',
                'form_id'       => '38',
                'action_id'     => '12',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_email_account_lines',
                'guard_name'    => 'api',
                'form_id'       => '38',
                'action_id'     => '13',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_view_account_lines',
                'guard_name'    => 'api',
                'form_id'       => '38',
                'action_id'     => '18',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_view_single_account_line',
                'guard_name'    => 'api',
                'form_id'       => '38',
                'action_id'     => '19',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // Doctors
            [
                'name'          => 'show_all_doctors',
                'guard_name'    => 'api',
                'form_id'       => '39',
                'action_id'     => '1',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'create_doctors',
                'guard_name'    => 'api',
                'form_id'       => '39',
                'action_id'     => '2',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_single_doctors',
                'guard_name'    => 'api',
                'form_id'       => '39',
                'action_id'     => '3',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_doctors',
                'guard_name'    => 'api',
                'form_id'       => '39',
                'action_id'     => '4',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'delete_doctors',
                'guard_name'    => 'api',
                'form_id'       => '39',
                'action_id'     => '5',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'restore_doctors',
                'guard_name'    => 'api',
                'form_id'       => '39',
                'action_id'     => '6',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_all_archive_doctors',
                'guard_name'    => 'api',
                'form_id'       => '39',
                'action_id'     => '7',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'destroy_doctors',
                'guard_name'    => 'api',
                'form_id'       => '39',
                'action_id'     => '8',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'import_doctors',
                'guard_name'    => 'api',
                'form_id'       => '39',
                'action_id'     => '10',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_xlsx_doctors',
                'guard_name'    => 'api',
                'form_id'       => '39',
                'action_id'     => '11',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_csv_doctors',
                'guard_name'    => 'api',
                'form_id'       => '39',
                'action_id'     => '22',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_pdf_doctors',
                'guard_name'    => 'api',
                'form_id'       => '39',
                'action_id'     => '12',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_email_doctors',
                'guard_name'    => 'api',
                'form_id'       => '39',
                'action_id'     => '13',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_view_doctor',
                'guard_name'    => 'api',
                'form_id'       => '39',
                'action_id'     => '18',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // Account Doctors
            [
                'name'          => 'show_all_account_doctors',
                'guard_name'    => 'api',
                'form_id'       => '40',
                'action_id'     => '1',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'create_account_doctors',
                'guard_name'    => 'api',
                'form_id'       => '40',
                'action_id'     => '2',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_single_account_doctors',
                'guard_name'    => 'api',
                'form_id'       => '40',
                'action_id'     => '3',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_account_doctors',
                'guard_name'    => 'api',
                'form_id'       => '40',
                'action_id'     => '4',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'delete_account_doctors',
                'guard_name'    => 'api',
                'form_id'       => '40',
                'action_id'     => '5',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'restore_account_doctors',
                'guard_name'    => 'api',
                'form_id'       => '40',
                'action_id'     => '6',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_all_archive_account_doctors',
                'guard_name'    => 'api',
                'form_id'       => '40',
                'action_id'     => '7',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'destroy_account_doctors',
                'guard_name'    => 'api',
                'form_id'       => '40',
                'action_id'     => '8',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'import_account_doctors',
                'guard_name'    => 'api',
                'form_id'       => '40',
                'action_id'     => '10',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_xlsx_account_doctors',
                'guard_name'    => 'api',
                'form_id'       => '40',
                'action_id'     => '11',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_csv_account_doctors',
                'guard_name'    => 'api',
                'form_id'       => '40',
                'action_id'     => '22',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_pdf_account_doctors',
                'guard_name'    => 'api',
                'form_id'       => '40',
                'action_id'     => '12',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_email_account_doctors',
                'guard_name'    => 'api',
                'form_id'       => '40',
                'action_id'     => '13',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_view_account_doctors',
                'guard_name'    => 'api',
                'form_id'       => '40',
                'action_id'     => '18',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_view_single_account_doctor',
                'guard_name'    => 'api',
                'form_id'       => '40',
                'action_id'     => '19',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // Levels
            [
                'name'          => 'show_all_levels',
                'guard_name'    => 'api',
                'form_id'       => '41',
                'action_id'     => '1',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'create_levels',
                'guard_name'    => 'api',
                'form_id'       => '41',
                'action_id'     => '2',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_single_levels',
                'guard_name'    => 'api',
                'form_id'       => '41',
                'action_id'     => '3',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_levels',
                'guard_name'    => 'api',
                'form_id'       => '41',
                'action_id'     => '4',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'delete_levels',
                'guard_name'    => 'api',
                'form_id'       => '41',
                'action_id'     => '5',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'restore_levels',
                'guard_name'    => 'api',
                'form_id'       => '41',
                'action_id'     => '6',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_all_archive_levels',
                'guard_name'    => 'api',
                'form_id'       => '41',
                'action_id'     => '7',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'destroy_levels',
                'guard_name'    => 'api',
                'form_id'       => '41',
                'action_id'     => '8',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'import_levels',
                'guard_name'    => 'api',
                'form_id'       => '41',
                'action_id'     => '10',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_xlsx_levels',
                'guard_name'    => 'api',
                'form_id'       => '41',
                'action_id'     => '11',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_csv_levels',
                'guard_name'    => 'api',
                'form_id'       => '41',
                'action_id'     => '22',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_pdf_levels',
                'guard_name'    => 'api',
                'form_id'       => '41',
                'action_id'     => '12',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_email_levels',
                'guard_name'    => 'api',
                'form_id'       => '41',
                'action_id'     => '13',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_view_level',
                'guard_name'    => 'api',
                'form_id'       => '41',
                'action_id'     => '18',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ],


            // Personality Types

            [
                'name'          => 'show_all_personalitytypes',
                'guard_name'    => 'api',
                'form_id'       => '42',
                'action_id'     => '1',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'create_personalitytypes',
                'guard_name'    => 'api',
                'form_id'       => '42',
                'action_id'     => '2',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_single_personalitytypes',
                'guard_name'    => 'api',
                'form_id'       => '42',
                'action_id'     => '3',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_personalitytypes',
                'guard_name'    => 'api',
                'form_id'       => '42',
                'action_id'     => '4',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'delete_personalitytypes',
                'guard_name'    => 'api',
                'form_id'       => '42',
                'action_id'     => '5',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'restore_personalitytypes',
                'guard_name'    => 'api',
                'form_id'       => '42',
                'action_id'     => '6',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_all_archive_personalitytypes',
                'guard_name'    => 'api',
                'form_id'       => '42',
                'action_id'     => '7',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'destroy_personalitytypes',
                'guard_name'    => 'api',
                'form_id'       => '42',
                'action_id'     => '8',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'import_personalitytypes',
                'guard_name'    => 'api',
                'form_id'       => '42',
                'action_id'     => '10',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_xlsx_personalitytypes',
                'guard_name'    => 'api',
                'form_id'       => '42',
                'action_id'     => '11',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_csv_personalitytypes',
                'guard_name'    => 'api',
                'form_id'       => '42',
                'action_id'     => '22',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_pdf_personalitytypes',
                'guard_name'    => 'api',
                'form_id'       => '42',
                'action_id'     => '12',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_email_personalitytypes',
                'guard_name'    => 'api',
                'form_id'       => '42',
                'action_id'     => '13',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_view_personalitytype',
                'guard_name'    => 'api',
                'form_id'       => '42',
                'action_id'     => '18',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            // Tools Permissions

            // Log Activity
            [
                'name'          => 'show_all_log_activities',
                'guard_name'    => 'api',
                'form_id'       => '88',
                'action_id'     => '1',
                'module_id'     => '15',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'create_log_activities',
                'guard_name'    => 'api',
                'form_id'       => '88',
                'action_id'     => '2',
                'module_id'     => '15',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_single_log_activities',
                'guard_name'    => 'api',
                'form_id'       => '88',
                'action_id'     => '3',
                'module_id'     => '15',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_log_activities',
                'guard_name'    => '88',
                'form_id'       => '88',
                'action_id'     => '4',
                'module_id'     => '15',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'delete_log_activities',
                'guard_name'    => 'api',
                'form_id'       => '88',
                'action_id'     => '5',
                'module_id'     => '15',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'restore_log_activities',
                'guard_name'    => 'api',
                'form_id'       => '88',
                'action_id'     => '6',
                'module_id'     => '15',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_all_archive_log_activities',
                'guard_name'    => 'api',
                'form_id'       => '88',
                'action_id'     => '7',
                'module_id'     => '15',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'destroy_log_activities',
                'guard_name'    => 'api',
                'form_id'       => '88',
                'action_id'     => '8',
                'module_id'     => '15',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'import_log_activities',
                'guard_name'    => 'api',
                'form_id'       => '88',
                'action_id'     => '10',
                'module_id'     => '15',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_xlsx_log_activities',
                'guard_name'    => 'api',
                'form_id'       => '88',
                'action_id'     => '11',
                'module_id'     => '15',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_csv_log_activities',
                'guard_name'    => 'api',
                'form_id'       => '88',
                'action_id'     => '22',
                'module_id'     => '15',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_pdf_log_activities',
                'guard_name'    => 'api',
                'form_id'       => '88',
                'action_id'     => '12',
                'module_id'     => '15',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_email_log_activities',
                'guard_name'    => 'api',
                'form_id'       => '88',
                'action_id'     => '13',
                'module_id'     => '15',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_view_log_activity',
                'guard_name'    => 'api',
                'form_id'       => '88',
                'action_id'     => '18',
                'module_id'     => '15',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // Files Imported
            [
                'name'          => 'show_all_imports',
                'guard_name'    => 'api',
                'form_id'       => '43',
                'action_id'     => '1',
                'module_id'     => '15',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'show_all_archived_imports',
                'guard_name'    => 'api',
                'form_id'       => '43',
                'action_id'     => '7',
                'module_id'     => '15',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'delete_imports',
                'guard_name'    => 'api',
                'form_id'       => '43',
                'action_id'     => '5',
                'module_id'     => '15',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'restore_imports',
                'guard_name'    => 'api',
                'form_id'       => '43',
                'action_id'     => '6',
                'module_id'     => '15',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'download_imports',
                'guard_name'    => 'api',
                'form_id'       => '43',
                'action_id'     => '20',
                'module_id'     => '15',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // Socials
            [
                'name'          => 'show_all_socials',
                'guard_name'    => 'api',
                'form_id'       => '44',
                'action_id'     => '1',
                'module_id'     => '15',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'create_socials',
                'guard_name'    => 'api',
                'form_id'       => '44',
                'action_id'     => '2',
                'module_id'     => '15',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'show_single_socials',
                'guard_name'    => 'api',
                'form_id'       => '44',
                'action_id'     => '3',
                'module_id'     => '15',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_socials',
                'guard_name'    => 'api',
                'form_id'       => '44',
                'action_id'     => '4',
                'module_id'     => '15',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'delete_socials',
                'guard_name'    => 'api',
                'form_id'       => '44',
                'action_id'     => '5',
                'module_id'     => '15',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'restore_socials',
                'guard_name'    => 'api',
                'form_id'       => '44',
                'action_id'     => '6',
                'module_id'     => '15',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'show_all_archive_socials',
                'guard_name'    => 'api',
                'form_id'       => '44',
                'action_id'     => '7',
                'module_id'     => '15',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'destroy_socials',
                'guard_name'    => 'api',
                'form_id'       => '44',
                'action_id'     => '8',
                'module_id'     => '15',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'import_socials',
                'guard_name'    => 'api',
                'form_id'       => '44',
                'action_id'     => '10',
                'module_id'     => '15',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_xlsx_socials',
                'guard_name'    => 'api',
                'form_id'       => '44',
                'action_id'     => '11',
                'module_id'     => '15',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_csv_socials',
                'guard_name'    => 'api',
                'form_id'       => '44',
                'action_id'     => '22',
                'module_id'     => '15',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_pdf_socials',
                'guard_name'    => 'api',
                'form_id'       => '44',
                'action_id'     => '12',
                'module_id'     => '15',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_email_socials',
                'guard_name'    => 'api',
                'form_id'       => '44',
                'action_id'     => '13',
                'module_id'     => '15',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_view_social',
                'guard_name'    => 'api',
                'form_id'       => '44',
                'action_id'     => '18',
                'module_id'     => '15',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // account social
            [
                'name'          => 'show_all_account_socials',
                'guard_name'    => 'api',
                'form_id'       => '46',
                'action_id'     => '1',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'create_account_socials',
                'guard_name'    => 'api',
                'form_id'       => '46',
                'action_id'     => '2',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_single_account_socials',
                'guard_name'    => 'api',
                'form_id'       => '46',
                'action_id'     => '3',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_account_socials',
                'guard_name'    => 'api',
                'form_id'       => '46',
                'action_id'     => '4',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'delete_account_socials',
                'guard_name'    => 'api',
                'form_id'       => '46',
                'action_id'     => '5',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'restore_account_socials',
                'guard_name'    => 'api',
                'form_id'       => '46',
                'action_id'     => '6',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_all_archive_account_socials',
                'guard_name'    => 'api',
                'form_id'       => '46',
                'action_id'     => '7',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'destroy_account_socials',
                'guard_name'    => 'api',
                'form_id'       => '46',
                'action_id'     => '8',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'import_account_socials',
                'guard_name'    => 'api',
                'form_id'       => '46',
                'action_id'     => '10',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_xlsx_account_socials',
                'guard_name'    => 'api',
                'form_id'       => '46',
                'action_id'     => '11',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_csv_account_socials',
                'guard_name'    => 'api',
                'form_id'       => '46',
                'action_id'     => '22',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_pdf_account_socials',
                'guard_name'    => 'api',
                'form_id'       => '46',
                'action_id'     => '12',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_email_account_socials',
                'guard_name'    => 'api',
                'form_id'       => '46',
                'action_id'     => '13',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_view_account_socials',
                'guard_name'    => 'api',
                'form_id'       => '46',
                'action_id'     => '18',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_view_single_account_social',
                'guard_name'    => 'api',
                'form_id'       => '46',
                'action_id'     => '19',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // Doctor Socials
            [
                'name'          => 'show_all_doctor_socials',
                'guard_name'    => 'api',
                'form_id'       => '45',
                'action_id'     => '1',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'create_doctor_socials',
                'guard_name'    => 'api',
                'form_id'       => '45',
                'action_id'     => '2',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_single_doctor_socials',
                'guard_name'    => 'api',
                'form_id'       => '45',
                'action_id'     => '3',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_doctor_socials',
                'guard_name'    => 'api',
                'form_id'       => '45',
                'action_id'     => '4',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'delete_doctor_socials',
                'guard_name'    => 'api',
                'form_id'       => '45',
                'action_id'     => '5',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'restore_doctor_socials',
                'guard_name'    => 'api',
                'form_id'       => '45',
                'action_id'     => '6',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_all_archive_doctor_socials',
                'guard_name'    => 'api',
                'form_id'       => '45',
                'action_id'     => '7',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'destroy_doctor_socials',
                'guard_name'    => 'api',
                'form_id'       => '45',
                'action_id'     => '8',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'import_doctor_socials',
                'guard_name'    => 'api',
                'form_id'       => '45',
                'action_id'     => '10',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_xlsx_doctor_socials',
                'guard_name'    => 'api',
                'form_id'       => '45',
                'action_id'     => '11',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_csv_doctor_socials',
                'guard_name'    => 'api',
                'form_id'       => '45',
                'action_id'     => '22',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_pdf_doctor_socials',
                'guard_name'    => 'api',
                'form_id'       => '45',
                'action_id'     => '12',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_email_doctor_socials',
                'guard_name'    => 'api',
                'form_id'       => '45',
                'action_id'     => '13',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_view_doctor_socials',
                'guard_name'    => 'api',
                'form_id'       => '45',
                'action_id'     => '18',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_view_single_doctor_social',
                'guard_name'    => 'api',
                'form_id'       => '45',
                'action_id'     => '19',
                'module_id'     => '4',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // Call Rate
            [
                'name'          => 'show_all_call_rates',
                'guard_name'    => 'api',
                'form_id'       => '121',
                'action_id'     => '1',
                'module_id'     => '18',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'create_call_rates',
                'guard_name'    => 'api',
                'form_id'       => '121',
                'action_id'     => '2',
                'module_id'     => '18',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_single_call_rates',
                'guard_name'    => 'api',
                'form_id'       => '121',
                'action_id'     => '3',
                'module_id'     => '18',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_call_rates',
                'guard_name'    => 'api',
                'form_id'       => '121',
                'action_id'     => '4',
                'module_id'     => '18',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'delete_call_rates',
                'guard_name'    => 'api',
                'form_id'       => '121',
                'action_id'     => '5',
                'module_id'     => '18',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'destroy_call_rates',
                'guard_name'    => 'api',
                'form_id'       => '121',
                'action_id'     => '8',
                'module_id'     => '18',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'restore_call_rates',
                'guard_name'    => 'api',
                'form_id'       => '121',
                'action_id'     => '6',
                'module_id'     => '18',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_view_call_rates',
                'guard_name'    => 'api',
                'form_id'       => '121',
                'action_id'     => '18',
                'module_id'     => '18',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_view_single_call_rates',
                'guard_name'    => 'api',
                'form_id'       => '121',
                'action_id'     => '19',
                'module_id'     => '18',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // Class Frequencies
            [
                'name'          => 'show_all_class_frequencies',
                'guard_name'    => 'api',
                'form_id'       => '48',
                'action_id'     => '1',
                'module_id'     => '18',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'create_class_frequencies',
                'guard_name'    => 'api',
                'form_id'       => '48',
                'action_id'     => '2',
                'module_id'     => '18',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_single_class_frequencies',
                'guard_name'    => 'api',
                'form_id'       => '48',
                'action_id'     => '3',
                'module_id'     => '18',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_class_frequencies',
                'guard_name'    => 'api',
                'form_id'       => '48',
                'action_id'     => '4',
                'module_id'     => '18',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'delete_class_frequencies',
                'guard_name'    => 'api',
                'form_id'       => '48',
                'action_id'     => '5',
                'module_id'     => '18',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'destroy_class_frequencies',
                'guard_name'    => 'api',
                'form_id'       => '48',
                'action_id'     => '8',
                'module_id'     => '18',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'restore_class_frequencies',
                'guard_name'    => 'api',
                'form_id'       => '48',
                'action_id'     => '6',
                'module_id'     => '18',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_view_class_frequencies',
                'guard_name'    => 'api',
                'form_id'       => '48',
                'action_id'     => '18',
                'module_id'     => '18',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_view_single_class_frequencies',
                'guard_name'    => 'api',
                'form_id'       => '48',
                'action_id'     => '19',
                'module_id'     => '18',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // Speciality Frequency

            [
                'name'          => 'show_all_speciality_frequencies',
                'guard_name'    => 'api',
                'form_id'       => '122',
                'action_id'     => '1',
                'module_id'     => '18',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'create_speciality_frequencies',
                'guard_name'    => 'api',
                'form_id'       => '122',
                'action_id'     => '2',
                'module_id'     => '18',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_single_speciality_frequencies',
                'guard_name'    => 'api',
                'form_id'       => '122',
                'action_id'     => '3',
                'module_id'     => '18',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_speciality_frequencies',
                'guard_name'    => 'api',
                'form_id'       => '122',
                'action_id'     => '4',
                'module_id'     => '18',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'delete_speciality_frequencies',
                'guard_name'    => 'api',
                'form_id'       => '122',
                'action_id'     => '5',
                'module_id'     => '18',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'destroy_speciality_frequencies',
                'guard_name'    => 'api',
                'form_id'       => '122',
                'action_id'     => '8',
                'module_id'     => '18',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'restore_speciality_frequencies',
                'guard_name'    => 'api',
                'form_id'       => '122',
                'action_id'     => '6',
                'module_id'     => '18',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_view_speciality_frequencies',
                'guard_name'    => 'api',
                'form_id'       => '122',
                'action_id'     => '18',
                'module_id'     => '18',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_view_single_speciality_frequencies',
                'guard_name'    => 'api',
                'form_id'       => '122',
                'action_id'     => '19',
                'module_id'     => '18',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // Doctor Frequency

            [
                'name'          => 'show_all_doctor_frequencies',
                'guard_name'    => 'api',
                'form_id'       => '123',
                'action_id'     => '1',
                'module_id'     => '18',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'create_doctor_frequencies',
                'guard_name'    => 'api',
                'form_id'       => '123',
                'action_id'     => '2',
                'module_id'     => '18',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_single_doctor_frequencies',
                'guard_name'    => 'api',
                'form_id'       => '123',
                'action_id'     => '3',
                'module_id'     => '18',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_doctor_frequencies',
                'guard_name'    => 'api',
                'form_id'       => '123',
                'action_id'     => '4',
                'module_id'     => '18',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'delete_doctor_frequencies',
                'guard_name'    => 'api',
                'form_id'       => '123',
                'action_id'     => '5',
                'module_id'     => '18',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'destroy_doctor_frequencies',
                'guard_name'    => 'api',
                'form_id'       => '123',
                'action_id'     => '8',
                'module_id'     => '18',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'restore_doctor_frequencies',
                'guard_name'    => 'api',
                'form_id'       => '123',
                'action_id'     => '6',
                'module_id'     => '18',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_view_doctor_frequencies',
                'guard_name'    => 'api',
                'form_id'       => '123',
                'action_id'     => '18',
                'module_id'     => '18',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_view_single_doctor_frequencies',
                'guard_name'    => 'api',
                'form_id'       => '123',
                'action_id'     => '19',
                'module_id'     => '18',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // Visit FeedBack

            [
                'name'          => 'show_all_visit_feedbacks',
                'guard_name'    => 'api',
                'form_id'       => '56',
                'action_id'     => '1',
                'module_id'     => '6',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'create_visit_feedbacks',
                'guard_name'    => 'api',
                'form_id'       => '56',
                'action_id'     => '2',
                'module_id'     => '6',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_single_visit_feedbacks',
                'guard_name'    => 'api',
                'form_id'       => '56',
                'action_id'     => '3',
                'module_id'     => '6',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_visit_feedbacks',
                'guard_name'    => 'api',
                'form_id'       => '56',
                'action_id'     => '4',
                'module_id'     => '6',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'delete_visit_feedbacks',
                'guard_name'    => 'api',
                'form_id'       => '56',
                'action_id'     => '5',
                'module_id'     => '6',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'destroy_visit_feedbacks',
                'guard_name'    => 'api',
                'form_id'       => '56',
                'action_id'     => '8',
                'module_id'     => '6',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'restore_visit_feedbacks',
                'guard_name'    => 'api',
                'form_id'       => '56',
                'action_id'     => '6',
                'module_id'     => '6',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_view_visit_feedbacks',
                'guard_name'    => 'api',
                'form_id'       => '56',
                'action_id'     => '18',
                'module_id'     => '6',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_view_single_visit_feedbacks',
                'guard_name'    => 'api',
                'form_id'       => '56',
                'action_id'     => '19',
                'module_id'     => '6',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // Givaways
            [
                'name'          => 'show_all_giveaways',
                'guard_name'    => 'api',
                'form_id'       => '57',
                'action_id'     => '1',
                'module_id'     => '6',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'create_giveaways',
                'guard_name'    => 'api',
                'form_id'       => '57',
                'action_id'     => '2',
                'module_id'     => '6',
                'created_at'    => now(),
                'updated_at'    => now()

            ], [
                'name'          => 'show_single_giveaways',
                'guard_name'    => 'api',
                'form_id'       => '57',
                'action_id'     => '3',
                'module_id'     => '6',
                'created_at'    => now(),
                'updated_at'    => now()

            ], [
                'name'          => 'edit_giveaways',
                'guard_name'    => 'api',
                'form_id'       => '57',
                'action_id'     => '4',
                'module_id'     => '6',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'delete_giveaways',
                'guard_name'    => 'api',
                'form_id'       => '57',
                'action_id'     => '5',
                'module_id'     => '6',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'destroy_giveaways',
                'guard_name'    => 'api',
                'form_id'       => '57',
                'action_id'     => '8',
                'module_id'     => '6',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'restore_giveaways',
                'guard_name'    => 'api',
                'form_id'       => '57',
                'action_id'     => '6',
                'module_id'     => '6',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_view_giveaways',
                'guard_name'    => 'api',
                'form_id'       => '57',
                'action_id'     => '18',
                'module_id'     => '6',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_view_single_giveaways',
                'guard_name'    => 'api',
                'form_id'       => '57',
                'action_id'     => '19',
                'module_id'     => '6',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            //vacation types
            [
                'name'          => 'show_all_vacation_types',
                'guard_name'    => 'api',
                'form_id'       => '58',
                'action_id'     => '1',
                'module_id'     => '9',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'create_vacation_types',
                'guard_name'    => 'api',
                'form_id'       => '58',
                'action_id'     => '2',
                'module_id'     => '9',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'show_single_vacation_types',
                'guard_name'    => 'api',
                'form_id'       => '58',
                'action_id'     => '3',
                'module_id'     => '9',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_vacation_types',
                'guard_name'    => 'api',
                'form_id'       => '58',
                'action_id'     => '4',
                'module_id'     => '9',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'delete_vacation_types',
                'guard_name'    => 'api',
                'form_id'       => '58',
                'action_id'     => '5',
                'module_id'     => '9',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'destroy_vacation_types',
                'guard_name'    => 'api',
                'form_id'       => '58',
                'action_id'     => '8',
                'module_id'     => '9',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'restore_vacation_types',
                'guard_name'    => 'api',
                'form_id'       => '58',
                'action_id'     => '6',
                'module_id'     => '9',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_view_vacation_types',
                'guard_name'    => 'api',
                'form_id'       => '58',
                'action_id'     => '18',
                'module_id'     => '9',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // vacations
            [
                'name'          => 'show_all_vacations',
                'guard_name'    => 'api',
                'form_id'       => '63',
                'action_id'     => '1',
                'module_id'     => '9',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'create_vacations',
                'guard_name'    => 'api',
                'form_id'       => '63',
                'action_id'     => '2',
                'module_id'     => '9',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'show_single_vacations',
                'guard_name'    => 'api',
                'form_id'       => '63',
                'action_id'     => '3',
                'module_id'     => '9',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_vacations',
                'guard_name'    => 'api',
                'form_id'       => '63',
                'action_id'     => '4',
                'module_id'     => '9',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'delete_vacations',
                'guard_name'    => 'api',
                'form_id'       => '63',
                'action_id'     => '5',
                'module_id'     => '9',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'destroy_vacations',
                'guard_name'    => 'api',
                'form_id'       => '63',
                'action_id'     => '8',
                'module_id'     => '9',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'restore_vacations',
                'guard_name'    => 'api',
                'form_id'       => '63',
                'action_id'     => '6',
                'module_id'     => '9',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_view_vacations',
                'guard_name'    => 'api',
                'form_id'       => '63',
                'action_id'     => '18',
                'module_id'     => '9',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // Public Holidays
            [
                'name'          => 'show_all_public_holidays',
                'guard_name'    => 'api',
                'form_id'       => '59',
                'action_id'     => '1',
                'module_id'     => '9',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'create_public_holidays',
                'guard_name'    => 'api',
                'form_id'       => '59',
                'action_id'     => '2',
                'module_id'     => '9',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'show_single_public_holidays',
                'guard_name'    => 'api',
                'form_id'       => '59',
                'action_id'     => '3',
                'module_id'     => '9',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_public_holidays',
                'guard_name'    => 'api',
                'form_id'       => '59',
                'action_id'     => '4',
                'module_id'     => '9',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'delete_public_holidays',
                'guard_name'    => 'api',
                'form_id'       => '59',
                'action_id'     => '5',
                'module_id'     => '9',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'destroy_public_holidays',
                'guard_name'    => 'api',
                'form_id'       => '59',
                'action_id'     => '8',
                'module_id'     => '9',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'restore_public_holidays',
                'guard_name'    => 'api',
                'form_id'       => '59',
                'action_id'     => '6',
                'module_id'     => '9',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_view_public_holidays',
                'guard_name'    => 'api',
                'form_id'       => '59',
                'action_id'     => '18',
                'module_id'     => '6',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_view_single_public_holidays',
                'guard_name'    => 'api',
                'form_id'       => '59',
                'action_id'     => '19',
                'module_id'     => '9',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // Actual visit required inputs
            [
                'name'          => 'show_all_av_required_inputs',
                'guard_name'    => 'api',
                'form_id'       => '60',
                'action_id'     => '1',
                'module_id'     => '6',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'create_av_required_inputs',
                'guard_name'    => 'api',
                'form_id'       => '60',
                'action_id'     => '2',
                'module_id'     => '6',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'show_single_av_required_inputs',
                'guard_name'    => 'api',
                'form_id'       => '60',
                'action_id'     => '3',
                'module_id'     => '6',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_av_required_inputs',
                'guard_name'    => 'api',
                'form_id'       => '60',
                'action_id'     => '4',
                'module_id'     => '6',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'delete_av_required_inputs',
                'guard_name'    => 'api',
                'form_id'       => '60',
                'action_id'     => '5',
                'module_id'     => '6',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'destroy_av_required_inputs',
                'guard_name'    => 'api',
                'form_id'       => '60',
                'action_id'     => '8',
                'module_id'     => '6',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'restore_av_required_inputs',
                'guard_name'    => 'api',
                'form_id'       => '60',
                'action_id'     => '6',
                'module_id'     => '6',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_view_av_required_inputs',
                'guard_name'    => 'api',
                'form_id'       => '60',
                'action_id'     => '18',
                'module_id'     => '6',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_view_single_av_required_inputs',
                'guard_name'    => 'api',
                'form_id'       => '60',
                'action_id'     => '19',
                'module_id'     => '6',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // Plan Visit Settings
            [
                'name'          => 'show_all_plan_visit_settings',
                'guard_name'    => 'api',
                'form_id'       => '51',
                'action_id'     => '1',
                'module_id'     => '5',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'create_plan_visit_settings',
                'guard_name'    => 'api',
                'form_id'       => '51',
                'action_id'     => '2',
                'module_id'     => '5',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'show_single_plan_visit_settings',
                'guard_name'    => 'api',
                'form_id'       => '51',
                'action_id'     => '3',
                'module_id'     => '5',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_plan_visit_settings',
                'guard_name'    => 'api',
                'form_id'       => '51',
                'action_id'     => '4',
                'module_id'     => '5',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_view_plan_visit_setting',
                'guard_name'    => 'api',
                'form_id'       => '51',
                'action_id'     => '18',
                'module_id'     => '5',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // Actual Visit Setting
            [
                'name'          => 'show_all_actual_visit_settings',
                'guard_name'    => 'api',
                'form_id'       => '52',
                'action_id'     => '1',
                'module_id'     => '6',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'create_actual_visit_settings',
                'guard_name'    => 'api',
                'form_id'       => '52',
                'action_id'     => '2',
                'module_id'     => '6',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'show_single_actual_visit_settings',
                'guard_name'    => 'api',
                'form_id'       => '52',
                'action_id'     => '3',
                'module_id'     => '6',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_actual_visit_settings',
                'guard_name'    => 'api',
                'form_id'       => '52',
                'action_id'     => '4',
                'module_id'     => '6',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_view_actual_visit_setting',
                'guard_name'    => 'api',
                'form_id'       => '52',
                'action_id'     => '18',
                'module_id'     => '6',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // Start Plan Day User
            [
                'name'          => 'show_all_start_plan_day_users',
                'guard_name'    => 'api',
                'form_id'       => '53',
                'action_id'     => '1',
                'module_id'     => '5',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'create_start_plan_day_users',
                'guard_name'    => 'api',
                'form_id'       => '53',
                'action_id'     => '2',
                'module_id'     => '5',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'show_single_start_plan_day_users',
                'guard_name'    => 'api',
                'form_id'       => '53',
                'action_id'     => '3',
                'module_id'     => '5',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_start_plan_day_users',
                'guard_name'    => 'api',
                'form_id'       => '53',
                'action_id'     => '4',
                'module_id'     => '5',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'delete_start_plan_day_users',
                'guard_name'    => 'api',
                'form_id'       => '53',
                'action_id'     => '5',
                'module_id'     => '5',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'destroy_start_plan_day_users',
                'guard_name'    => 'api',
                'form_id'       => '53',
                'action_id'     => '8',
                'module_id'     => '5',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'restore_start_plan_day_users',
                'guard_name'    => 'api',
                'form_id'       => '53',
                'action_id'     => '6',
                'module_id'     => '5',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_view_start_plan_day_users',
                'guard_name'    => 'api',
                'form_id'       => '53',
                'action_id'     => '18',
                'module_id'     => '5',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_view_single_start_plan_day_users',
                'guard_name'    => 'api',
                'form_id'       => '53',
                'action_id'     => '19',
                'module_id'     => '5',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // Start Actual Day User
            [
                'name'          => 'show_all_start_actual_day_users',
                'guard_name'    => 'api',
                'form_id'       => '54',
                'action_id'     => '1',
                'module_id'     => '6',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'create_start_actual_day_users',
                'guard_name'    => 'api',
                'form_id'       => '54',
                'action_id'     => '2',
                'module_id'     => '6',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'show_single_start_actual_day_users',
                'guard_name'    => 'api',
                'form_id'       => '54',
                'action_id'     => '3',
                'module_id'     => '6',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_start_actual_day_users',
                'guard_name'    => 'api',
                'form_id'       => '54',
                'action_id'     => '4',
                'module_id'     => '6',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'delete_start_actual_day_users',
                'guard_name'    => 'api',
                'form_id'       => '54',
                'action_id'     => '5',
                'module_id'     => '6',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'destroy_start_actual_day_users',
                'guard_name'    => 'api',
                'form_id'       => '54',
                'action_id'     => '8',
                'module_id'     => '6',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'restore_start_actual_day_users',
                'guard_name'    => 'api',
                'form_id'       => '54',
                'action_id'     => '6',
                'module_id'     => '6',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_view_start_actual_day_users',
                'guard_name'    => 'api',
                'form_id'       => '54',
                'action_id'     => '18',
                'module_id'     => '6',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_view_single_start_actual_day_users',
                'guard_name'    => 'api',
                'form_id'       => '54',
                'action_id'     => '19',
                'module_id'     => '6',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // Plan Visits
            [
                'name'          => 'create_plan_visits',
                'guard_name'    => 'api',
                'form_id'       => '49',
                'action_id'     => '2',
                'module_id'     => '5',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'show_all_plan',
                'guard_name'    => 'api',
                'form_id'       => '49',
                'action_id'     => '1',
                'module_id'     => '5',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'show_single_plan_visits',
                'guard_name'    => 'api',
                'form_id'       => '49',
                'action_id'     => '3',
                'module_id'     => '5',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_plan_visits',
                'guard_name'    => 'api',
                'form_id'       => '49',
                'action_id'     => '4',
                'module_id'     => '5',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'delete_plan_visits',
                'guard_name'    => 'api',
                'form_id'       => '49',
                'action_id'     => '5',
                'module_id'     => '5',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // Actual Visits
            [
                'name'          => 'create_actual_visits',
                'guard_name'    => 'api',
                'form_id'       => '50',
                'action_id'     => '2',
                'module_id'     => '6',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'show_all_actual',
                'guard_name'    => 'api',
                'form_id'       => '50',
                'action_id'     => '1',
                'module_id'     => '6',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'show_single_actual_visit',
                'guard_name'    => 'api',
                'form_id'       => '50',
                'action_id'     => '3',
                'module_id'     => '6',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_actual_visits',
                'guard_name'    => 'api',
                'form_id'       => '50',
                'action_id'     => '4',
                'module_id'     => '6',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'delete_actual_visits',
                'guard_name'    => 'api',
                'form_id'       => '50',
                'action_id'     => '5',
                'module_id'     => '6',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'create_plan_actual_visit',
                'guard_name'    => 'api',
                'form_id'       => '50',
                'action_id'     => '2',
                'module_id'     => '6',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // Plan Visit Columns
            [
                'name'          => 'show_all_plan_visit_columns',
                'guard_name'    => 'api',
                'form_id'       => '55',
                'action_id'     => '1',
                'module_id'     => '5',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_plan_visit_columns',
                'guard_name'    => 'api',
                'form_id'       => '55',
                'action_id'     => '4',
                'module_id'     => '5',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_view_plan_visit_columns',
                'guard_name'    => 'api',
                'form_id'       => '55',
                'action_id'     => '18',
                'module_id'     => '5',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // Office Work Types
            [
                'name'          => 'show_all_office_work_types',
                'guard_name'    => 'api',
                'form_id'       => '61',
                'action_id'     => '1',
                'module_id'     => '7',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'create_office_work_types',
                'guard_name'    => 'api',
                'form_id'       => '61',
                'action_id'     => '2',
                'module_id'     => '7',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'show_single_office_work_types',
                'guard_name'    => 'api',
                'form_id'       => '61',
                'action_id'     => '3',
                'module_id'     => '7',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_office_work_types',
                'guard_name'    => 'api',
                'form_id'       => '61',
                'action_id'     => '4',
                'module_id'     => '7',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'delete_office_work_types',
                'guard_name'    => 'api',
                'form_id'       => '61',
                'action_id'     => '5',
                'module_id'     => '7',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'destroy_office_work_types',
                'guard_name'    => 'api',
                'form_id'       => '61',
                'action_id'     => '8',
                'module_id'     => '7',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'restore_office_work_types',
                'guard_name'    => 'api',
                'form_id'       => '61',
                'action_id'     => '6',
                'module_id'     => '7',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_view_office_work_types',
                'guard_name'    => 'api',
                'form_id'       => '61',
                'action_id'     => '18',
                'module_id'     => '7',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_view_single_office_work_types',
                'guard_name'    => 'api',
                'form_id'       => '61',
                'action_id'     => '19',
                'module_id'     => '7',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // Office Work Plan Visits
            [
                'name'          => 'show_all_ow_plan_visits',
                'guard_name'    => 'api',
                'form_id'       => '64',
                'action_id'     => '1',
                'module_id'     => '7',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'create_ow_plan_visits',
                'guard_name'    => 'api',
                'form_id'       => '64',
                'action_id'     => '2',
                'module_id'     => '7',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'show_single_ow_plan_visits',
                'guard_name'    => 'api',
                'form_id'       => '64',
                'action_id'     => '3',
                'module_id'     => '7',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_ow_plan_visits',
                'guard_name'    => 'api',
                'form_id'       => '64',
                'action_id'     => '4',
                'module_id'     => '7',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'delete_ow_plan_visits',
                'guard_name'    => 'api',
                'form_id'       => '64',
                'action_id'     => '5',
                'module_id'     => '7',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'destroy_ow_plan_visits',
                'guard_name'    => 'api',
                'form_id'       => '64',
                'action_id'     => '8',
                'module_id'     => '7',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'restore_ow_plan_visits',
                'guard_name'    => 'api',
                'form_id'       => '64',
                'action_id'     => '6',
                'module_id'     => '7',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_view_ow_plan_visits',
                'guard_name'    => 'api',
                'form_id'       => '64',
                'action_id'     => '18',
                'module_id'     => '7',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // Office work actual visits

            [
                'name'          => 'show_all_ow_actual_visits',
                'guard_name'    => 'api',
                'form_id'       => '65',
                'action_id'     => '1',
                'module_id'     => '7',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'create_ow_actual_visits',
                'guard_name'    => 'api',
                'form_id'       => '65',
                'action_id'     => '2',
                'module_id'     => '7',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'show_single_ow_actual_visits',
                'guard_name'    => 'api',
                'form_id'       => '65',
                'action_id'     => '3',
                'module_id'     => '7',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_ow_actual_visits',
                'guard_name'    => 'api',
                'form_id'       => '65',
                'action_id'     => '4',
                'module_id'     => '7',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'delete_ow_actual_visits',
                'guard_name'    => 'api',
                'form_id'       => '65',
                'action_id'     => '5',
                'module_id'     => '7',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'destroy_ow_actual_visits',
                'guard_name'    => 'api',
                'form_id'       => '65',
                'action_id'     => '8',
                'module_id'     => '7',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'restore_ow_actual_visits',
                'guard_name'    => 'api',
                'form_id'       => '65',
                'action_id'     => '6',
                'module_id'     => '7',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_view_ow_actual_visits',
                'guard_name'    => 'api',
                'form_id'       => '65',
                'action_id'     => '18',
                'module_id'     => '7',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // Line Giveaways
            [
                'name'          => 'show_all_line_giveaways',
                'guard_name'    => 'api',
                'form_id'       => '66',
                'action_id'     => '1',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'create_line_giveaways',
                'guard_name'    => 'api',
                'form_id'       => '66',
                'action_id'     => '2',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'show_single_line_giveaways',
                'guard_name'    => 'api',
                'form_id'       => '66',
                'action_id'     => '3',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_line_giveaways',
                'guard_name'    => 'api',
                'form_id'       => '66',
                'action_id'     => '4',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'delete_line_giveaways',
                'guard_name'    => 'api',
                'form_id'       => '66',
                'action_id'     => '5',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'destroy_line_giveaways',
                'guard_name'    => 'api',
                'form_id'       => '66',
                'action_id'     => '8',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'restore_line_giveaways',
                'guard_name'    => 'api',
                'form_id'       => '66',
                'action_id'     => '6',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_view_line_giveaways',
                'guard_name'    => 'api',
                'form_id'       => '66',
                'action_id'     => '18',
                'module_id'     => '3',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // Widgets

            // [
            //     'name'          => 'widgets',
            //     'guard_name'    => 'api',
            //     'form_id'       => '67',
            //     'action_id'     => '1',
            //     'module_id'     => '10',
            //     'created_at'    => now(),
            //     'updated_at'    => now()
            // ],
            [
                'name'          => 'visits_widget',
                'guard_name'    => 'api',
                'form_id'       => '67',
                'action_id'     => '3',
                'module_id'     => '10',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'doctor_coverage_widget',
                'guard_name'    => 'api',
                'form_id'       => '67',
                'action_id'     => '3',
                'module_id'     => '10',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'account_coverage_widget',
                'guard_name'    => 'api',
                'form_id'       => '67',
                'action_id'     => '3',
                'module_id'     => '10',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'am_doctor_coverage_widget',
                'guard_name'    => 'api',
                'form_id'       => '67',
                'action_id'     => '3',
                'module_id'     => '10',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'am_account_coverage_widget',
                'guard_name'    => 'api',
                'form_id'       => '67',
                'action_id'     => '3',
                'module_id'     => '10',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'pm_doctor_coverage_widget',
                'guard_name'    => 'api',
                'form_id'       => '67',
                'action_id'     => '3',
                'module_id'     => '10',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'pm_account_coverage_widget',
                'guard_name'    => 'api',
                'form_id'       => '67',
                'action_id'     => '3',
                'module_id'     => '10',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'account_type_coverage_widget',
                'guard_name'    => 'api',
                'form_id'       => '67',
                'action_id'     => '3',
                'module_id'     => '10',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'sales_product_coverage_unit_widget',
                'guard_name'    => 'api',
                'form_id'       => '68',
                'action_id'     => '3',
                'module_id'     => '10',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'sales_product_coverage_value_widget',
                'guard_name'    => 'api',
                'form_id'       => '68',
                'action_id'     => '3',
                'module_id'     => '10',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'sales_distributor_coverage_value_widget',
                'guard_name'    => 'api',
                'form_id'       => '68',
                'action_id'     => '3',
                'module_id'     => '10',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'sales_distributor_coverage_unit_widget',
                'guard_name'    => 'api',
                'form_id'       => '68',
                'action_id'     => '3',
                'module_id'     => '10',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'sales_target_coverage_unit_widget',
                'guard_name'    => 'api',
                'form_id'       => '68',
                'action_id'     => '3',
                'module_id'     => '10',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'sales_target_coverage_value_widget',
                'guard_name'    => 'api',
                'form_id'       => '68',
                'action_id'     => '3',
                'module_id'     => '10',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'sales_year_to_date_widget',
                'guard_name'    => 'api',
                'form_id'       => '68',
                'action_id'     => '3',
                'module_id'     => '10',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'tasks_widget',
                'guard_name'    => 'api',
                'form_id'       => '68',
                'action_id'     => '3',
                'module_id'     => '10',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'announcements_widget',
                'guard_name'    => 'api',
                'form_id'       => '68',
                'action_id'     => '3',
                'module_id'     => '10',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // Sales Settings
            [
                'name'          => 'show_all_sales_settings',
                'guard_name'    => 'api',
                'form_id'       => '69',
                'action_id'     => '1',
                'module_id'     => '11',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'create_sales_settings',
                'guard_name'    => 'api',
                'form_id'       => '69',
                'action_id'     => '2',
                'module_id'     => '11',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'show_single_sales_settings',
                'guard_name'    => 'api',
                'form_id'       => '69',
                'action_id'     => '3',
                'module_id'     => '11',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_sales_settings',
                'guard_name'    => 'api',
                'form_id'       => '69',
                'action_id'     => '4',
                'module_id'     => '11',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // Sales Mapping
            [
                'name'          => 'show_all_sales_mappings',
                'guard_name'    => 'api',
                'form_id'       => '70',
                'action_id'     => '1',
                'module_id'     => '11',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'create_sales_mappings',
                'guard_name'    => 'api',
                'form_id'       => '70',
                'action_id'     => '2',
                'module_id'     => '11',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_single_sales_mappings',
                'guard_name'    => 'api',
                'form_id'       => '70',
                'action_id'     => '3',
                'module_id'     => '11',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_sales_mappings',
                'guard_name'    => 'api',
                'form_id'       => '70',
                'action_id'     => '4',
                'module_id'     => '11',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'delete_sales_mappings',
                'guard_name'    => 'api',
                'form_id'       => '70',
                'action_id'     => '5',
                'module_id'     => '11',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'restore_sales_mappings',
                'guard_name'    => 'api',
                'form_id'       => '70',
                'action_id'     => '6',
                'module_id'     => '11',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_all_archive_sales_mappings',
                'guard_name'    => 'api',
                'form_id'       => '70',
                'action_id'     => '7',
                'module_id'     => '11',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'destroy_sales_mappings',
                'guard_name'    => 'api',
                'form_id'       => '70',
                'action_id'     => '8',
                'module_id'     => '11',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'import_sales_mappings',
                'guard_name'    => 'api',
                'form_id'       => '70',
                'action_id'     => '10',
                'module_id'     => '11',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_xlsx_sales_mappings',
                'guard_name'    => 'api',
                'form_id'       => '70',
                'action_id'     => '11',
                'module_id'     => '11',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_csv_sales_mappings',
                'guard_name'    => 'api',
                'form_id'       => '70',
                'action_id'     => '22',
                'module_id'     => '11',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_pdf_sales_mappings',
                'guard_name'    => 'api',
                'form_id'       => '70',
                'action_id'     => '12',
                'module_id'     => '11',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_email_sales_mappings',
                'guard_name'    => 'api',
                'form_id'       => '70',
                'action_id'     => '13',
                'module_id'     => '11',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_view_sales_mappings',
                'guard_name'    => 'api',
                'form_id'       => '70',
                'action_id'     => '18',
                'module_id'     => '11',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // Contribution
            [
                'name'          => 'show_all_contributions',
                'guard_name'    => 'api',
                'form_id'       => '71',
                'action_id'     => '1',
                'module_id'     => '11',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'create_contributions',
                'guard_name'    => 'api',
                'form_id'       => '71',
                'action_id'     => '2',
                'module_id'     => '11',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_single_contributions',
                'guard_name'    => 'api',
                'form_id'       => '71',
                'action_id'     => '3',
                'module_id'     => '11',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_contributions',
                'guard_name'    => 'api',
                'form_id'       => '71',
                'action_id'     => '4',
                'module_id'     => '11',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'delete_contributions',
                'guard_name'    => 'api',
                'form_id'       => '71',
                'action_id'     => '5',
                'module_id'     => '11',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'restore_contributions',
                'guard_name'    => 'api',
                'form_id'       => '71',
                'action_id'     => '6',
                'module_id'     => '11',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_all_archive_contributions',
                'guard_name'    => 'api',
                'form_id'       => '71',
                'action_id'     => '7',
                'module_id'     => '11',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'destroy_contributions',
                'guard_name'    => 'api',
                'form_id'       => '71',
                'action_id'     => '8',
                'module_id'     => '11',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'import_contributions',
                'guard_name'    => 'api',
                'form_id'       => '71',
                'action_id'     => '10',
                'module_id'     => '11',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_xlsx_contributions',
                'guard_name'    => 'api',
                'form_id'       => '71',
                'action_id'     => '11',
                'module_id'     => '11',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_csv_contributions',
                'guard_name'    => 'api',
                'form_id'       => '71',
                'action_id'     => '22',
                'module_id'     => '11',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_pdf_contributions',
                'guard_name'    => 'api',
                'form_id'       => '71',
                'action_id'     => '12',
                'module_id'     => '11',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_email_contributions',
                'guard_name'    => 'api',
                'form_id'       => '71',
                'action_id'     => '13',
                'module_id'     => '11',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_view_contributions',
                'guard_name'    => 'api',
                'form_id'       => '71',
                'action_id'     => '18',
                'module_id'     => '11',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // Target
            [
                'name'          => 'show_all_targets',
                'guard_name'    => 'api',
                'form_id'       => '72',
                'action_id'     => '1',
                'module_id'     => '11',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'create_targets',
                'guard_name'    => 'api',
                'form_id'       => '72',
                'action_id'     => '2',
                'module_id'     => '11',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_single_targets',
                'guard_name'    => 'api',
                'form_id'       => '72',
                'action_id'     => '3',
                'module_id'     => '11',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_targets',
                'guard_name'    => 'api',
                'form_id'       => '72',
                'action_id'     => '4',
                'module_id'     => '11',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'delete_targets',
                'guard_name'    => 'api',
                'form_id'       => '72',
                'action_id'     => '5',
                'module_id'     => '11',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'restore_targets',
                'guard_name'    => 'api',
                'form_id'       => '72',
                'action_id'     => '6',
                'module_id'     => '11',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_all_archive_targets',
                'guard_name'    => 'api',
                'form_id'       => '72',
                'action_id'     => '7',
                'module_id'     => '11',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'destroy_targets',
                'guard_name'    => 'api',
                'form_id'       => '72',
                'action_id'     => '8',
                'module_id'     => '11',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'import_targets',
                'guard_name'    => 'api',
                'form_id'       => '72',
                'action_id'     => '10',
                'module_id'     => '11',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_xlsx_targets',
                'guard_name'    => 'api',
                'form_id'       => '72',
                'action_id'     => '11',
                'module_id'     => '11',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_csv_targets',
                'guard_name'    => 'api',
                'form_id'       => '72',
                'action_id'     => '22',
                'module_id'     => '11',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_pdf_targets',
                'guard_name'    => 'api',
                'form_id'       => '72',
                'action_id'     => '12',
                'module_id'     => '11',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_email_targets',
                'guard_name'    => 'api',
                'form_id'       => '72',
                'action_id'     => '13',
                'module_id'     => '11',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_view_targets',
                'guard_name'    => 'api',
                'form_id'       => '72',
                'action_id'     => '18',
                'module_id'     => '11',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // Target Details
            [
                'name'          => 'show_all_target_details',
                'guard_name'    => 'api',
                'form_id'       => '73',
                'action_id'     => '1',
                'module_id'     => '11',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'create_target_details',
                'guard_name'    => 'api',
                'form_id'       => '73',
                'action_id'     => '2',
                'module_id'     => '11',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_single_target_details',
                'guard_name'    => 'api',
                'form_id'       => '73',
                'action_id'     => '3',
                'module_id'     => '11',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_target_details',
                'guard_name'    => 'api',
                'form_id'       => '73',
                'action_id'     => '4',
                'module_id'     => '11',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'delete_target_details',
                'guard_name'    => 'api',
                'form_id'       => '73',
                'action_id'     => '5',
                'module_id'     => '11',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'restore_target_details',
                'guard_name'    => 'api',
                'form_id'       => '73',
                'action_id'     => '6',
                'module_id'     => '11',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_all_archive_target_details',
                'guard_name'    => 'api',
                'form_id'       => '73',
                'action_id'     => '7',
                'module_id'     => '11',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'destroy_target_details',
                'guard_name'    => 'api',
                'form_id'       => '73',
                'action_id'     => '8',
                'module_id'     => '11',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'import_target_details',
                'guard_name'    => 'api',
                'form_id'       => '73',
                'action_id'     => '10',
                'module_id'     => '11',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_xlsx_target_details',
                'guard_name'    => 'api',
                'form_id'       => '73',
                'action_id'     => '11',
                'module_id'     => '11',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_csv_target_details',
                'guard_name'    => 'api',
                'form_id'       => '73',
                'action_id'     => '22',
                'module_id'     => '11',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_pdf_target_details',
                'guard_name'    => 'api',
                'form_id'       => '73',
                'action_id'     => '12',
                'module_id'     => '11',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_email_target_details',
                'guard_name'    => 'api',
                'form_id'       => '73',
                'action_id'     => '13',
                'module_id'     => '11',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_view_target_details',
                'guard_name'    => 'api',
                'form_id'       => '73',
                'action_id'     => '18',
                'module_id'     => '11',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // Sales
            [
                'name'          => 'show_all_sales',
                'guard_name'    => 'api',
                'form_id'       => '74',
                'action_id'     => '1',
                'module_id'     => '11',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'create_sales',
                'guard_name'    => 'api',
                'form_id'       => '74',
                'action_id'     => '2',
                'module_id'     => '11',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_single_sales',
                'guard_name'    => 'api',
                'form_id'       => '74',
                'action_id'     => '3',
                'module_id'     => '11',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_sales',
                'guard_name'    => 'api',
                'form_id'       => '74',
                'action_id'     => '4',
                'module_id'     => '11',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'delete_sales',
                'guard_name'    => 'api',
                'form_id'       => '74',
                'action_id'     => '5',
                'module_id'     => '11',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'restore_sales',
                'guard_name'    => 'api',
                'form_id'       => '74',
                'action_id'     => '6',
                'module_id'     => '11',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_all_archive_sales',
                'guard_name'    => 'api',
                'form_id'       => '74',
                'action_id'     => '7',
                'module_id'     => '11',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'destroy_sales',
                'guard_name'    => 'api',
                'form_id'       => '74',
                'action_id'     => '8',
                'module_id'     => '11',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'import_sales',
                'guard_name'    => 'api',
                'form_id'       => '74',
                'action_id'     => '10',
                'module_id'     => '11',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_xlsx_sales',
                'guard_name'    => 'api',
                'form_id'       => '74',
                'action_id'     => '11',
                'module_id'     => '11',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_csv_sales',
                'guard_name'    => 'api',
                'form_id'       => '74',
                'action_id'     => '22',
                'module_id'     => '11',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_pdf_sales',
                'guard_name'    => 'api',
                'form_id'       => '74',
                'action_id'     => '12',
                'module_id'     => '11',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_email_sales',
                'guard_name'    => 'api',
                'form_id'       => '74',
                'action_id'     => '13',
                'module_id'     => '11',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_view_sales',
                'guard_name'    => 'api',
                'form_id'       => '74',
                'action_id'     => '18',
                'module_id'     => '11',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // Unified Codes
            [
                'name'          => 'show_all_unified_codes',
                'guard_name'    => 'api',
                'form_id'       => '75',
                'action_id'     => '1',
                'module_id'     => '11',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'create_unified_codes',
                'guard_name'    => 'api',
                'form_id'       => '75',
                'action_id'     => '2',
                'module_id'     => '11',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_single_unified_codes',
                'guard_name'    => 'api',
                'form_id'       => '75',
                'action_id'     => '3',
                'module_id'     => '11',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_unified_codes',
                'guard_name'    => 'api',
                'form_id'       => '75',
                'action_id'     => '4',
                'module_id'     => '11',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'delete_unified_codes',
                'guard_name'    => 'api',
                'form_id'       => '75',
                'action_id'     => '5',
                'module_id'     => '11',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'restore_unified_codes',
                'guard_name'    => 'api',
                'form_id'       => '75',
                'action_id'     => '6',
                'module_id'     => '11',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_all_archive_unified_codes',
                'guard_name'    => 'api',
                'form_id'       => '75',
                'action_id'     => '7',
                'module_id'     => '11',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'destroy_unified_codes',
                'guard_name'    => 'api',
                'form_id'       => '75',
                'action_id'     => '8',
                'module_id'     => '11',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'import_unified_codes',
                'guard_name'    => 'api',
                'form_id'       => '75',
                'action_id'     => '10',
                'module_id'     => '11',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_xlsx_unified_codes',
                'guard_name'    => 'api',
                'form_id'       => '75',
                'action_id'     => '11',
                'module_id'     => '11',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_csv_unified_codes',
                'guard_name'    => 'api',
                'form_id'       => '75',
                'action_id'     => '22',
                'module_id'     => '11',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_pdf_unified_codes',
                'guard_name'    => 'api',
                'form_id'       => '75',
                'action_id'     => '12',
                'module_id'     => '11',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_email_unified_codes',
                'guard_name'    => 'api',
                'form_id'       => '75',
                'action_id'     => '13',
                'module_id'     => '11',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_view_unified_codes',
                'guard_name'    => 'api',
                'form_id'       => '75',
                'action_id'     => '18',
                'module_id'     => '11',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // permissions for position module

            // Position Settings
            [
                'name'          => 'show_all_position_settings',
                'guard_name'    => 'api',
                'form_id'       => '107',
                'action_id'     => '1',
                'module_id'     => '12',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'create_position_settings',
                'guard_name'    => 'api',
                'form_id'       => '107',
                'action_id'     => '2',
                'module_id'     => '12',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_single_position_settings',
                'guard_name'    => 'api',
                'form_id'       => '107',
                'action_id'     => '3',
                'module_id'     => '12',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_position_settings',
                'guard_name'    => 'api',
                'form_id'       => '107',
                'action_id'     => '4',
                'module_id'     => '12',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'delete_position_settings',
                'guard_name'    => 'api',
                'form_id'       => '107',
                'action_id'     => '5',
                'module_id'     => '12',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'restore_position_settings',
                'guard_name'    => 'api',
                'form_id'       => '107',
                'action_id'     => '6',
                'module_id'     => '12',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_all_archive_position_settings',
                'guard_name'    => 'api',
                'form_id'       => '107',
                'action_id'     => '7',
                'module_id'     => '12',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'destroy_position_settings',
                'guard_name'    => 'api',
                'form_id'       => '107',
                'action_id'     => '8',
                'module_id'     => '12',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'import_position_settings',
                'guard_name'    => 'api',
                'form_id'       => '107',
                'action_id'     => '10',
                'module_id'     => '12',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_xlsx_position_settings',
                'guard_name'    => 'api',
                'form_id'       => '107',
                'action_id'     => '11',
                'module_id'     => '12',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_csv_position_settings',
                'guard_name'    => 'api',
                'form_id'       => '107',
                'action_id'     => '22',
                'module_id'     => '12',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_pdf_position_settings',
                'guard_name'    => 'api',
                'form_id'       => '107',
                'action_id'     => '12',
                'module_id'     => '12',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_email_position_settings',
                'guard_name'    => 'api',
                'form_id'       => '107',
                'action_id'     => '13',
                'module_id'     => '12',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_view_position_settings',
                'guard_name'    => 'api',
                'form_id'       => '107',
                'action_id'     => '18',
                'module_id'     => '12',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // Position managers
            [
                'name'          => 'show_all_position_managers',
                'guard_name'    => 'api',
                'form_id'       => '108',
                'action_id'     => '1',
                'module_id'     => '12',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'create_position_managers',
                'guard_name'    => 'api',
                'form_id'       => '108',
                'action_id'     => '2',
                'module_id'     => '12',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_single_position_managers',
                'guard_name'    => 'api',
                'form_id'       => '108',
                'action_id'     => '3',
                'module_id'     => '12',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_position_managers',
                'guard_name'    => 'api',
                'form_id'       => '108',
                'action_id'     => '4',
                'module_id'     => '12',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'delete_position_managers',
                'guard_name'    => 'api',
                'form_id'       => '108',
                'action_id'     => '5',
                'module_id'     => '12',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'restore_position_managers',
                'guard_name'    => 'api',
                'form_id'       => '108',
                'action_id'     => '6',
                'module_id'     => '12',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_all_archive_position_managers',
                'guard_name'    => 'api',
                'form_id'       => '108',
                'action_id'     => '7',
                'module_id'     => '12',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'destroy_position_managers',
                'guard_name'    => 'api',
                'form_id'       => '108',
                'action_id'     => '8',
                'module_id'     => '12',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'import_position_managers',
                'guard_name'    => 'api',
                'form_id'       => '108',
                'action_id'     => '10',
                'module_id'     => '12',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_xlsx_position_managers',
                'guard_name'    => 'api',
                'form_id'       => '108',
                'action_id'     => '11',
                'module_id'     => '12',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_csv_position_managers',
                'guard_name'    => 'api',
                'form_id'       => '108',
                'action_id'     => '22',
                'module_id'     => '12',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_pdf_position_managers',
                'guard_name'    => 'api',
                'form_id'       => '108',
                'action_id'     => '12',
                'module_id'     => '12',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_email_position_managers',
                'guard_name'    => 'api',
                'form_id'       => '108',
                'action_id'     => '13',
                'module_id'     => '12',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_view_position_managers',
                'guard_name'    => 'api',
                'form_id'       => '108',
                'action_id'     => '18',
                'module_id'     => '12',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // Position
            [
                'name'          => 'show_all_positions',
                'guard_name'    => 'api',
                'form_id'       => '86',
                'action_id'     => '1',
                'module_id'     => '12',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'create_positions',
                'guard_name'    => 'api',
                'form_id'       => '86',
                'action_id'     => '2',
                'module_id'     => '12',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_single_positions',
                'guard_name'    => 'api',
                'form_id'       => '86',
                'action_id'     => '3',
                'module_id'     => '12',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_positions',
                'guard_name'    => 'api',
                'form_id'       => '86',
                'action_id'     => '4',
                'module_id'     => '12',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'delete_positions',
                'guard_name'    => 'api',
                'form_id'       => '86',
                'action_id'     => '5',
                'module_id'     => '12',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'restore_positions',
                'guard_name'    => 'api',
                'form_id'       => '86',
                'action_id'     => '6',
                'module_id'     => '12',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_all_archive_positions',
                'guard_name'    => 'api',
                'form_id'       => '86',
                'action_id'     => '7',
                'module_id'     => '12',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'destroy_positions',
                'guard_name'    => 'api',
                'form_id'       => '86',
                'action_id'     => '8',
                'module_id'     => '12',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'import_positions',
                'guard_name'    => 'api',
                'form_id'       => '86',
                'action_id'     => '10',
                'module_id'     => '12',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_xlsx_positions',
                'guard_name'    => 'api',
                'form_id'       => '86',
                'action_id'     => '11',
                'module_id'     => '12',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_csv_positions',
                'guard_name'    => 'api',
                'form_id'       => '86',
                'action_id'     => '22',
                'module_id'     => '12',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_pdf_positions',
                'guard_name'    => 'api',
                'form_id'       => '86',
                'action_id'     => '12',
                'module_id'     => '12',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_email_positions',
                'guard_name'    => 'api',
                'form_id'       => '86',
                'action_id'     => '13',
                'module_id'     => '12',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_view_positions',
                'guard_name'    => 'api',
                'form_id'       => '86',
                'action_id'     => '18',
                'module_id'     => '12',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            // employee position settings
            [
                'name'          => 'show_all_employee_position_settings',
                'guard_name'    => 'api',
                'form_id'       => '87',
                'action_id'     => '1',
                'module_id'     => '12',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'create_employee_position_settings',
                'guard_name'    => 'api',
                'form_id'       => '87',
                'action_id'     => '2',
                'module_id'     => '12',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_single_employee_position_settings',
                'guard_name'    => 'api',
                'form_id'       => '87',
                'action_id'     => '3',
                'module_id'     => '12',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_employee_position_settings',
                'guard_name'    => 'api',
                'form_id'       => '87',
                'action_id'     => '4',
                'module_id'     => '12',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'delete_employee_position_settings',
                'guard_name'    => 'api',
                'form_id'       => '87',
                'action_id'     => '5',
                'module_id'     => '12',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'restore_employee_position_settings',
                'guard_name'    => 'api',
                'form_id'       => '87',
                'action_id'     => '6',
                'module_id'     => '12',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_all_archive_employee_position_settings',
                'guard_name'    => 'api',
                'form_id'       => '87',
                'action_id'     => '7',
                'module_id'     => '12',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'destroy_employee_position_settings',
                'guard_name'    => 'api',
                'form_id'       => '87',
                'action_id'     => '8',
                'module_id'     => '12',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'import_employee_position_settings',
                'guard_name'    => 'api',
                'form_id'       => '87',
                'action_id'     => '10',
                'module_id'     => '12',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_xlsx_employee_position_settings',
                'guard_name'    => 'api',
                'form_id'       => '87',
                'action_id'     => '11',
                'module_id'     => '12',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_csv_employee_position_settings',
                'guard_name'    => 'api',
                'form_id'       => '87',
                'action_id'     => '22',
                'module_id'     => '12',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_pdf_employee_position_settings',
                'guard_name'    => 'api',
                'form_id'       => '87',
                'action_id'     => '12',
                'module_id'     => '12',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_email_employee_position_settings',
                'guard_name'    => 'api',
                'form_id'       => '87',
                'action_id'     => '13',
                'module_id'     => '12',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_view_employee_position_settings',
                'guard_name'    => 'api',
                'form_id'       => '87',
                'action_id'     => '18',
                'module_id'     => '12',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // Report Links Page
            
            [
                'name'          => 'show_reports_general',
                'guard_name'    => 'api',
                'form_id'       => '96',
                'action_id'     => '1',
                'module_id'     => '13',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            [
                'name'          => 'show_reports_sales',
                'guard_name'    => 'api',
                'form_id'       => '96',
                'action_id'     => '1',
                'module_id'     => '13',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            [
                'name'          => 'show_reports_visits',
                'guard_name'    => 'api',
                'form_id'       => '96',
                'action_id'     => '1',
                'module_id'     => '13',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            [
                'name'          => 'show_reports_gps',
                'guard_name'    => 'api',
                'form_id'       => '96',
                'action_id'     => '1',
                'module_id'     => '13',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            [
                'name'          => 'show_reports_coaching',
                'guard_name'    => 'api',
                'form_id'       => '96',
                'action_id'     => '1',
                'module_id'     => '13',
                'created_at'    => now(),
                'updated_at'    => now()
            ],


            // general_reports

            [
                'name'          => 'show_reports_general_structure',
                'guard_name'    => 'api',
                'form_id'       => '89',
                'action_id'     => '1',
                'module_id'     => '13',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            [
                'name'          => 'show_reports_general_product',
                'guard_name'    => 'api',
                'form_id'       => '89',
                'action_id'     => '1',
                'module_id'     => '13',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            [
                'name'          => 'show_reports_general_log_activity',
                'guard_name'    => 'api',
                'form_id'       => '89',
                'action_id'     => '1',
                'module_id'     => '13',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            [
                'name'          => 'show_reports_general_list',
                'guard_name'    => 'api',
                'form_id'       => '89',
                'action_id'     => '1',
                'module_id'     => '13',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            [
                'name'          => 'show_reports_general_list_statistics',
                'guard_name'    => 'api',
                'form_id'       => '89',
                'action_id'     => '1',
                'module_id'     => '13',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // sales_reports 

            [
                'name'          => 'show_reports_sales_analysis',
                'guard_name'    => 'api',
                'form_id'       => '90',
                'action_id'     => '1',
                'module_id'     => '13',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            [
                'name'          => 'show_reports_sales_graph',
                'guard_name'    => 'api',
                'form_id'       => '90',
                'action_id'     => '1',
                'module_id'     => '13',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            [
                'name'          => 'show_reports_sales_line',
                'guard_name'    => 'api',
                'form_id'       => '90',
                'action_id'     => '1',
                'module_id'     => '13',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            [
                'name'          => 'show_reports_sales_summary',
                'guard_name'    => 'api',
                'form_id'       => '90',
                'action_id'     => '1',
                'module_id'     => '13',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // visits_reports

            [
                'name'          => 'show_reports_visits_visits',
                'guard_name'    => 'api',
                'form_id'       => '91',
                'action_id'     => '1',
                'module_id'     => '13',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            [
                'name'          => 'show_reports_visits_coverage',
                'guard_name'    => 'api',
                'form_id'       => '91',
                'action_id'     => '1',
                'module_id'     => '13',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            [
                'name'          => 'show_reports_visits_call_rate',
                'guard_name'    => 'api',
                'form_id'       => '91',
                'action_id'     => '1',
                'module_id'     => '13',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            [
                'name'          => 'show_reports_visits_product_frequency',
                'guard_name'    => 'api',
                'form_id'       => '91',
                'action_id'     => '1',
                'module_id'     => '13',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            [
                'name'          => 'show_reports_visits_doctor_tracing',
                'guard_name'    => 'api',
                'form_id'       => '91',
                'action_id'     => '1',
                'module_id'     => '13',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            [
                'name'          => 'show_reports_visits_frequency_per_class',
                'guard_name'    => 'api',
                'form_id'       => '91',
                'action_id'     => '1',
                'module_id'     => '13',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            [
                'name'          => 'show_reports_visits_frequency_per_speciality',
                'guard_name'    => 'api',
                'form_id'       => '91',
                'action_id'     => '1',
                'module_id'     => '13',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            [
                'name'          => 'show_reports_visits_frequency_per_doctor',
                'guard_name'    => 'api',
                'form_id'       => '91',
                'action_id'     => '1',
                'module_id'     => '13',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            
            // gps_reports 

            [
                'name'          => 'show_reports_gps_employee_route',
                'guard_name'    => 'api',
                'form_id'       => '92',
                'action_id'     => '1',
                'module_id'     => '13',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            [
                'name'          => 'show_reports_gps_team_locations',
                'guard_name'    => 'api',
                'form_id'       => '92',
                'action_id'     => '1',
                'module_id'     => '13',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // coaching_reports 

            [
                'name'          => 'show_reports_coaching_performance',
                'guard_name'    => 'api',
                'form_id'       => '93',
                'action_id'     => '1',
                'module_id'     => '13',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            [
                'name'          => 'show_reports_coaching_performance_summary',
                'guard_name'    => 'api',
                'form_id'       => '93',
                'action_id'     => '1',
                'module_id'     => '13',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            [
                'name'          => 'show_reports_coaching_monthly_summary',
                'guard_name'    => 'api',
                'form_id'       => '93',
                'action_id'     => '1',
                'module_id'     => '13',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            [
                'name'          => 'show_reports_coaching_visits',
                'guard_name'    => 'api',
                'form_id'       => '93',
                'action_id'     => '1',
                'module_id'     => '13',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // user profile permissions
            [
                'name'          => 'change_profile_pic',
                'guard_name'    => 'api',
                'form_id'       => '94',
                'action_id'     => '15',
                'module_id'     => '14',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'change_password',
                'guard_name'    => 'api',
                'form_id'       => '94',
                'action_id'     => '14',
                'module_id'     => '14',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'show_single_user_profile',
                'guard_name'    => 'api',
                'form_id'       => '94',
                'action_id'     => '3',
                'module_id'     => '14',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'lock_account',
                'guard_name'    => 'api',
                'form_id'       => '94',
                'action_id'     => '21',
                'module_id'     => '14',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'show_all_support',
                'guard_name'    => 'api',
                'form_id'       => '94',
                'action_id'     => '1',
                'module_id'     => '14',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'show_user_profile_image',
                'guard_name'    => 'api',
                'form_id'       => '94',
                'action_id'     => '1',
                'module_id'     => '14',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'show_all_logos',
                'guard_name'    => 'api',
                'form_id'       => '94',
                'action_id'     => '1',
                'module_id'     => '14',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'show_all_company_logos',
                'guard_name'    => 'api',
                'form_id'       => '94',
                'action_id'     => '1',
                'module_id'     => '14',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // disapproval reasons
            [
                'name'          => 'show_all_disapproval_reasons',
                'guard_name'    => 'api',
                'form_id'       => '124',
                'action_id'     => '1',
                'module_id'     => '5',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'create_disapproval_reasons',
                'guard_name'    => 'api',
                'form_id'       => '124',
                'action_id'     => '2',
                'module_id'     => '5',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'create_plan_reasons',
                'guard_name'    => 'api',
                'form_id'       => '124',
                'action_id'     => '2',
                'module_id'     => '5',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_single_disapproval_reasons',
                'guard_name'    => 'api',
                'form_id'       => '124',
                'action_id'     => '3',
                'module_id'     => '5',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_disapproval_reasons',
                'guard_name'    => 'api',
                'form_id'       => '124',
                'action_id'     => '4',
                'module_id'     => '5',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'delete_disapproval_reasons',
                'guard_name'    => 'api',
                'form_id'       => '124',
                'action_id'     => '5',
                'module_id'     => '5',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'restore_disapproval_reasons',
                'guard_name'    => 'api',
                'form_id'       => '124',
                'action_id'     => '6',
                'module_id'     => '5',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_all_archive_disapproval_reasons',
                'guard_name'    => 'api',
                'form_id'       => '124',
                'action_id'     => '7',
                'module_id'     => '5',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'destroy_disapproval_reasons',
                'guard_name'    => 'api',
                'form_id'       => '124',
                'action_id'     => '8',
                'module_id'     => '5',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_view_disapproval_reasons',
                'guard_name'    => 'api',
                'form_id'       => '124',
                'action_id'     => '18',
                'module_id'     => '5',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // Plan Approvals
            [
                'name'          => 'show_all_plan_approvals',
                'guard_name'    => 'api',
                'form_id'       => '124',
                'action_id'     => '1',
                'module_id'     => '5',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'create_approve_plan',
                'guard_name'    => 'api',
                'form_id'       => '124',
                'action_id'     => '2',
                'module_id'     => '5',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'create_disapprove_plan',
                'guard_name'    => 'api',
                'form_id'       => '124',
                'action_id'     => '3',
                'module_id'     => '5',
                'created_at'    => now(),
                'updated_at'    => now()
            ],



            // Commercial Cost

            [
                'name'          => 'show_all_cost_types',
                'guard_name'    => 'api',
                'form_id'       => '114',
                'action_id'     => '1',
                'module_id'     => '9',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'create_cost_types',
                'guard_name'    => 'api',
                'form_id'       => '114',
                'action_id'     => '2',
                'module_id'     => '9',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_single_cost_types',
                'guard_name'    => 'api',
                'form_id'       => '114',
                'action_id'     => '3',
                'module_id'     => '9',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_cost_types',
                'guard_name'    => 'api',
                'form_id'       => '114',
                'action_id'     => '4',
                'module_id'     => '9',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'delete_cost_types',
                'guard_name'    => 'api',
                'form_id'       => '114',
                'action_id'     => '5',
                'module_id'     => '9',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'restore_cost_types',
                'guard_name'    => 'api',
                'form_id'       => '114',
                'action_id'     => '6',
                'module_id'     => '9',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_all_archive_cost_types',
                'guard_name'    => 'api',
                'form_id'       => '114',
                'action_id'     => '7',
                'module_id'     => '9',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'destroy_cost_types',
                'guard_name'    => 'api',
                'form_id'       => '114',
                'action_id'     => '8',
                'module_id'     => '9',
                'created_at'    => now(),
                'updated_at'    => now()
            ],  [
                'name'          => 'import_cost_types',
                'guard_name'    => 'api',
                'form_id'       => '114',
                'action_id'     => '10',
                'module_id'     => '9',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_xlsx_cost_types',
                'guard_name'    => 'api',
                'form_id'       => '114',
                'action_id'     => '11',
                'module_id'     => '9',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_csv_cost_types',
                'guard_name'    => 'api',
                'form_id'       => '114',
                'action_id'     => '22',
                'module_id'     => '9',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_pdf_cost_types',
                'guard_name'    => 'api',
                'form_id'       => '114',
                'action_id'     => '12',
                'module_id'     => '9',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_email_cost_types',
                'guard_name'    => 'api',
                'form_id'       => '114',
                'action_id'     => '13',
                'module_id'     => '9',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_view_cost_types',
                'guard_name'    => 'api',
                'form_id'       => '114',
                'action_id'     => '18',
                'module_id'     => '9',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // Double Plan

            [
                'name'          => 'show_all_double_plans',
                'guard_name'    => 'api',
                'form_id'       => '125',
                'action_id'     => '1',
                'module_id'     => '5',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'create_double_plans',
                'guard_name'    => 'api',
                'form_id'       => '125',
                'action_id'     => '2',
                'module_id'     => '5',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'show_single_double_plans',
                'guard_name'    => 'api',
                'form_id'       => '125',
                'action_id'     => '3',
                'module_id'     => '5',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_double_plans',
                'guard_name'    => 'api',
                'form_id'       => '125',
                'action_id'     => '4',
                'module_id'     => '5',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'delete_double_plans',
                'guard_name'    => 'api',
                'form_id'       => '125',
                'action_id'     => '5',
                'module_id'     => '5',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'restore_double_plans',
                'guard_name'    => 'api',
                'form_id'       => '125',
                'action_id'     => '6',
                'module_id'     => '5',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_all_archive_double_plans',
                'guard_name'    => 'api',
                'form_id'       => '125',
                'action_id'     => '7',
                'module_id'     => '5',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'destroy_double_plans',
                'guard_name'    => 'api',
                'form_id'       => '125',
                'action_id'     => '8',
                'module_id'     => '5',
                'created_at'    => now(),
                'updated_at'    => now()
            ],  [
                'name'          => 'import_double_plans',
                'guard_name'    => 'api',
                'form_id'       => '125',
                'action_id'     => '10',
                'module_id'     => '5',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_xlsx_double_plans',
                'guard_name'    => 'api',
                'form_id'       => '125',
                'action_id'     => '11',
                'module_id'     => '5',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_csv_double_plans',
                'guard_name'    => 'api',
                'form_id'       => '125',
                'action_id'     => '22',
                'module_id'     => '5',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_pdf_double_plans',
                'guard_name'    => 'api',
                'form_id'       => '125',
                'action_id'     => '12',
                'module_id'     => '5',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_email_double_plans',
                'guard_name'    => 'api',
                'form_id'       => '125',
                'action_id'     => '13',
                'module_id'     => '5',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_view_double_plans',
                'guard_name'    => 'api',
                'form_id'       => '125',
                'action_id'     => '18',
                'module_id'     => '5',
                'created_at'    => now(),
                'updated_at'    => now()
            ],







            // notes

            [
                'name'          => 'show_all_notes',
                'guard_name'    => 'api',
                'form_id'       => '99',
                'action_id'     => '1',
                'module_id'     => '15',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'create_notes',
                'guard_name'    => 'api',
                'form_id'       => '99',
                'action_id'     => '2',
                'module_id'     => '15',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_single_notes',
                'guard_name'    => 'api',
                'form_id'       => '99',
                'action_id'     => '3',
                'module_id'     => '15',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_notes',
                'guard_name'    => 'api',
                'form_id'       => '99',
                'action_id'     => '4',
                'module_id'     => '15',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'delete_notes',
                'guard_name'    => 'api',
                'form_id'       => '99',
                'action_id'     => '5',
                'module_id'     => '15',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'restore_notes',
                'guard_name'    => 'api',
                'form_id'       => '99',
                'action_id'     => '6',
                'module_id'     => '15',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_all_archive_notes',
                'guard_name'    => 'api',
                'form_id'       => '99',
                'action_id'     => '7',
                'module_id'     => '15',
                'created_at'    => now(),
                'updated_at'    => now()
            ],  [
                'name'          => 'import_notes',
                'guard_name'    => 'api',
                'form_id'       => '99',
                'action_id'     => '10',
                'module_id'     => '15',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_xlsx_notes',
                'guard_name'    => 'api',
                'form_id'       => '99',
                'action_id'     => '11',
                'module_id'     => '15',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_csv_notes',
                'guard_name'    => 'api',
                'form_id'       => '99',
                'action_id'     => '22',
                'module_id'     => '15',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_pdf_notes',
                'guard_name'    => 'api',
                'form_id'       => '99',
                'action_id'     => '12',
                'module_id'     => '15',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_email_notes',
                'guard_name'    => 'api',
                'form_id'       => '99',
                'action_id'     => '13',
                'module_id'     => '15',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_view_notes',
                'guard_name'    => 'api',
                'form_id'       => '99',
                'action_id'     => '18',
                'module_id'     => '15',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // Download Templates

            [
                'name'          => 'download_all_templates',
                'guard_name'    => 'api',
                'form_id'       => '100',
                'action_id'     => '9',
                'module_id'     => '15',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_files',
                'guard_name'    => 'api',
                'form_id'       => '100',
                'action_id'     => '9',
                'module_id'     => '15',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // Support
            [
                'name'          => 'show_all_supports',
                'guard_name'    => 'api',
                'form_id'       => '128',
                'action_id'     => '1',
                'module_id'     => '15',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'create_supports',
                'guard_name'    => 'api',
                'form_id'       => '128',
                'action_id'     => '2',
                'module_id'     => '15',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_single_supports',
                'guard_name'    => 'api',
                'form_id'       => '128',
                'action_id'     => '3',
                'module_id'     => '15',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_supports',
                'guard_name'    => 'api',
                'form_id'       => '128',
                'action_id'     => '4',
                'module_id'     => '15',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'delete_supports',
                'guard_name'    => 'api',
                'form_id'       => '128',
                'action_id'     => '5',
                'module_id'     => '15',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'restore_supports',
                'guard_name'    => 'api',
                'form_id'       => '128',
                'action_id'     => '6',
                'module_id'     => '15',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_all_archive_supports',
                'guard_name'    => 'api',
                'form_id'       => '128',
                'action_id'     => '7',
                'module_id'     => '15',
                'created_at'    => now(),
                'updated_at'    => now()
            ],  [
                'name'          => 'import_supports',
                'guard_name'    => 'api',
                'form_id'       => '128',
                'action_id'     => '10',
                'module_id'     => '15',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_xlsx_supports',
                'guard_name'    => 'api',
                'form_id'       => '128',
                'action_id'     => '11',
                'module_id'     => '15',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_csv_supports',
                'guard_name'    => 'api',
                'form_id'       => '128',
                'action_id'     => '22',
                'module_id'     => '15',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_pdf_supports',
                'guard_name'    => 'api',
                'form_id'       => '128',
                'action_id'     => '12',
                'module_id'     => '15',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_email_supports',
                'guard_name'    => 'api',
                'form_id'       => '128',
                'action_id'     => '13',
                'module_id'     => '15',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_view_supports',
                'guard_name'    => 'api',
                'form_id'       => '128',
                'action_id'     => '18',
                'module_id'     => '15',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // Mail Sender
            [
                'name'          => 'show_all_mails',
                'guard_name'    => 'api',
                'form_id'       => '129',
                'action_id'     => '1',
                'module_id'     => '15',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'create_mails',
                'guard_name'    => 'api',
                'form_id'       => '129',
                'action_id'     => '2',
                'module_id'     => '15',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_single_mails',
                'guard_name'    => 'api',
                'form_id'       => '129',
                'action_id'     => '3',
                'module_id'     => '15',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_mails',
                'guard_name'    => 'api',
                'form_id'       => '129',
                'action_id'     => '4',
                'module_id'     => '15',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'delete_mails',
                'guard_name'    => 'api',
                'form_id'       => '129',
                'action_id'     => '5',
                'module_id'     => '15',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'restore_mails',
                'guard_name'    => 'api',
                'form_id'       => '129',
                'action_id'     => '6',
                'module_id'     => '15',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_all_archive_mails',
                'guard_name'    => 'api',
                'form_id'       => '129',
                'action_id'     => '7',
                'module_id'     => '15',
                'created_at'    => now(),
                'updated_at'    => now()
            ],  [
                'name'          => 'import_mails',
                'guard_name'    => 'api',
                'form_id'       => '129',
                'action_id'     => '10',
                'module_id'     => '15',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_xlsx_mails',
                'guard_name'    => 'api',
                'form_id'       => '129',
                'action_id'     => '11',
                'module_id'     => '15',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_csv_mails',
                'guard_name'    => 'api',
                'form_id'       => '129',
                'action_id'     => '22',
                'module_id'     => '15',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_pdf_mails',
                'guard_name'    => 'api',
                'form_id'       => '129',
                'action_id'     => '12',
                'module_id'     => '15',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_email_mails',
                'guard_name'    => 'api',
                'form_id'       => '129',
                'action_id'     => '13',
                'module_id'     => '15',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_view_mails',
                'guard_name'    => 'api',
                'form_id'       => '129',
                'action_id'     => '18',
                'module_id'     => '15',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // Requests

            //personal request

            [
                'name'          => 'show_all_personal_requests',
                'guard_name'    => 'api',
                'form_id'       => '103',
                'action_id'     => '1',
                'module_id'     => '9',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'create_personal_requests',
                'guard_name'    => 'api',
                'form_id'       => '103',
                'action_id'     => '2',
                'module_id'     => '9',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_single_personal_requests',
                'guard_name'    => 'api',
                'form_id'       => '103',
                'action_id'     => '3',
                'module_id'     => '9',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_personal_requests',
                'guard_name'    => 'api',
                'form_id'       => '103',
                'action_id'     => '4',
                'module_id'     => '9',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'delete_personal_requests',
                'guard_name'    => 'api',
                'form_id'       => '103',
                'action_id'     => '5',
                'module_id'     => '9',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'restore_personal_requests',
                'guard_name'    => 'api',
                'form_id'       => '103',
                'action_id'     => '6',
                'module_id'     => '9',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_all_archive_personal_requests',
                'guard_name'    => 'api',
                'form_id'       => '103',
                'action_id'     => '7',
                'module_id'     => '9',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            [
                'name'          => 'import_personal_requests',
                'guard_name'    => 'api',
                'form_id'       => '103',
                'action_id'     => '10',
                'module_id'     => '9',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_xlsx_personal_requests',
                'guard_name'    => 'api',
                'form_id'       => '103',
                'action_id'     => '11',
                'module_id'     => '9',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_csv_personal_requests',
                'guard_name'    => 'api',
                'form_id'       => '103',
                'action_id'     => '22',
                'module_id'     => '9',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_pdf_personal_requests',
                'guard_name'    => 'api',
                'form_id'       => '103',
                'action_id'     => '12',
                'module_id'     => '9',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_email_personal_requests',
                'guard_name'    => 'api',
                'form_id'       => '103',
                'action_id'     => '13',
                'module_id'     => '9',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_view_personal_requests',
                'guard_name'    => 'api',
                'form_id'       => '103',
                'action_id'     => '18',
                'module_id'     => '9',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            //personal request Types

            [
                'name'          => 'show_all_personal_request_types',
                'guard_name'    => 'api',
                'form_id'       => '104',
                'action_id'     => '1',
                'module_id'     => '9',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'create_personal_request_types',
                'guard_name'    => 'api',
                'form_id'       => '104',
                'action_id'     => '2',
                'module_id'     => '9',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_single_personal_request_types',
                'guard_name'    => 'api',
                'form_id'       => '104',
                'action_id'     => '3',
                'module_id'     => '9',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_personal_request_types',
                'guard_name'    => 'api',
                'form_id'       => '104',
                'action_id'     => '4',
                'module_id'     => '9',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'delete_personal_request_types',
                'guard_name'    => 'api',
                'form_id'       => '104',
                'action_id'     => '5',
                'module_id'     => '9',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'restore_personal_request_types',
                'guard_name'    => 'api',
                'form_id'       => '104',
                'action_id'     => '6',
                'module_id'     => '9',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_all_archive_personal_request_types',
                'guard_name'    => 'api',
                'form_id'       => '104',
                'action_id'     => '7',
                'module_id'     => '9',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'import_personal_request_types',
                'guard_name'    => 'api',
                'form_id'       => '104',
                'action_id'     => '10',
                'module_id'     => '9',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_xlsx_personal_request_types',
                'guard_name'    => 'api',
                'form_id'       => '104',
                'action_id'     => '11',
                'module_id'     => '9',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_csv_personal_request_types',
                'guard_name'    => 'api',
                'form_id'       => '104',
                'action_id'     => '22',
                'module_id'     => '9',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_pdf_personal_request_types',
                'guard_name'    => 'api',
                'form_id'       => '104',
                'action_id'     => '12',
                'module_id'     => '9',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_email_personal_request_types',
                'guard_name'    => 'api',
                'form_id'       => '104',
                'action_id'     => '13',
                'module_id'     => '9',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_view_personal_request_types',
                'guard_name'    => 'api',
                'form_id'       => '104',
                'action_id'     => '18',
                'module_id'     => '9',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // Commercial

            [
                'name'          => 'show_all_commercial_requests',
                'guard_name'    => 'api',
                'form_id'       => '116',
                'action_id'     => '1',
                'module_id'     => '9',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'create_commercial_requests',
                'guard_name'    => 'api',
                'form_id'       => '116',
                'action_id'     => '2',
                'module_id'     => '9',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'create_commercial_attachments',
                'guard_name'    => 'api',
                'form_id'       => '116',
                'action_id'     => '2',
                'module_id'     => '9',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_single_commercial_requests',
                'guard_name'    => 'api',
                'form_id'       => '116',
                'action_id'     => '3',
                'module_id'     => '9',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_commercial_requests',
                'guard_name'    => 'api',
                'form_id'       => '116',
                'action_id'     => '4',
                'module_id'     => '9',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'delete_commercial_requests',
                'guard_name'    => 'api',
                'form_id'       => '116',
                'action_id'     => '5',
                'module_id'     => '9',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'restore_commercial_requests',
                'guard_name'    => 'api',
                'form_id'       => '116',
                'action_id'     => '6',
                'module_id'     => '9',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_all_archive_commercial_requests',
                'guard_name'    => 'api',
                'form_id'       => '116',
                'action_id'     => '7',
                'module_id'     => '9',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'import_commercial_requests',
                'guard_name'    => 'api',
                'form_id'       => '116',
                'action_id'     => '10',
                'module_id'     => '9',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_xlsx_commercial_requests',
                'guard_name'    => 'api',
                'form_id'       => '116',
                'action_id'     => '11',
                'module_id'     => '9',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_csv_commercial_requests',
                'guard_name'    => 'api',
                'form_id'       => '116',
                'action_id'     => '22',
                'module_id'     => '9',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_pdf_commercial_requests',
                'guard_name'    => 'api',
                'form_id'       => '116',
                'action_id'     => '12',
                'module_id'     => '9',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_email_commercial_requests',
                'guard_name'    => 'api',
                'form_id'       => '116',
                'action_id'     => '13',
                'module_id'     => '9',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_view_commercial_requests',
                'guard_name'    => 'api',
                'form_id'       => '116',
                'action_id'     => '18',
                'module_id'     => '9',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // Expense Types

            [
                'name'          => 'show_all_expense_types',
                'guard_name'    => 'api',
                'form_id'       => '117',
                'action_id'     => '1',
                'module_id'     => '9',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'create_expense_types',
                'guard_name'    => 'api',
                'form_id'       => '117',
                'action_id'     => '2',
                'module_id'     => '9',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_single_expense_types',
                'guard_name'    => 'api',
                'form_id'       => '117',
                'action_id'     => '3',
                'module_id'     => '9',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_expense_types',
                'guard_name'    => 'api',
                'form_id'       => '117',
                'action_id'     => '4',
                'module_id'     => '9',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'delete_expense_types',
                'guard_name'    => 'api',
                'form_id'       => '117',
                'action_id'     => '5',
                'module_id'     => '9',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'restore_expense_types',
                'guard_name'    => 'api',
                'form_id'       => '117',
                'action_id'     => '6',
                'module_id'     => '9',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_all_archive_expense_types',
                'guard_name'    => 'api',
                'form_id'       => '117',
                'action_id'     => '7',
                'module_id'     => '9',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'import_expense_types',
                'guard_name'    => 'api',
                'form_id'       => '117',
                'action_id'     => '10',
                'module_id'     => '9',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_xlsx_expense_types',
                'guard_name'    => 'api',
                'form_id'       => '117',
                'action_id'     => '11',
                'module_id'     => '9',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_csv_expense_types',
                'guard_name'    => 'api',
                'form_id'       => '117',
                'action_id'     => '22',
                'module_id'     => '9',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_pdf_expense_types',
                'guard_name'    => 'api',
                'form_id'       => '117',
                'action_id'     => '12',
                'module_id'     => '9',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_email_expense_types',
                'guard_name'    => 'api',
                'form_id'       => '117',
                'action_id'     => '13',
                'module_id'     => '9',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_view_expense_types',
                'guard_name'    => 'api',
                'form_id'       => '117',
                'action_id'     => '18',
                'module_id'     => '9',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // Expense Meals

            [
                'name'          => 'show_all_expense_meals',
                'guard_name'    => 'api',
                'form_id'       => '117',
                'action_id'     => '1',
                'module_id'     => '9',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'show_single_expense_meals',
                'guard_name'    => 'api',
                'form_id'       => '117',
                'action_id'     => '3',
                'module_id'     => '9',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_expense_meals',
                'guard_name'    => 'api',
                'form_id'       => '117',
                'action_id'     => '4',
                'module_id'     => '9',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'delete_expense_meals',
                'guard_name'    => 'api',
                'form_id'       => '117',
                'action_id'     => '5',
                'module_id'     => '9',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            // [
            //     'name'          => 'restore_expense_types',
            //     'guard_name'    => 'api',
            //     'form_id'       => '117',
            //     'action_id'     => '6',
            //     'module_id'     => '9',
            //     'created_at'    => now(),
            //     'updated_at'    => now()
            // ], [
            //     'name'          => 'show_all_archive_expense_types',
            //     'guard_name'    => 'api',
            //     'form_id'       => '117',
            //     'action_id'     => '7',
            //     'module_id'     => '9',
            //     'created_at'    => now(),
            //     'updated_at'    => now()
            // ], [
            //     'name'          => 'import_expense_types',
            //     'guard_name'    => 'api',
            //     'form_id'       => '117',
            //     'action_id'     => '10',
            //     'module_id'     => '9',
            //     'created_at'    => now(),
            //     'updated_at'    => now()
            // ], [
            //     'name'          => 'export_xlsx_expense_types',
            //     'guard_name'    => 'api',
            //     'form_id'       => '117',
            //     'action_id'     => '11',
            //     'module_id'     => '9',
            //     'created_at'    => now(),
            //     'updated_at'    => now()
            // ],
            // [
            //     'name'          => 'export_csv_expense_types',
            //     'guard_name'    => 'api',
            //     'form_id'       => '117',
            //     'action_id'     => '22',
            //     'module_id'     => '9',
            //     'created_at'    => now(),
            //     'updated_at'    => now()
            // ], [
            //     'name'          => 'export_pdf_expense_types',
            //     'guard_name'    => 'api',
            //     'form_id'       => '117',
            //     'action_id'     => '12',
            //     'module_id'     => '9',
            //     'created_at'    => now(),
            //     'updated_at'    => now()
            // ], [
            //     'name'          => 'export_email_expense_types',
            //     'guard_name'    => 'api',
            //     'form_id'       => '117',
            //     'action_id'     => '13',
            //     'module_id'     => '9',
            //     'created_at'    => now(),
            //     'updated_at'    => now()
            // ],
            // [
            //     'name'          => 'edit_view_expense_types',
            //     'guard_name'    => 'api',
            //     'form_id'       => '117',
            //     'action_id'     => '18',
            //     'module_id'     => '9',
            //     'created_at'    => now(),
            //     'updated_at'    => now()
            // ],

            // Expenses

            [
                'name'          => 'show_all_expenses',
                'guard_name'    => 'api',
                'form_id'       => '118',
                'action_id'     => '1',
                'module_id'     => '9',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'create_expenses',
                'guard_name'    => 'api',
                'form_id'       => '118',
                'action_id'     => '2',
                'module_id'     => '9',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'create_expense_attachments',
                'guard_name'    => 'api',
                'form_id'       => '118',
                'action_id'     => '2',
                'module_id'     => '9',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_single_expenses',
                'guard_name'    => 'api',
                'form_id'       => '118',
                'action_id'     => '3',
                'module_id'     => '9',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_expenses',
                'guard_name'    => 'api',
                'form_id'       => '118',
                'action_id'     => '4',
                'module_id'     => '9',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'delete_expenses',
                'guard_name'    => 'api',
                'form_id'       => '118',
                'action_id'     => '5',
                'module_id'     => '9',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'restore_expenses',
                'guard_name'    => 'api',
                'form_id'       => '118',
                'action_id'     => '6',
                'module_id'     => '9',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_all_archive_expenses',
                'guard_name'    => 'api',
                'form_id'       => '118',
                'action_id'     => '7',
                'module_id'     => '9',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'import_expenses',
                'guard_name'    => 'api',
                'form_id'       => '118',
                'action_id'     => '10',
                'module_id'     => '9',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_xlsx_expenses',
                'guard_name'    => 'api',
                'form_id'       => '118',
                'action_id'     => '11',
                'module_id'     => '9',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_csv_expenses',
                'guard_name'    => 'api',
                'form_id'       => '118',
                'action_id'     => '22',
                'module_id'     => '9',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_pdf_expenses',
                'guard_name'    => 'api',
                'form_id'       => '118',
                'action_id'     => '12',
                'module_id'     => '9',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_email_expenses',
                'guard_name'    => 'api',
                'form_id'       => '118',
                'action_id'     => '13',
                'module_id'     => '9',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_view_expenses',
                'guard_name'    => 'api',
                'form_id'       => '118',
                'action_id'     => '18',
                'module_id'     => '9',
                'created_at'    => now(),
                'updated_at'    => now()
            ],


            // Help Module Permissions

            // help permissions
            [
                'name'          => 'access_button_help_data',
                'guard_name'    => 'api',
                'form_id'       => '95',
                'action_id'     => '21',
                'module_id'     => '16',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // Main topics

            [
                'name'          => 'show_all_main_topics',
                'guard_name'    => 'api',
                'form_id'       => '110',
                'action_id'     => '1',
                'module_id'     => '16',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'create_main_topics',
                'guard_name'    => 'api',
                'form_id'       => '110',
                'action_id'     => '2',
                'module_id'     => '16',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_single_main_topics',
                'guard_name'    => 'api',
                'form_id'       => '110',
                'action_id'     => '3',
                'module_id'     => '16',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_main_topics',
                'guard_name'    => 'api',
                'form_id'       => '110',
                'action_id'     => '4',
                'module_id'     => '16',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'delete_main_topics',
                'guard_name'    => 'api',
                'form_id'       => '110',
                'action_id'     => '5',
                'module_id'     => '16',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'restore_main_topics',
                'guard_name'    => 'api',
                'form_id'       => '110',
                'action_id'     => '6',
                'module_id'     => '16',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_all_archive_main_topics',
                'guard_name'    => 'api',
                'form_id'       => '110',
                'action_id'     => '7',
                'module_id'     => '16',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'destroy_main_topics',
                'guard_name'    => 'api',
                'form_id'       => '110',
                'action_id'     => '8',
                'module_id'     => '16',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'import_main_topics',
                'guard_name'    => 'api',
                'form_id'       => '110',
                'action_id'     => '10',
                'module_id'     => '16',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_xlsx_main_topics',
                'guard_name'    => 'api',
                'form_id'       => '110',
                'action_id'     => '11',
                'module_id'     => '16',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_csv_main_topics',
                'guard_name'    => 'api',
                'form_id'       => '110',
                'action_id'     => '22',
                'module_id'     => '16',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_pdf_main_topics',
                'guard_name'    => 'api',
                'form_id'       => '110',
                'action_id'     => '12',
                'module_id'     => '16',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_email_main_topics',
                'guard_name'    => 'api',
                'form_id'       => '110',
                'action_id'     => '13',
                'module_id'     => '16',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_view_main_topic',
                'guard_name'    => 'api',
                'form_id'       => '110',
                'action_id'     => '18',
                'module_id'     => '16',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // Sub topics

            [
                'name'          => 'show_all_sub_topics',
                'guard_name'    => 'api',
                'form_id'       => '111',
                'action_id'     => '1',
                'module_id'     => '16',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'create_sub_topics',
                'guard_name'    => 'api',
                'form_id'       => '111',
                'action_id'     => '2',
                'module_id'     => '16',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_single_sub_topics',
                'guard_name'    => 'api',
                'form_id'       => '111',
                'action_id'     => '3',
                'module_id'     => '16',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_sub_topics',
                'guard_name'    => 'api',
                'form_id'       => '111',
                'action_id'     => '4',
                'module_id'     => '16',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'delete_sub_topics',
                'guard_name'    => 'api',
                'form_id'       => '111',
                'action_id'     => '5',
                'module_id'     => '16',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'restore_sub_topics',
                'guard_name'    => 'api',
                'form_id'       => '111',
                'action_id'     => '6',
                'module_id'     => '16',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_all_archive_sub_topics',
                'guard_name'    => 'api',
                'form_id'       => '111',
                'action_id'     => '7',
                'module_id'     => '16',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'destroy_sub_topics',
                'guard_name'    => 'api',
                'form_id'       => '111',
                'action_id'     => '8',
                'module_id'     => '16',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'import_sub_topics',
                'guard_name'    => 'api',
                'form_id'       => '111',
                'action_id'     => '10',
                'module_id'     => '16',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_xlsx_sub_topics',
                'guard_name'    => 'api',
                'form_id'       => '111',
                'action_id'     => '11',
                'module_id'     => '16',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_csv_sub_topics',
                'guard_name'    => 'api',
                'form_id'       => '111',
                'action_id'     => '22',
                'module_id'     => '16',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_pdf_sub_topics',
                'guard_name'    => 'api',
                'form_id'       => '111',
                'action_id'     => '12',
                'module_id'     => '16',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_email_sub_topics',
                'guard_name'    => 'api',
                'form_id'       => '111',
                'action_id'     => '13',
                'module_id'     => '16',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_view_sub_topic',
                'guard_name'    => 'api',
                'form_id'       => '111',
                'action_id'     => '18',
                'module_id'     => '16',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // articles

            [
                'name'          => 'show_all_articles',
                'guard_name'    => 'api',
                'form_id'       => '109',
                'action_id'     => '1',
                'module_id'     => '16',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'create_articles',
                'guard_name'    => 'api',
                'form_id'       => '109',
                'action_id'     => '2',
                'module_id'     => '16',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_single_articles',
                'guard_name'    => 'api',
                'form_id'       => '109',
                'action_id'     => '3',
                'module_id'     => '16',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_articles',
                'guard_name'    => 'api',
                'form_id'       => '109',
                'action_id'     => '4',
                'module_id'     => '16',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'delete_articles',
                'guard_name'    => 'api',
                'form_id'       => '109',
                'action_id'     => '5',
                'module_id'     => '16',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'restore_articles',
                'guard_name'    => 'api',
                'form_id'       => '109',
                'action_id'     => '6',
                'module_id'     => '16',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_all_archive_articles',
                'guard_name'    => 'api',
                'form_id'       => '109',
                'action_id'     => '7',
                'module_id'     => '16',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'destroy_articles',
                'guard_name'    => 'api',
                'form_id'       => '109',
                'action_id'     => '8',
                'module_id'     => '16',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'import_articles',
                'guard_name'    => 'api',
                'form_id'       => '109',
                'action_id'     => '10',
                'module_id'     => '16',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_xlsx_articles',
                'guard_name'    => 'api',
                'form_id'       => '109',
                'action_id'     => '11',
                'module_id'     => '16',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_csv_articles',
                'guard_name'    => 'api',
                'form_id'       => '109',
                'action_id'     => '22',
                'module_id'     => '16',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_pdf_articles',
                'guard_name'    => 'api',
                'form_id'       => '109',
                'action_id'     => '12',
                'module_id'     => '16',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_email_articles',
                'guard_name'    => 'api',
                'form_id'       => '109',
                'action_id'     => '13',
                'module_id'     => '16',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_view_articles',
                'guard_name'    => 'api',
                'form_id'       => '109',
                'action_id'     => '18',
                'module_id'     => '16',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // Keywords

            [
                'name'          => 'show_all_keywords',
                'guard_name'    => 'api',
                'form_id'       => '112',
                'action_id'     => '1',
                'module_id'     => '16',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'create_keywords',
                'guard_name'    => 'api',
                'form_id'       => '112',
                'action_id'     => '2',
                'module_id'     => '16',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_single_keywords',
                'guard_name'    => 'api',
                'form_id'       => '112',
                'action_id'     => '3',
                'module_id'     => '16',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_keywords',
                'guard_name'    => 'api',
                'form_id'       => '112',
                'action_id'     => '4',
                'module_id'     => '16',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'delete_keywords',
                'guard_name'    => 'api',
                'form_id'       => '112',
                'action_id'     => '5',
                'module_id'     => '16',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'restore_keywords',
                'guard_name'    => 'api',
                'form_id'       => '112',
                'action_id'     => '6',
                'module_id'     => '16',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_all_archive_keywords',
                'guard_name'    => 'api',
                'form_id'       => '112',
                'action_id'     => '7',
                'module_id'     => '16',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'destroy_keywords',
                'guard_name'    => 'api',
                'form_id'       => '112',
                'action_id'     => '8',
                'module_id'     => '16',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'import_keywords',
                'guard_name'    => 'api',
                'form_id'       => '112',
                'action_id'     => '10',
                'module_id'     => '16',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_xlsx_keywords',
                'guard_name'    => 'api',
                'form_id'       => '112',
                'action_id'     => '11',
                'module_id'     => '16',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_csv_keywords',
                'guard_name'    => 'api',
                'form_id'       => '112',
                'action_id'     => '22',
                'module_id'     => '16',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_pdf_keywords',
                'guard_name'    => 'api',
                'form_id'       => '112',
                'action_id'     => '12',
                'module_id'     => '16',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_email_keywords',
                'guard_name'    => 'api',
                'form_id'       => '112',
                'action_id'     => '13',
                'module_id'     => '16',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_view_keyword',
                'guard_name'    => 'api',
                'form_id'       => '112',
                'action_id'     => '18',
                'module_id'     => '16',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // topics

            [
                'name'          => 'show_all_topics',
                'guard_name'    => 'api',
                'form_id'       => '113',
                'action_id'     => '1',
                'module_id'     => '16',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'create_topics',
                'guard_name'    => 'api',
                'form_id'       => '113',
                'action_id'     => '2',
                'module_id'     => '16',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_single_topics',
                'guard_name'    => 'api',
                'form_id'       => '113',
                'action_id'     => '3',
                'module_id'     => '16',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_topics',
                'guard_name'    => 'api',
                'form_id'       => '113',
                'action_id'     => '4',
                'module_id'     => '16',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'delete_topics',
                'guard_name'    => 'api',
                'form_id'       => '113',
                'action_id'     => '5',
                'module_id'     => '16',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'restore_topics',
                'guard_name'    => 'api',
                'form_id'       => '113',
                'action_id'     => '6',
                'module_id'     => '16',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_all_archive_topics',
                'guard_name'    => 'api',
                'form_id'       => '113',
                'action_id'     => '7',
                'module_id'     => '16',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'destroy_topics',
                'guard_name'    => 'api',
                'form_id'       => '113',
                'action_id'     => '8',
                'module_id'     => '16',
                'created_at'    => now(),
                'updated_at'    => now()
            ],  [
                'name'          => 'import_topics',
                'guard_name'    => 'api',
                'form_id'       => '113',
                'action_id'     => '10',
                'module_id'     => '16',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_xlsx_topics',
                'guard_name'    => 'api',
                'form_id'       => '113',
                'action_id'     => '11',
                'module_id'     => '16',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_csv_topics',
                'guard_name'    => 'api',
                'form_id'       => '113',
                'action_id'     => '22',
                'module_id'     => '16',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_pdf_topics',
                'guard_name'    => 'api',
                'form_id'       => '113',
                'action_id'     => '12',
                'module_id'     => '16',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_email_topics',
                'guard_name'    => 'api',
                'form_id'       => '113',
                'action_id'     => '13',
                'module_id'     => '16',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_view_topic',
                'guard_name'    => 'api',
                'form_id'       => '113',
                'action_id'     => '18',
                'module_id'     => '16',
                'created_at'    => now(),
                'updated_at'    => now()
            ],


            // Request Types

            [
                'name'          => 'show_all_request_types',
                'guard_name'    => 'api',
                'form_id'       => '115',
                'action_id'     => '1',
                'module_id'     => '9',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'create_request_types',
                'guard_name'    => 'api',
                'form_id'       => '115',
                'action_id'     => '2',
                'module_id'     => '9',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_single_request_types',
                'guard_name'    => 'api',
                'form_id'       => '115',
                'action_id'     => '3',
                'module_id'     => '9',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_request_types',
                'guard_name'    => 'api',
                'form_id'       => '115',
                'action_id'     => '4',
                'module_id'     => '9',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'delete_request_types',
                'guard_name'    => 'api',
                'form_id'       => '115',
                'action_id'     => '5',
                'module_id'     => '9',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'restore_request_types',
                'guard_name'    => 'api',
                'form_id'       => '115',
                'action_id'     => '6',
                'module_id'     => '9',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_all_archive_request_types',
                'guard_name'    => 'api',
                'form_id'       => '115',
                'action_id'     => '7',
                'module_id'     => '9',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'destroy_request_types',
                'guard_name'    => 'api',
                'form_id'       => '115',
                'action_id'     => '8',
                'module_id'     => '9',
                'created_at'    => now(),
                'updated_at'    => now()
            ],  [
                'name'          => 'import_request_types',
                'guard_name'    => 'api',
                'form_id'       => '115',
                'action_id'     => '10',
                'module_id'     => '9',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_xlsx_request_types',
                'guard_name'    => 'api',
                'form_id'       => '115',
                'action_id'     => '11',
                'module_id'     => '9',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_csv_request_types',
                'guard_name'    => 'api',
                'form_id'       => '115',
                'action_id'     => '22',
                'module_id'     => '9',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_pdf_request_types',
                'guard_name'    => 'api',
                'form_id'       => '115',
                'action_id'     => '12',
                'module_id'     => '9',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_email_request_types',
                'guard_name'    => 'api',
                'form_id'       => '115',
                'action_id'     => '13',
                'module_id'     => '9',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_view_request_type',
                'guard_name'    => 'api',
                'form_id'       => '115',
                'action_id'     => '18',
                'module_id'     => '9',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // coaching types
            [
                'name'          => 'show_all_types',
                'guard_name'    => 'api',
                'form_id'       => '130',
                'action_id'     => '1',
                'module_id'     => '19',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'create_types',
                'guard_name'    => 'api',
                'form_id'       => '130',
                'action_id'     => '2',
                'module_id'     => '19',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_single_types',
                'guard_name'    => 'api',
                'form_id'       => '130',
                'action_id'     => '3',
                'module_id'     => '19',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_types',
                'guard_name'    => 'api',
                'form_id'       => '130',
                'action_id'     => '4',
                'module_id'     => '19',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'delete_types',
                'guard_name'    => 'api',
                'form_id'       => '130',
                'action_id'     => '5',
                'module_id'     => '19',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'restore_types',
                'guard_name'    => 'api',
                'form_id'       => '130',
                'action_id'     => '6',
                'module_id'     => '19',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_all_archive_types',
                'guard_name'    => 'api',
                'form_id'       => '130',
                'action_id'     => '7',
                'module_id'     => '19',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'import_types',
                'guard_name'    => 'api',
                'form_id'       => '130',
                'action_id'     => '10',
                'module_id'     => '19',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_xlsx_types',
                'guard_name'    => 'api',
                'form_id'       => '130',
                'action_id'     => '11',
                'module_id'     => '19',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_csv_types',
                'guard_name'    => 'api',
                'form_id'       => '130',
                'action_id'     => '22',
                'module_id'     => '19',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_pdf_types',
                'guard_name'    => 'api',
                'form_id'       => '130',
                'action_id'     => '12',
                'module_id'     => '19',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_email_types',
                'guard_name'    => 'api',
                'form_id'       => '130',
                'action_id'     => '13',
                'module_id'     => '19',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_view_type',
                'guard_name'    => 'api',
                'form_id'       => '130',
                'action_id'     => '18',
                'module_id'     => '19',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // coaching categories
            [
                'name'          => 'show_all_categories',
                'guard_name'    => 'api',
                'form_id'       => '131',
                'action_id'     => '1',
                'module_id'     => '19',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'create_categories',
                'guard_name'    => 'api',
                'form_id'       => '131',
                'action_id'     => '2',
                'module_id'     => '19',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_single_categories',
                'guard_name'    => 'api',
                'form_id'       => '131',
                'action_id'     => '3',
                'module_id'     => '19',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_categories',
                'guard_name'    => 'api',
                'form_id'       => '131',
                'action_id'     => '4',
                'module_id'     => '19',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'delete_categories',
                'guard_name'    => 'api',
                'form_id'       => '131',
                'action_id'     => '5',
                'module_id'     => '19',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'restore_categories',
                'guard_name'    => 'api',
                'form_id'       => '131',
                'action_id'     => '6',
                'module_id'     => '19',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_all_archive_categories',
                'guard_name'    => 'api',
                'form_id'       => '131',
                'action_id'     => '7',
                'module_id'     => '19',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'import_categories',
                'guard_name'    => 'api',
                'form_id'       => '131',
                'action_id'     => '10',
                'module_id'     => '19',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_xlsx_categories',
                'guard_name'    => 'api',
                'form_id'       => '131',
                'action_id'     => '11',
                'module_id'     => '19',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_csv_categories',
                'guard_name'    => 'api',
                'form_id'       => '131',
                'action_id'     => '22',
                'module_id'     => '19',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_pdf_categories',
                'guard_name'    => 'api',
                'form_id'       => '131',
                'action_id'     => '12',
                'module_id'     => '19',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_email_categories',
                'guard_name'    => 'api',
                'form_id'       => '131',
                'action_id'     => '13',
                'module_id'     => '19',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_view_category',
                'guard_name'    => 'api',
                'form_id'       => '131',
                'action_id'     => '18',
                'module_id'     => '19',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // coaching questions
            [
                'name'          => 'show_all_questions',
                'guard_name'    => 'api',
                'form_id'       => '132',
                'action_id'     => '1',
                'module_id'     => '19',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'create_questions',
                'guard_name'    => 'api',
                'form_id'       => '132',
                'action_id'     => '2',
                'module_id'     => '19',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_single_questions',
                'guard_name'    => 'api',
                'form_id'       => '132',
                'action_id'     => '3',
                'module_id'     => '19',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_questions',
                'guard_name'    => 'api',
                'form_id'       => '132',
                'action_id'     => '4',
                'module_id'     => '19',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'delete_questions',
                'guard_name'    => 'api',
                'form_id'       => '132',
                'action_id'     => '5',
                'module_id'     => '19',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'restore_questions',
                'guard_name'    => 'api',
                'form_id'       => '132',
                'action_id'     => '6',
                'module_id'     => '19',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_all_archive_questions',
                'guard_name'    => 'api',
                'form_id'       => '132',
                'action_id'     => '7',
                'module_id'     => '19',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'import_questions',
                'guard_name'    => 'api',
                'form_id'       => '132',
                'action_id'     => '10',
                'module_id'     => '19',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_xlsx_questions',
                'guard_name'    => 'api',
                'form_id'       => '132',
                'action_id'     => '11',
                'module_id'     => '19',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_csv_questions',
                'guard_name'    => 'api',
                'form_id'       => '132',
                'action_id'     => '22',
                'module_id'     => '19',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_pdf_questions',
                'guard_name'    => 'api',
                'form_id'       => '132',
                'action_id'     => '12',
                'module_id'     => '19',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_email_questions',
                'guard_name'    => 'api',
                'form_id'       => '132',
                'action_id'     => '13',
                'module_id'     => '19',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_view_question',
                'guard_name'    => 'api',
                'form_id'       => '132',
                'action_id'     => '18',
                'module_id'     => '19',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // coaching Answers

            [
                'name'          => 'show_all_answers',
                'guard_name'    => 'api',
                'form_id'       => '133',
                'action_id'     => '1',
                'module_id'     => '19',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'create_answers',
                'guard_name'    => 'api',
                'form_id'       => '133',
                'action_id'     => '2',
                'module_id'     => '19',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_single_answers',
                'guard_name'    => 'api',
                'form_id'       => '133',
                'action_id'     => '3',
                'module_id'     => '19',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_answers',
                'guard_name'    => 'api',
                'form_id'       => '133',
                'action_id'     => '4',
                'module_id'     => '19',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'delete_answers',
                'guard_name'    => 'api',
                'form_id'       => '133',
                'action_id'     => '5',
                'module_id'     => '19',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'restore_answers',
                'guard_name'    => 'api',
                'form_id'       => '133',
                'action_id'     => '6',
                'module_id'     => '19',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_all_archive_answers',
                'guard_name'    => 'api',
                'form_id'       => '133',
                'action_id'     => '7',
                'module_id'     => '19',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'import_answers',
                'guard_name'    => 'api',
                'form_id'       => '133',
                'action_id'     => '10',
                'module_id'     => '19',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_xlsx_answers',
                'guard_name'    => 'api',
                'form_id'       => '133',
                'action_id'     => '11',
                'module_id'     => '19',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_csv_answers',
                'guard_name'    => 'api',
                'form_id'       => '133',
                'action_id'     => '22',
                'module_id'     => '19',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_pdf_answers',
                'guard_name'    => 'api',
                'form_id'       => '133',
                'action_id'     => '12',
                'module_id'     => '19',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_email_answers',
                'guard_name'    => 'api',
                'form_id'       => '133',
                'action_id'     => '13',
                'module_id'     => '19',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_view_answer',
                'guard_name'    => 'api',
                'form_id'       => '133',
                'action_id'     => '18',
                'module_id'     => '19',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // List Accounts Type 

            [
                'name'          => 'show_all_list_type_settings',
                'guard_name'    => 'api',
                'form_id'       => '134',
                'action_id'     => '1',
                'module_id'     => '19',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'create_list_type_settings',
                'guard_name'    => 'api',
                'form_id'       => '134',
                'action_id'     => '2',
                'module_id'     => '19',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_single_list_type_settings',
                'guard_name'    => 'api',
                'form_id'       => '134',
                'action_id'     => '3',
                'module_id'     => '19',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_list_type_settings',
                'guard_name'    => 'api',
                'form_id'       => '134',
                'action_id'     => '4',
                'module_id'     => '19',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'delete_list_type_settings',
                'guard_name'    => 'api',
                'form_id'       => '134',
                'action_id'     => '5',
                'module_id'     => '19',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'restore_list_type_settings',
                'guard_name'    => 'api',
                'form_id'       => '134',
                'action_id'     => '6',
                'module_id'     => '19',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_all_archive_list_type_settings',
                'guard_name'    => 'api',
                'form_id'       => '134',
                'action_id'     => '7',
                'module_id'     => '19',
                'created_at'    => now(),
                'updated_at'    => now()
            ],  [
                'name'          => 'import_list_type_settings',
                'guard_name'    => 'api',
                'form_id'       => '134',
                'action_id'     => '10',
                'module_id'     => '19',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_xlsx_list_type_settings',
                'guard_name'    => 'api',
                'form_id'       => '134',
                'action_id'     => '11',
                'module_id'     => '19',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_csv_list_type_settings',
                'guard_name'    => 'api',
                'form_id'       => '134',
                'action_id'     => '22',
                'module_id'     => '19',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_pdf_list_type_settings',
                'guard_name'    => 'api',
                'form_id'       => '134',
                'action_id'     => '12',
                'module_id'     => '19',
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_email_list_type_settings',
                'guard_name'    => 'api',
                'form_id'       => '134',
                'action_id'     => '13',
                'module_id'     => '19',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // show dashboard item - menu
            [
                'name'          => 'show_dashboard',
                'guard_name'    => 'api',
                'form_id'       => '135',
                'action_id'     => '1',
                'module_id'     => '20',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // show calendar item - menu
            [
                'name'          => 'show_calendar',
                'guard_name'    => 'api',
                'form_id'       => '135',
                'action_id'     => '1',
                'module_id'     => '20',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // show communication item - menu
            [
                'name'          => 'show_communication',
                'guard_name'    => 'api',
                'form_id'       => '135',
                'action_id'     => '1',
                'module_id'     => '20',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // show visits item - menu
            [
                'name'          => 'show_visits',
                'guard_name'    => 'api',
                'form_id'       => '135',
                'action_id'     => '1',
                'module_id'     => '20',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // show requests item - menu
            [
                'name'          => 'show_requests',
                'guard_name'    => 'api',
                'form_id'       => '135',
                'action_id'     => '1',
                'module_id'     => '20',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // show sales item - menu
            [
                'name'          => 'show_sales',
                'guard_name'    => 'api',
                'form_id'       => '135',
                'action_id'     => '1',
                'module_id'     => '20',
                'created_at'    => now(),
                'updated_at'    => now()
            ],


            // show training item - menu
            [
                'name'          => 'show_training',
                'guard_name'    => 'api',
                'form_id'       => '135',
                'action_id'     => '1',
                'module_id'     => '20',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // show tools item - menu
            [
                'name'          => 'show_tools',
                'guard_name'    => 'api',
                'form_id'       => '135',
                'action_id'     => '1',
                'module_id'     => '20',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // show settings item - menu
            [
                'name'          => 'show_settings',
                'guard_name'    => 'api',
                'form_id'       => '135',
                'action_id'     => '1',
                'module_id'     => '20',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // show help item - menu
            [
                'name'          => 'show_help',
                'guard_name'    => 'api',
                'form_id'       => '135',
                'action_id'     => '1',
                'module_id'     => '20',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // show support item - menu
            [
                'name'          => 'show_support',
                'guard_name'    => 'api',
                'form_id'       => '135',
                'action_id'     => '1',
                'module_id'     => '20',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // show reports item - menu
            [
                'name'          => 'show_reports',
                'guard_name'    => 'api',
                'form_id'       => '135',
                'action_id'     => '1',
                'module_id'     => '20',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // Sidebar Submenu items

            // show internal messaging item - communication_sub
            [
                'name'          => 'show_communication_internal_messaging',
                'guard_name'    => 'api',
                'form_id'       => '144',
                'action_id'     => '1',
                'module_id'     => '20',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // show tasks item - communication
            [
                'name'          => 'show_communication_tasks',
                'guard_name'    => 'api',
                'form_id'       => '144',
                'action_id'     => '1',
                'module_id'     => '20',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // show announcement item - communication
            [
                'name'          => 'show_communication_announcement',
                'guard_name'    => 'api',
                'form_id'       => '144',
                'action_id'     => '1',
                'module_id'     => '20',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // show communication_live item - communication
            [
                'name'          => 'show_communication_live',
                'guard_name'    => 'api',
                'form_id'       => '144',
                'action_id'     => '1',
                'module_id'     => '20',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // show plan item - visits
            [
                'name'          => 'show_visits_plan',
                'guard_name'    => 'api',
                'form_id'       => '145',
                'action_id'     => '1',
                'module_id'     => '20',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // show actual item - visits
            [
                'name'          => 'show_visits_actual',
                'guard_name'    => 'api',
                'form_id'       => '145',
                'action_id'     => '1',
                'module_id'     => '20',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // show commercial & branding item - requests
            [
                'name'          => 'show_requests_commercial_&_branding',
                'guard_name'    => 'api',
                'form_id'       => '146',
                'action_id'     => '1',
                'module_id'     => '20',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // show expenses item - requests
            [
                'name'          => 'show_requests_expenses',
                'guard_name'    => 'api',
                'form_id'       => '146',
                'action_id'     => '1',
                'module_id'     => '20',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // show material item - requests
            [
                'name'          => 'show_requests_material',
                'guard_name'    => 'api',
                'form_id'       => '146',
                'action_id'     => '1',
                'module_id'     => '20',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // show vacations item - requests
            [
                'name'          => 'show_requests_vacations',
                'guard_name'    => 'api',
                'form_id'       => '146',
                'action_id'     => '1',
                'module_id'     => '20',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // show personal requests item - requests
            [
                'name'          => 'show_requests_personal_requests',
                'guard_name'    => 'api',
                'form_id'       => '146',
                'action_id'     => '1',
                'module_id'     => '20',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // show budget setup item - requests
            [
                'name'          => 'show_requests_budget_setup',
                'guard_name'    => 'api',
                'form_id'       => '146',
                'action_id'     => '1',
                'module_id'     => '20',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // show sales importer item - sales
            [
                'name'          => 'show_sales_sales_importer',
                'guard_name'    => 'api',
                'form_id'       => '147',
                'action_id'     => '1',
                'module_id'     => '20',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // show incentive calculations item - sales
            [
                'name'          => 'show_sales_incentive_calculations',
                'guard_name'    => 'api',
                'form_id'       => '147',
                'action_id'     => '1',
                'module_id'     => '20',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // show files item - training
            [
                'name'          => 'show_training_files',
                'guard_name'    => 'api',
                'form_id'       => '148',
                'action_id'     => '1',
                'module_id'     => '20',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // show videos item - training
            [
                'name'          => 'show_training_videos',
                'guard_name'    => 'api',
                'form_id'       => '148',
                'action_id'     => '1',
                'module_id'     => '20',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // show training_live item - training
            [
                'name'          => 'show_training_live',
                'guard_name'    => 'api',
                'form_id'       => '148',
                'action_id'     => '1',
                'module_id'     => '20',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // show coaching item - training
            [
                'name'          => 'show_training_coaching',
                'guard_name'    => 'api',
                'form_id'       => '148',
                'action_id'     => '1',
                'module_id'     => '20',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // show quizzes item - training
            [
                'name'          => 'show_training_quizzes',
                'guard_name'    => 'api',
                'form_id'       => '148',
                'action_id'     => '1',
                'module_id'     => '20',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // show log activity item - tools
            [
                'name'          => 'show_tools_log_activity',
                'guard_name'    => 'api',
                'form_id'       => '149',
                'action_id'     => '1',
                'module_id'     => '20',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // show files imported item - tools
            [
                'name'          => 'show_tools_files_imported',
                'guard_name'    => 'api',
                'form_id'       => '149',
                'action_id'     => '1',
                'module_id'     => '20',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // show reports item - reports
            [
                'name'          => 'show_reports_main_reports',
                'guard_name'    => 'api',
                'form_id'       => '150',
                'action_id'     => '1',
                'module_id'     => '20',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // show customize your report item - reports
            [
                'name'          => 'show_reports_customize_your_report',
                'guard_name'    => 'api',
                'form_id'       => '150',
                'action_id'     => '1',
                'module_id'     => '20',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // setting_links

            // settings_general
            [
                'name'          => 'show_settings_general',
                'guard_name'    => 'api',
                'form_id'       => '97',
                'action_id'     => '1',
                'module_id'     => '8',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            [
                'name'          => 'show_settings_general_general',
                'guard_name'    => 'api',
                'form_id'       => '151',
                'action_id'     => '1',
                'module_id'     => '8',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            [
                'name'          => 'show_settings_general_company',
                'guard_name'    => 'api',
                'form_id'       => '151',
                'action_id'     => '1',
                'module_id'     => '8',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            [
                'name'          => 'show_settings_general_error_messages',
                'guard_name'    => 'api',
                'form_id'       => '151',
                'action_id'     => '1',
                'module_id'     => '8',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            [
                'name'          => 'show_settings_general_social',
                'guard_name'    => 'api',
                'form_id'       => '151',
                'action_id'     => '1',
                'module_id'     => '8',
                'created_at'    => now(),
                'updated_at'    => now()
            ],


            // settings_user
            [
                'name'          => 'show_settings_user',
                'guard_name'    => 'api',
                'form_id'       => '97',
                'action_id'     => '1',
                'module_id'     => '8',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            [
                'name'          => 'show_settings_user_users',
                'guard_name'    => 'api',
                'form_id'       => '152',
                'action_id'     => '1',
                'module_id'     => '8',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            [
                'name'          => 'show_settings_user_roles',
                'guard_name'    => 'api',
                'form_id'       => '152',
                'action_id'     => '1',
                'module_id'     => '8',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            [
                'name'          => 'show_settings_user_permissions',
                'guard_name'    => 'api',
                'form_id'       => '152',
                'action_id'     => '1',
                'module_id'     => '8',
                'created_at'    => now(),
                'updated_at'    => now()
            ],


            // settings_structure
            [
                'name'          => 'show_settings_structure',
                'guard_name'    => 'api',
                'form_id'       => '97',
                'action_id'     => '1',
                'module_id'     => '8',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            [
                'name'          => 'show_settings_structure_lines',
                'guard_name'    => 'api',
                'form_id'       => '153',
                'action_id'     => '1',
                'module_id'     => '8',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            [
                'name'          => 'show_settings_structure_products',
                'guard_name'    => 'api',
                'form_id'       => '153',
                'action_id'     => '1',
                'module_id'     => '8',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            [
                'name'          => 'show_settings_structure_distributors',
                'guard_name'    => 'api',
                'form_id'       => '153',
                'action_id'     => '1',
                'module_id'     => '8',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            [
                'name'          => 'show_settings_structure_accounts',
                'guard_name'    => 'api',
                'form_id'       => '153',
                'action_id'     => '1',
                'module_id'     => '8',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // settings_dashboard
            [
                'name'          => 'show_settings_dashboard',
                'guard_name'    => 'api',
                'form_id'       => '97',
                'action_id'     => '1',
                'module_id'     => '8',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            [
                'name'          => 'show_settings_dashboard_general',
                'guard_name'    => 'api',
                'form_id'       => '154',
                'action_id'     => '1',
                'module_id'     => '8',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            [
                'name'          => 'settings_dashboard_widgets',
                'guard_name'    => 'api',
                'form_id'       => '154',
                'action_id'     => '1',
                'module_id'     => '8',
                'created_at'    => now(),
                'updated_at'    => now()
            ],


            // settings_calendar
            [
                'name'          => 'show_settings_calendar',
                'guard_name'    => 'api',
                'form_id'       => '97',
                'action_id'     => '1',
                'module_id'     => '8',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            [
                'name'          => 'show_settings_calendar_general',
                'guard_name'    => 'api',
                'form_id'       => '155',
                'action_id'     => '1',
                'module_id'     => '8',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // settings_visits
            [
                'name'          => 'show_settings_visits',
                'guard_name'    => 'api',
                'form_id'       => '97',
                'action_id'     => '1',
                'module_id'     => '8',
                'created_at'    => now(),
                'updated_at'    => now()
            ],


            [
                'name'          => 'show_settings_visits_plan',
                'guard_name'    => 'api',
                'form_id'       => '156',
                'action_id'     => '1',
                'module_id'     => '8',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            [
                'name'          => 'show_settings_visits_actual',
                'guard_name'    => 'api',
                'form_id'       => '156',
                'action_id'     => '1',
                'module_id'     => '8',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            [
                'name'          => 'show_settings_visits_other',
                'guard_name'    => 'api',
                'form_id'       => '156',
                'action_id'     => '1',
                'module_id'     => '8',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // settings_requests
            [
                'name'          => 'show_settings_requests',
                'guard_name'    => 'api',
                'form_id'       => '97',
                'action_id'     => '1',
                'module_id'     => '8',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            [
                'name'          => 'show_settings_requests_vacation_types',
                'guard_name'    => 'api',
                'form_id'       => '157',
                'action_id'     => '1',
                'module_id'     => '8',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            [
                'name'          => 'show_settings_requests_public_holidays',
                'guard_name'    => 'api',
                'form_id'       => '157',
                'action_id'     => '1',
                'module_id'     => '8',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            [
                'name'          => 'show_settings_requests_office_work_types',
                'guard_name'    => 'api',
                'form_id'       => '157',
                'action_id'     => '1',
                'module_id'     => '8',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            [
                'name'          => 'show_settings_requests_personal_request_types',
                'guard_name'    => 'api',
                'form_id'       => '157',
                'action_id'     => '1',
                'module_id'     => '8',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            [
                'name'          => 'show_settings_requests_commercial_cost_types',
                'guard_name'    => 'api',
                'form_id'       => '157',
                'action_id'     => '1',
                'module_id'     => '8',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            [
                'name'          => 'show_settings_requests_request_types',
                'guard_name'    => 'api',
                'form_id'       => '157',
                'action_id'     => '1',
                'module_id'     => '8',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            [
                'name'          => 'show_settings_requests_expense_types',
                'guard_name'    => 'api',
                'form_id'       => '157',
                'action_id'     => '1',
                'module_id'     => '8',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // settings_sales
            [
                'name'          => 'show_settings_sales',
                'guard_name'    => 'api',
                'form_id'       => '97',
                'action_id'     => '1',
                'module_id'     => '8',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            [
                'name'          => 'show_settings_sales_general',
                'guard_name'    => 'api',
                'form_id'       => '158',
                'action_id'     => '1',
                'module_id'     => '8',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            [
                'name'          => 'show_settings_sales_mapping',
                'guard_name'    => 'api',
                'form_id'       => '158',
                'action_id'     => '1',
                'module_id'     => '8',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            [
                'name'          => 'show_settings_sales_sales',
                'guard_name'    => 'api',
                'form_id'       => '158',
                'action_id'     => '1',
                'module_id'     => '8',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            [
                'name'          => 'show_settings_sales_target',
                'guard_name'    => 'api',
                'form_id'       => '158',
                'action_id'     => '1',
                'module_id'     => '8',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            [
                'name'          => 'show_settings_sales_contribution',
                'guard_name'    => 'api',
                'form_id'       => '158',
                'action_id'     => '1',
                'module_id'     => '8',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            [
                'name'          => 'show_settings_sales_target_details',
                'guard_name'    => 'api',
                'form_id'       => '158',
                'action_id'     => '1',
                'module_id'     => '8',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            [
                'name'          => 'show_settings_sales_mapping_unified_codes',
                'guard_name'    => 'api',
                'form_id'       => '158',
                'action_id'     => '1',
                'module_id'     => '8',
                'created_at'    => now(),
                'updated_at'    => now()
            ],


            // settings_training
            [
                'name'          => 'show_settings_training',
                'guard_name'    => 'api',
                'form_id'       => '97',
                'action_id'     => '1',
                'module_id'     => '8',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            [
                'name'          => 'show_settings_training_general',
                'guard_name'    => 'api',
                'form_id'       => '159',
                'action_id'     => '1',
                'module_id'     => '8',
                'created_at'    => now(),
                'updated_at'    => now()
            ],


            // settings_positions
            [
                'name'          => 'show_settings_positions',
                'guard_name'    => 'api',
                'form_id'       => '97',
                'action_id'     => '1',
                'module_id'     => '8',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            [
                'name'          => 'show_settings_positions_details',
                'guard_name'    => 'api',
                'form_id'       => '160',
                'action_id'     => '1',
                'module_id'     => '8',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            [
                'name'          => 'show_settings_positions_employee',
                'guard_name'    => 'api',
                'form_id'       => '160',
                'action_id'     => '1',
                'module_id'     => '8',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // settings_coaching
            [
                'name'          => 'show_settings_coaching',
                'guard_name'    => 'api',
                'form_id'       => '97',
                'action_id'     => '1',
                'module_id'     => '8',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            [
                'name'          => 'show_settings_coaching_general',
                'guard_name'    => 'api',
                'form_id'       => '161',
                'action_id'     => '1',
                'module_id'     => '8',
                'created_at'    => now(),
                'updated_at'    => now()
            ],

        ];

        Schema::disableForeignKeyConstraints();
        $tableNames = config('permission.table_names');
        DB::table($tableNames['role_has_permissions'])->truncate();
        DB::table($tableNames['model_has_roles'])->truncate();
        DB::table($tableNames['model_has_permissions'])->truncate();
        DB::table($tableNames['roles'])->truncate();
        DB::table($tableNames['permissions'])->truncate();
        Schema::enableForeignKeyConstraints();

        $permissions_chunked = array_chunk($permissions, 100);

        foreach ($permissions_chunked as  $value) {
            Permission::insert($value);
        }


        $adminRole = Role::firstOrCreate(['name' => 'admin']);

        if (!$adminRole->hasPermissionTo('all_permissions')) {
            $adminRole->givePermissionTo('all_permissions');
        }
    }

}
