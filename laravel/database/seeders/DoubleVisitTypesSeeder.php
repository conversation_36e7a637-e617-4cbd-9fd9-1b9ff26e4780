<?php

namespace Database\Seeders;

use App\Models\DoubleVisitType;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Schema;

class DoubleVisitTypesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $types =
            [
                [
                    'name' => 'Coaching',
                    'notes' => 'Coaching',
                    'visit_type_id' => 2,
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'name' => 'Check',
                    'notes' => 'Check',
                    'visit_type_id' => 1,
                    'created_at' => now(),
                    'updated_at' => now()
                ],
            ];

        Schema::disableForeignKeyConstraints();
        DoubleVisitType::truncate();
        Schema::enableForeignKeyConstraints();

        $chunked_types = array_chunk($types, 5);


        foreach ($chunked_types as $value) {
            DoubleVisitType::insert($value);
        }
    }
}
