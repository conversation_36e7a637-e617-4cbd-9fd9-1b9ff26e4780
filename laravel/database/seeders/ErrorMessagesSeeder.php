<?php

namespace Database\Seeders;

use App\ErrorMessages;
use App\Form;
use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

use function Ramsey\Uuid\v1;

class ErrorMessagesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // $row = ErrorMessages::create([
        //     'code' => '001',
        //     'slug' => 'required_field',
        //     'message_ar' => 'required_field',
        //     'message_en' => 'required_field',
        //     'created_at' => now(),
        //     'updated_at' => now()
        // ]);

        // $row = ErrorMessages::create([
        //     'code' => '002',
        //     'slug' => 'string_field',
        //     'message_ar' => 'string_field',
        //     'message_en' => 'string_field',
        //     'created_at' => now(),
        //     'updated_at' => now()
        // ]);

        // $row = ErrorMessages::create([
        //     'code' => '003',
        //     'slug' => 'integer_field',
        //     'message_ar' => 'integer_field',
        //     'message_en' => 'integer_field',
        //     'created_at' => now(),
        //     'updated_at' => now()
        // ]);

        // $row = ErrorMessages::create([
        //     'code' => '004',
        //     'slug' => 'min_field',
        //     'message_ar' => 'min_field',
        //     'message_en' => 'min_field',
        //     'created_at' => now(),
        //     'updated_at' => now()
        // ]);

        // $row = ErrorMessages::create([
        //     'code' => '005',
        //     'slug' => 'unique_field',
        //     'message_ar' => 'unique_field',
        //     'message_en' => 'unique_field',
        //     'created_at' => now(),
        //     'updated_at' => now()
        // ]);

        // $row = ErrorMessages::create([
        //     'code' => '006',
        //     'slug' => 'array_field',
        //     'message_ar' => 'array_field',
        //     'message_en' => 'array_field',
        //     'created_at' => now(),
        //     'updated_at' => now()
        // ]);

        // $row = ErrorMessages::create([
        //     'code' => '007',
        //     'slug' => 'email_field',
        //     'message_ar' => 'email_field',
        //     'message_en' => 'email_field',
        //     'created_at' => now(),
        //     'updated_at' => now()
        // ]);

        // $row = ErrorMessages::create([
        //     'code' => '008',
        //     'slug' => 'max_field',
        //     'message_ar' => 'max_field',
        //     'message_en' => 'max_field',
        //     'created_at' => now(),
        //     'updated_at' => now()
        // ]);

        // $row = ErrorMessages::create([
        //     'code' => '009',
        //     'slug' => 'exists_field',
        //     'message_ar' => 'exists_field',
        //     'message_en' => 'exists_field',
        //     'created_at' => now(),
        //     'updated_at' => now()
        // ]);

        // $row = ErrorMessages::create([
        //     'code' => '0010',
        //     'slug' => 'after_or_equal_field',
        //     'message_ar' => 'after_or_equal_field',
        //     'message_en' => 'after_or_equal_field',
        //     'created_at' => now(),
        //     'updated_at' => now()
        // ]);


        $messages =  [
            [
                'code' => '001',
                'slug' => 'accepted',
                'message_ar' => 'must be accepted',
                'message_en' => 'must be accepted',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'code' => '002',
                'slug' => 'active_url',
                'message_ar' => 'is not a valid URL.',
                'message_en' => 'is not a valid URL.',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'code' => '003',
                'slug' => 'after',
                'message_ar' => 'must be a date after',
                'message_en' => 'must be a date after',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'code' => '004',
                'slug' => 'after_or_equal',
                'message_ar' => 'must be a date after or equal to',
                'message_en' => 'must be a date after or equal to',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'code' => '005',
                'slug' => 'alpha',
                'message_ar' => 'may only contain letters.',
                'message_en' => 'may only contain letters.',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'code' => '006',
                'slug' => 'alpha_dash',
                'message_ar' => 'may only contain letters, numbers, dashes and underscores.',
                'message_en' => 'may only contain letters, numbers, dashes and underscores.',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'code' => '007',
                'slug' => 'alpha_num',
                'message_ar' => 'may only contain letters and numbers.',
                'message_en' => 'may only contain letters and numbers.',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'code' => '008',
                'slug' => 'array',
                'message_ar' => 'must be an array.',
                'message_en' => 'must be an array.',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'code' => '009',
                'slug' => 'before',
                'message_ar' => 'must be a date before',
                'message_en' => 'must be a date before',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'code' => '010',
                'slug' => 'before_or_equal',
                'message_ar' => 'must be a date before or equal to',
                'message_en' => 'must be a date before or equal to',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'code' => '0011',
                'slug' => 'boolean',
                'message_ar' => 'field must be true or false.',
                'message_en' => 'field must be true or false.',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'code' => '012',
                'slug' => 'confirmed',
                'message_ar' => 'confirmation does not match.',
                'message_en' => 'confirmation does not match.',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'code' => '013',
                'slug' => 'date',
                'message_ar' => 'is not a valid date.',
                'message_en' => 'is not a valid date.',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'code' => '014',
                'slug' => 'date_equals',
                'message_ar' => 'must be a date equal to',
                'message_en' => 'must be a date equal to',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'code' => '015',
                'slug' => 'date_format',
                'message_ar' => 'does not match the format',
                'message_en' => 'does not match the format',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'code' => '016',
                'slug' => 'different',
                'message_ar' => 'must be different.',
                'message_en' => 'must be different.',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'code' => '017',
                'slug' => 'digits',
                'message_ar' => 'digits',
                'message_en' => 'digits',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'code' => '018',
                'slug' => 'dimensions',
                'message_ar' => 'has invalid image dimensions.',
                'message_en' => 'has invalid image dimensions.',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'code' => '019',
                'slug' => 'distinct',
                'message_ar' => 'field has a duplicate value.',
                'message_en' => 'field has a duplicate value.',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'code' => '020',
                'slug' => 'email',
                'message_ar' => 'must be a valid email address.',
                'message_en' => 'must be a valid email address.',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'code' => '021',
                'slug' => 'ends_with',
                'message_ar' => 'must end with one of the following:',
                'message_en' => 'must end with one of the following:',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'code' => '022',
                'slug' => 'exists',
                'message_ar' => 'is invalid.',
                'message_en' => 'is invalid.',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'code' => '023',
                'slug' => 'file',
                'message_ar' => 'must be a file.',
                'message_en' => 'must be a file.',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'code' => '024',
                'slug' => 'filled',
                'message_ar' => 'field must have a value.',
                'message_en' => 'field must have a value.',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'code' => '025',
                'slug' => 'image',
                'message_ar' => 'must be an image.',
                'message_en' => 'must be an image.',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'code' => '026',
                'slug' => 'in',
                'message_ar' => 'is invalid.',
                'message_en' => 'is invalid.',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'code' => '027',
                'slug' => 'in_array',
                'message_ar' => 'field does not exist in',
                'message_en' => 'field does not exist in',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'code' => '028',
                'slug' => 'integer',
                'message_ar' => 'must be an integer.',
                'message_en' => 'must be an integer.',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'code' => '029',
                'slug' => 'ip',
                'message_ar' => 'must be a valid IP address.',
                'message_en' => 'must be a valid IP address.',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'code' => '030',
                'slug' => 'present',
                'message_ar' => 'field must be present.',
                'message_en' => 'field must be present.',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'code' => '031',
                'slug' => 'ipv4',
                'message_ar' => 'must be a valid IPv4 address.',
                'message_en' => 'must be a valid IPv4 address.',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'code' => '032',
                'slug' => 'ipv6',
                'message_ar' => 'must be a valid IPv6 address.',
                'message_en' => 'must be a valid IPv6 address.',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'code' => '033',
                'slug' => 'json',
                'message_ar' => 'must be a valid JSON string.',
                'message_en' => 'must be a valid JSON string.',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'code' => '034',
                'slug' => 'not_in',
                'message_ar' => 'is invalid.',
                'message_en' => 'is invalid.',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'code' => '035',
                'slug' => 'not_regex',
                'message_ar' => 'format is invalid.',
                'message_en' => 'format is invalid.',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'code' => '036',
                'slug' => 'numeric',
                'message_ar' => 'must be a number.',
                'message_en' => 'must be a number.',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'code' => '037',
                'slug' => 'password',
                'message_ar' => 'The password is incorrect.',
                'message_en' => 'The password is incorrect.',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'code' => '038',
                'slug' => 'regex',
                'message_ar' => 'format is invalid.',
                'message_en' => 'format is invalid.',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'code' => '039',
                'slug' => 'required',
                'message_ar' => 'field is required.',
                'message_en' => 'field is required.',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'code' => '040',
                'slug' => 'string',
                'message_ar' => 'must be a string.',
                'message_en' => 'must be a string.',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'code' => '041',
                'slug' => 'timezone',
                'message_ar' => 'must be a valid zone.',
                'message_en' => 'must be a valid zone.',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'code' => '042',
                'slug' => 'unique',
                'message_ar' => 'has already been taken.',
                'message_en' => 'has already been taken.',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'code' => '043',
                'slug' => 'uploaded',
                'message_ar' => 'failed to upload.',
                'message_en' => 'failed to upload.',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'code' => '044',
                'slug' => 'url',
                'message_ar' => 'format is invalid.',
                'message_en' => 'format is invalid.',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'code' => '045',
                'slug' => 'uuid',
                'message_ar' => 'must be a valid UUID.',
                'message_en' => 'must be a valid UUID.',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'code' => '0046',
                'slug' => 'confirm_delete',
                'message_ar' => 'Do you really want to delete?',
                'message_en' => 'Do you really want to delete?',
                'created_at' => now(),
                'updated_at' => now()
            ],

            [
                'code' => '0047',
                'slug' => 'confirm_restore',
                'message_ar' => 'Do you want to restore?',
                'message_en' => 'Do you want to restore?',
                'created_at' => now(),
                'updated_at' => now()
            ],


            [
                'code' => '0048',
                'slug' => 'successfully_created',
                'message_ar' => 'Successfully created',
                'message_en' => 'Successfully created',
                'created_at' => now(),
                'updated_at' => now()
            ],


            [
                'code' => '0049',
                'slug' => 'successfully_updated',
                'message_ar' => 'Successfully updated',
                'message_en' => 'Successfully updated',
                'created_at' => now(),
                'updated_at' => now()
            ],

            [
                'code' => '0050',
                'slug' => 'successfully_deleted',
                'message_ar' => 'Successfully deleted',
                'message_en' => 'Successfully deleted',
                'created_at' => now(),
                'updated_at' => now()
            ],


            [
                'code' => '0051',
                'slug' => 'successfully_uploaded',
                'message_ar' => 'Successfully uploaded',
                'message_en' => 'Successfully uploaded',
                'created_at' => now(),
                'updated_at' => now()
            ],

            [
                'code' => '422',
                'slug' => 'failed_delete',
                'message_ar' => 'Can not be deleted. If error persists, contact <EMAIL>',
                'message_en' => 'Can not be deleted. If error persists, contact <EMAIL>',
                'created_at' => now(),
                'updated_at' => now()
            ],

            [
                'code' => '401',
                'slug' => 'failed_login',
                'message_ar' => 'Unauthorized',
                'message_en' => 'Unauthorized',
                'created_at' => now(),
                'updated_at' => now()
            ],

            [
                'code' => '0052',
                'slug' => 'failed_change_password',
                'message_ar' => 'The old password does not match.',
                'message_en' => 'The old password does not match.',
                'created_at' => now(),
                'updated_at' => now()
            ],

            [
                'code' => '0053',
                'slug' => 'file_type',
                'message_ar' => 'The file must be a file of type: xlsx,xls,pdf,jpg,png',
                'message_en' => 'The file must be a file of type: xlsx,xls,pdf,jpg,png',
                'created_at' => now(),
                'updated_at' => now()
            ],

            [
                'code' => '0054',
                'slug' => 'failed_export_template',
                'message_ar' => 'Requested file does not exist on our server!',
                'message_en' => 'Requested file does not exist on our server!',
                'created_at' => now(),
                'updated_at' => now()
            ],

            [
                'code' => '0055',
                'slug' => 'starts_with',
                'message_ar' => 'must start with one of the following:',
                'message_en' => 'must start with one of the following:',
                'created_at' => now(),
                'updated_at' => now()
            ],

            [
                'code' => '0056',
                'slug' => 'max',
                'message_ar' => 'may not be greater than',
                'message_en' => 'may not be greater than',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'code' => '0057',
                'slug' => 'min',
                'message_ar' => 'must be at least',
                'message_en' => 'must be at least',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'code' => '0058',
                'slug' => 'lt',
                'message_ar' => 'must be less than',
                'message_en' => 'must be less than',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'code' => '0059',
                'slug' => 'lte',
                'message_ar' => 'must be less than or equal',
                'message_en' => 'must be less than or equal',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'code' => '0060',
                'slug' => 'between',
                'message_ar' => 'must be between',
                'message_en' => 'must be between',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'code' => '0061',
                'slug' => 'gt',
                'message_ar' => 'must be greater than',
                'message_en' => 'must be greater than',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'code' => '0062',
                'slug' => 'gte',
                'message_ar' => 'must be greater than or equal',
                'message_en' => 'must be greater than or equal',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'code' => '0063',
                'slug' => 'same',
                'message_ar' => 'must match',
                'message_en' => 'must match',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'code' => '0064',
                'slug' => 'mimes',
                'message_ar' => 'must be a file of type:',
                'message_en' => 'must be a file of type:',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'code' => '0065',
                'slug' => 'size',
                'message_ar' => 'must be',
                'message_en' => 'must be',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'code' => '0066',
                'slug' => 'deviation',
                'message_ar' => 'Distance between your location and account location must be less than or equal to ',
                'message_en' => 'Distance between your location and account location must be less than or equal to ',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'code' => '0067',
                'slug' => 'meet_frequency',
                'message_ar' => 'Meet Frequency',
                'message_en' => 'Meet Frequency',
                'created_at' => now(),
                'updated_at' => now()
            ],

        ];
        Schema::disableForeignKeyConstraints();
        ErrorMessages::truncate();
        Schema::enableForeignKeyConstraints();

        $messages_chunked = array_chunk($messages, 10);

        foreach ($messages_chunked as  $value) {
            ErrorMessages::insert($value);
        }
    }
}
