<?php

namespace Database\Seeders;
use App\ApprovalType;
use Illuminate\Database\Seeder;

class ApprovalTypesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $row = ApprovalType::create([
            'name' => 'Position',
            'sort' => 100,
            'created_at' => now(),
            'updated_at' => now()
        ]);

        $row = ApprovalType::create([
            'name' => 'Division Type',
            'sort' => 200,
            'created_at' => now(),
            'updated_at' => now()
        ]);
    }
}
