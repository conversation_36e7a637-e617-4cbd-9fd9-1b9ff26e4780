<?php

namespace Database\Seeders;

use App\Models\ListSetting;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Schema;

class ListSettingSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $list_settings =
            [
                [
                    'name' => 'List Type',
                    'key' => 'list_type',
                    'value' => 'Doctor',
                    'type' => 'select',
                    'options' => json_encode(["values" => ['Default', 'Account', 'Doctor']]),
                ],
                [
                    'name' => 'Add Reason At Inactive List',
                    'key' => 'reason_inactive_list',
                    'value' => 'No',
                    'type' => 'select',
                    'options' => json_encode(["values" => ['Yes', 'No']]),
                ],
                [
                    'name' => 'Make Reason At Inactive List Required',
                    'key' => 'reason_inactive_list_required',
                    'value' => 'No',
                    'type' => 'select',
                    'options' => json_encode(["values" => ['Yes', 'No']]),
                ],
                [
                    'name' => 'Copy List',
                    'key' => 'copy_list',
                    'value' => 'Inactive',
                    'type' => 'select',
                    'options' => json_encode(["values" => ['Active', 'Inactive']]),
                ],
                [
                    'name' => 'Account Request Type',
                    'key' => 'account_request_type',
                    'value' => 'Line',
                    'type' => 'select',
                    'options' => json_encode(["values" => ['Line', 'Brick']]),
                ],
            ];

        Schema::disableForeignKeyConstraints();
        ListSetting::truncate();
        Schema::enableForeignKeyConstraints();

        $chunked_list_settings = array_chunk($list_settings, 5);


        foreach ($chunked_list_settings as $value) {
            ListSetting::insert($value);
        }
    }
}
