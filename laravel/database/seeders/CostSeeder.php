<?php

namespace Database\Seeders;

use App\Models\CommercialRequest\Costs\Cost;
use Illuminate\Database\Seeder;

class CostSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        Cost::create([
            'name' => 'per request',
            'sort' => '100'
        ]);
        Cost::create([
            'name' => 'per employee',
            'sort' => '200'
        ]);
        Cost::create([
            'name' => 'per doctor',
            'sort' => '300'
        ]);
    }
}
