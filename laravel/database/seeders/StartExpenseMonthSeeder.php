<?php

namespace Database\Seeders;

use App\Models\StartExpenseMonth;
use App\StartPlanDay;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Schema;

class StartExpenseMonthSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {

        $sales_settings =
            [
                [
                    'day' => '-4',
                    'name' => 'Before 4 Months',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'day' => '-3',
                    'name' => 'Before 3 Months',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'day' => '-2',
                    'name' => 'Before 2 Months',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'day' => '-1',
                    'name' => 'Before 1 Month',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'day' => '0',
                    'name' => 'Now',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'day' => '1',
                    'name' => 'After 1 Month',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'day' => '2',
                    'name' => 'After 2 Months',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'day' => '3',
                    'name' => 'After 3 Months',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'day' => '4',
                    'name' => 'After 4 Months',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'day' => '5',
                    'name' => 'After 5 Months',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'day' => '6',
                    'name' => 'After 6 Months',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
            ];

        Schema::disableForeignKeyConstraints();
        StartExpenseMonth::truncate();
        Schema::enableForeignKeyConstraints();

        $chunked_sales_settings = array_chunk($sales_settings, 5);


        foreach ($chunked_sales_settings as $value) {
            StartExpenseMonth::insert($value);
        }
    }
}
