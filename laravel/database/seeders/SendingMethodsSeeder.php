<?php

namespace Database\Seeders;

use App\Models\AlertBy;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class SendingMethodsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $sending_methods = [
            [
                'id' => 1,
                'sending_method' => 'Email',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id' => 2,
                'sending_method' => 'Widget',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id' => 3,
                'sending_method' => 'Internal Messaging',
                'created_at' => now(),
                'updated_at' => now()
            ]
        ];


        $chunked_sending_methods = array_chunk($sending_methods, 10);


        foreach ($chunked_sending_methods as $value) {
            AlertBy::insert($value);
        }
    }
}
