<?php

namespace Database\Seeders;
use App\StartPlanDay;
use Illuminate\Database\Seeder;

class StartPlanDaysSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $row = StartPlanDay::create([
            'day' => '-7',
            'name' => 'before 1 week',
            'created_at' => now(),
            'updated_at' => now()
        ]);

        $row = StartPlanDay::create([
            'day' => '-6',
            'name' => 'before 6 days',
            'created_at' => now(),
            'updated_at' => now()
        ]);

        $row = StartPlanDay::create([
            'day' => '-5',
            'name' => 'before 5 days',
            'created_at' => now(),
            'updated_at' => now()
        ]);

        $row = StartPlanDay::create([
            'day' => '-4',
            'name' => 'before 4 days',
            'created_at' => now(),
            'updated_at' => now()
        ]);

        $row = StartPlanDay::create([
            'day' => '-3',
            'name' => 'before 3 days',
            'created_at' => now(),
            'updated_at' => now()
        ]);

        $row = StartPlanDay::create([
            'day' => '-2',
            'name' => 'before 2 days',
            'created_at' => now(),
            'updated_at' => now()
        ]);

        $row = StartPlanDay::create([
            'day' => '-1',
            'name' => 'yesterday',
            'created_at' => now(),
            'updated_at' => now()
        ]);

        $row = StartPlanDay::create([
            'day' => '0',
            'name' => 'today',
            'created_at' => now(),
            'updated_at' => now()
        ]);

        $row = StartPlanDay::create([
            'day' => '1',
            'name' => 'tomorrow',
            'created_at' => now(),
            'updated_at' => now()
        ]);

        $row = StartPlanDay::create([
            'day' => '2',
            'name' => 'after 2 days',
            'created_at' => now(),
            'updated_at' => now()
        ]);

        $row = StartPlanDay::create([
            'day' => '3',
            'name' => 'after 3 days',
            'created_at' => now(),
            'updated_at' => now()
        ]);

        $row = StartPlanDay::create([
            'day' => '4',
            'name' => 'after 4 days',
            'created_at' => now(),
            'updated_at' => now()
        ]);

        $row = StartPlanDay::create([
            'day' => '5',
            'name' => 'after 5 days',
            'created_at' => now(),
            'updated_at' => now()
        ]);

        $row = StartPlanDay::create([
            'day' => '6',
            'name' => 'after 6 days',
            'created_at' => now(),
            'updated_at' => now()
        ]);

        $row = StartPlanDay::create([
            'day' => '7',
            'name' => 'after 1 week',
            'created_at' => now(),
            'updated_at' => now()
        ]);

    }
}
