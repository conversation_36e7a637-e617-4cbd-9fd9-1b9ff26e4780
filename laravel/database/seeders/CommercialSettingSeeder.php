<?php

namespace Database\Seeders;

use App\Models\CommercialRequest\CommercialSetting;
use App\Models\ExpenseSetting;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Schema;

class CommercialSettingSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {

        $commercial_settings =
            [
                [
                    'name' => 'Due Date Time',
                    'key' => 'due_date_time',
                    'value' => 'After 2 Months',
                    'type' => 'select',
                    'options' => json_encode(["values" => ['Now', 'After 1 Month', 'After 2 Months']]),
                ],

                [
                    'name' => 'Auto Approved if there is no approval settings',
                    'key' => 'auto_approved',
                    'value' => 'No',
                    'type' => 'select',
                    'options' => json_encode(["values" => ['Yes', 'No']]),
                ],
            ];

        Schema::disableForeignKeyConstraints();
        CommercialSetting::truncate();
        Schema::enableForeignKeyConstraints();

        $chunked_commercial_settings = array_chunk($commercial_settings, 5);


        foreach ($chunked_commercial_settings as $value) {
            CommercialSetting::insert($value);
        }
    }
}
