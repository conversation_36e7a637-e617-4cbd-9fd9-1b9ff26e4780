<?php

namespace Database\Seeders;

use App\VacationSetting;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Schema;

class VacationSettingSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $vacation_settings =
            [
                [
                    'name' => 'Edit/Delete Vacation',
                    'key' => 'delete_edit_vacation',
                    'value' => 'Before Approval',
                    'type' => 'select',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'name' => 'Vacation Time',
                    'key' => 'vacation_time',
                    'value' => 'Now',
                    'type' => 'select',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'name' => 'Vacation Approval',
                    'key' => 'vacation_approval',
                    'value' => 'Now',
                    'type' => 'select',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'name' => 'Change Vacation Status to Disapproved if disapproved from any Employee',
                    'key' => 'vacation_disapprove_automatic',
                    'value' => 'No',
                    'type' => 'select',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
            ];

        Schema::disableForeignKeyConstraints();
        VacationSetting::truncate();
        Schema::enableForeignKeyConstraints();

        $chunked_plan_settings = array_chunk($vacation_settings, 5);


        foreach ($chunked_plan_settings as $value) {
            VacationSetting::insert($value);
        }
    }
}
