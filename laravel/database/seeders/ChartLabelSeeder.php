<?php

namespace Database\Seeders;
use App\ChartLabel;
use Illuminate\Database\Seeder;

class ChartLabelSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $row = ChartLabel::create([
            'name' => 'Coverage',
            'chart_id' => 1,
            'created_at' => now(),
            'updated_at' => now()
        ]);
        $row = ChartLabel::create([
            'name' => 'Doctors',
            'chart_id' => 1,
            'created_at' => now(),
            'updated_at' => now()
        ]);

        $row = ChartLabel::create([
            'name' => 'Coverage',
            'chart_id' => 2,
            'created_at' => now(),
            'updated_at' => now()
        ]);

        $row = ChartLabel::create([
            'name' => 'Accounts',
            'chart_id' => 2,
            'created_at' => now(),
            'updated_at' => now()
        ]);

        $row = ChartLabel::create([
            'name' => 'Coverage',
            'chart_id' => 3,
            'created_at' => now(),
            'updated_at' => now()
        ]);

        $row = ChartLabel::create([
            'name' => 'Doctors',
            'chart_id' => 3,
            'created_at' => now(),
            'updated_at' => now()
        ]);

        $row = ChartLabel::create([
            'name' => 'Coverage',
            'chart_id' => 4,
            'created_at' => now(),
            'updated_at' => now()
        ]);

        $row = ChartLabel::create([
            'name' => 'Accounts',
            'chart_id' => 4,
            'created_at' => now(),
            'updated_at' => now()
        ]);

        $row = ChartLabel::create([
            'name' => 'Coverage',
            'chart_id' => 5,
            'created_at' => now(),
            'updated_at' => now()
        ]);

        $row = ChartLabel::create([
            'name' => 'Doctors',
            'chart_id' => 5,
            'created_at' => now(),
            'updated_at' => now()
        ]);

        $row = ChartLabel::create([
            'name' => 'Coverage',
            'chart_id' => 6,
            'created_at' => now(),
            'updated_at' => now()
        ]);

        $row = ChartLabel::create([
            'name' => 'Accounts',
            'chart_id' => 6,
            'created_at' => now(),
            'updated_at' => now()
        ]);

    }
}
