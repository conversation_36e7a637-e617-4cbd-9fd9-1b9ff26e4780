<?php

namespace Database\Seeders;
use App\Line;
use App\Position;
use App\UserPosition;
use Illuminate\Database\Seeder;

class PositionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        //Positions
        Position::insert([
            [
                'id'=>1,
                'name' => 'Product Manager',
                'sort' => 100,
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id'=>2,
                'name' => 'Senior Product Manager',
                'sort' => 200,
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id'=>3,
                'name' => 'Trainer Manager',
                'sort' => 300,
                'created_at' => now(),
                'updated_at' => now()
            ]
        ]);

        // Create Emp for positions
        Position::find(1)->users()->attach(12,['from_date'=>now()]);
        Position::find(2)->users()->attach(13,['from_date'=>now()]);

        //Create Lines For Positions
        $line=Line::find(1);
        foreach (UserPosition::get() as  $userPosition) {
            // attach line 1 foreach userPosition
            $userPosition->lines()->attach($line->first()->id);
            // attach divisions of line1  foreach userPosition
            $userPosition->divisions()->syncWithoutDetaching($line->divisions()->get('id')->random(3)->pluck('id')->toArray());
            // attach products of line 1 foreach userPosition
            $userPosition->products()->syncWithoutDetaching($line->products()->get()->random(2)->pluck('id')->toArray());
        }
    }
}
