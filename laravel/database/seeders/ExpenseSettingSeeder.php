<?php

namespace Database\Seeders;

use App\Models\ExpenseSetting;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Schema;

class ExpenseSettingSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {

        $expense_settings =
            [
                [
                    'name' => 'Expense Time',
                    'key' => 'expense_time',
                    'value' => null,
                    'type' => 'select',
                    'options' => json_encode(["values" => ['Before 4 Months', 'Before 3 Months', 'Before 2 Months', 'Before 1 Month', 'Now', 'After 1 Month', 'After 2 Months']]),
                ],
                [
                    'name' => 'Expense Header Time',
                    'key' => 'expense_header_time',
                    'value' => null,
                    'type' => 'select',
                    'options' => json_encode(["values" => ['Before 4 Months', 'Before 3 Months', 'Before 2 Months', 'Before 1 Month', 'Now', 'After 1 Month', 'After 2 Months']]),
                ],
                [
                    'name' => 'Day Of Close Expense Approvals',
                    'key' => 'day_of_close_expense_approvals',
                    'value' => '00',
                    'type' => 'number',
                    'options' => null,
                ],
                [
                    'name' => 'Use Automatic Expense',
                    'key' => 'use_automatic_expense',
                    'value' => 'No',
                    'type' => 'select',
                    'options' => json_encode(["values" => ['Yes', 'No']]),
                ],
                [
                    'name' => 'Change Expense Status to Disapproved if disapproved from any Employee',
                    'key' => 'expense_disapprove_automatic',
                    'value' => 'No',
                    'type' => 'select',
                    'options' => json_encode(["values" => ['Yes', 'No']]),
                ],
                [
                    'name' => 'Choose your Expense Type per Location',
                    'key' => 'choose_expense_type',
                    'value' => 'Single',
                    'type' => 'select',
                    'options' => json_encode(["values" => ['Single', 'Double']]),
                ],
                [
                    'name' => 'Auto Approved if there is no approval settings',
                    'key' => 'auto_approved',
                    'value' => 'No',
                    'type' => 'select',
                    'options' => json_encode(["values" => ['Yes', 'No']]),
                ],
            ];

        Schema::disableForeignKeyConstraints();
        ExpenseSetting::truncate();
        Schema::enableForeignKeyConstraints();

        $chunked_expense_settings = array_chunk($expense_settings, 5);


        foreach ($chunked_expense_settings as $value) {
            ExpenseSetting::insert($value);
        }
    }
}
