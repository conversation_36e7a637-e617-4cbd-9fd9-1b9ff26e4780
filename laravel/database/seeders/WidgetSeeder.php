<?php

namespace Database\Seeders;

use App\ActualVisit;
use App\Models\Widgets\MedicalRepVisitPerClass;
use App\Models\Widgets\MedicalRepVisitPerSpeciality;
use App\Models\Widgets\ProductCallPerClass;
use App\Models\Widgets\ProductCallPerDetailing;
use App\Models\Widgets\ProductCallPerSpeciality;
use App\Models\Widgets\SalesDistributorCoverageValue;
use App\Models\Widgets\SalesDistributorCoverageUnit;
use App\Models\Widgets\MedicalRepNonWorkingUsers;
use App\Models\Widgets\SalesProductCoverageValue;
use App\Models\Widgets\SalesTargetCoverageValue;
use App\Models\Widgets\MedicalRepVisitPerMember;
use App\Models\Widgets\SalesProductCoverageUnit;
use App\Models\Widgets\MedicalRepTodayVacation;
use App\Models\Widgets\SalesTargetCoverageUnit;
use App\Models\Widgets\AlertBelowFrequencies;
use App\Models\Widgets\AccountTypesCoverage;
use App\Models\Widgets\AlertWithoutAMVisits;
use App\Models\Widgets\AlertWithoutPMVisits;
use App\Models\Widgets\PmAccountCoverage;
use App\Models\Widgets\AmAccountCoverage;
use App\Models\Widgets\AmDoctorCoverage;
use App\Models\Widgets\PmDoctorCoverage;
use App\Models\Widgets\AccountCoverage;
use App\Models\Widgets\SalesYearToDate;
use App\Models\Widgets\CoachingHeader;
use App\Models\Widgets\MarketFeedback;
use App\Models\Widgets\DoctorCoverage;
use App\Models\Widgets\Announcements;
use App\Models\Widgets\Leaderboard;
use App\Models\Widgets\Approval;
use App\Models\Widgets\Coaching;
use App\Models\Widgets\DoubleVisit;
use App\Models\Widgets\FollowUp;
use App\Models\Widgets\ProductFeedback;
use App\Models\Widgets\Vacation;
use Illuminate\Database\Seeder;
use App\Models\Widgets\Visits;
use App\Models\Widgets\Tasks;
use App\Models\Widgets\Quiz;
use App\Models\Widgets\Sample;
use App\Models\WidgetType;
use App\Widget;

class WidgetSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {

        $types = WidgetType::get();
        $widgets = [
            [
                'name' => 'List Coverage Doctor Level',
                'cols' => 6,
                'sort' => 100,
                'type_id' => $types->where('name', 'donut')->first()->id,
                'widgetable_type' => DoctorCoverage::class,
                'widget_module_id' => 1,
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'name' => 'List Coverage Account Level',
                'cols' => 6,
                'sort' => 200,
                'type_id' => $types->where('name', 'pie')->first()->id,
                'widgetable_type' => AccountCoverage::class,
                'widget_module_id' => 1,
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'name' => 'AM List Coverage Doctor Level',
                'cols' => 6,
                'sort' => 300,
                'type_id' => $types->where('name', 'donut')->first()->id,
                'widgetable_type' => AmDoctorCoverage::class,
                'widget_module_id' => 1,
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'name' => 'AM List Coverage Account Level',
                'cols' => 6,
                'sort' => 400,
                'type_id' => $types->where('name', 'donut')->first()->id,
                'widgetable_type' => AmAccountCoverage::class,
                'widget_module_id' => 1,
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'name' => 'PM List Coverage Doctor Level',
                'cols' => 6,
                'sort' => 500,
                'type_id' => $types->where('name', 'pie')->first()->id,
                'widgetable_type' => PmDoctorCoverage::class,
                'widget_module_id' => 1,
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'name' => 'PM List Coverage Account Level',
                'cols' => 6,
                'sort' => 600,
                'type_id' => $types->where('name', 'pie')->first()->id,
                'widgetable_type' => PmAccountCoverage::class,
                'widget_module_id' => 1,
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'name' => 'List Account Types Coverage',
                'cols' => 12,
                'sort' => 700,
                'type_id' => $types->where('name', 'bar')->first()->id,
                'widgetable_type' => AccountTypesCoverage::class,
                'widget_module_id' => 1,
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'name' => 'Plan Visit Widget',
                'cols' => 12,
                'sort' => 170,
                'type_id' => $types->where('name', 'tabs')->first()->id,
                'widgetable_type' => Visits::class,
                'widget_module_id' => 1,
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'name' => 'Sample Widget',
                'cols' => 12,
                'sort' => 150,
                'type_id' => $types->where('name', 'table')->first()->id,
                'widgetable_type' => Sample::class,
                'widget_module_id' => 1,
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'name' => 'Sales Product in Units',
                'cols' => 6,
                'sort' => 900,
                'type_id' => $types->where('name', 'pie')->first()->id,
                'widgetable_type' => SalesProductCoverageUnit::class,
                'widget_module_id' => 2,
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'name' => 'Sales Product in Values',
                'cols' => 6,
                'sort' => 1000,
                'type_id' => $types->where('name', 'pie')->first()->id,
                'widgetable_type' => SalesProductCoverageValue::class,
                'widget_module_id' => 2,
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'name' => 'Sales Distributor in Units',
                'cols' => 6,
                'sort' => 1100,
                'type_id' => $types->where('name', 'pie')->first()->id,
                'widgetable_type' => SalesDistributorCoverageUnit::class,
                'widget_module_id' => 2,
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'name' => 'Sales Distributor in Values',
                'cols' => 6,
                'sort' => 1200,
                'type_id' => $types->where('name', 'pie')->first()->id,
                'widgetable_type' => SalesDistributorCoverageValue::class,
                'widget_module_id' => 2,
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'name' => 'Sales YTD',
                'cols' => 6,
                'sort' => 1300,
                'type_id' => $types->where('name', 'pie')->first()->id,
                'widgetable_type' => SalesYearToDate::class,
                'widget_module_id' => 2,
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'name' => 'Sales Target in Units',
                'cols' => 6,
                'sort' => 1400,
                'type_id' => $types->where('name', 'pie')->first()->id,
                'widgetable_type' => SalesTargetCoverageUnit::class,
                'widget_module_id' => 2,
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'name' => 'Sales Target in Values',
                'cols' => 6,
                'sort' => 1500,
                'type_id' => $types->where('name', 'pie')->first()->id,
                'widgetable_type' => SalesTargetCoverageValue::class,
                'widget_module_id' => 2,
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'name' => 'Tasks',
                'cols' => 12,
                'sort' => 1600,
                'type_id' => $types->where('name', 'table')->first()->id,
                'widgetable_type' => Tasks::class,
                'widget_module_id' => 3,
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'name' => 'Announcements',
                'cols' => 12,
                'sort' => 1700,
                'type_id' => $types->where('name', 'table')->first()->id,
                'widgetable_type' => Announcements::class,
                'widget_module_id' => 3,
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'name' => 'Follow Up Widget',
                'cols' => 12,
                'sort' => 1800,
                'type_id' => $types->where('name', 'table')->first()->id,
                'widgetable_type' => FollowUp::class,
                'widget_module_id' => 1,
                'created_at' => now(),
                'updated_at' => now()
            ],

            [
                'name' => 'Approval Widget',
                'cols' => 12,
                'sort' => 1900,
                'type_id' => $types->where('name', 'tabs')->first()->id,
                'widgetable_type' => Approval::class,
                'widget_module_id' => 1,
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'name' => 'Coaching Header Widget',
                'cols' => 12,
                'sort' => 2000,
                'type_id' => $types->where('name', 'table')->first()->id,
                'widgetable_type' => CoachingHeader::class,
                'widget_module_id' => 4,
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'name' => 'Quiz Widget',
                'cols' => 12,
                'sort' => 2100,
                'type_id' => $types->where('name', 'table')->first()->id,
                'widgetable_type' => Quiz::class,
                'widget_module_id' => 4,
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'name' => 'Double Visit Widget',
                'cols' => 12,
                'sort' => 3700,
                'type_id' => $types->where('name', 'table')->first()->id,
                'widgetable_type' => DoubleVisit::class,
                'widget_module_id' => 1,
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'name' => 'Vacation Widget',
                'cols' => 12,
                'sort' => 2200,
                'type_id' => $types->where('name', 'table')->first()->id,
                'widgetable_type' => Vacation::class,
                'widget_module_id' => 5,
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'name' => 'Market Feedback Widget',
                'cols' => 12,
                'sort' => 2300,
                'type_id' => $types->where('name', 'table')->first()->id,
                'widgetable_type' => MarketFeedback::class,
                'widget_module_id' => 1,
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'name' => 'Leaderboards Widget',
                'cols' => 12,
                'sort' => 1,
                'type_id' => $types->where('name', 'image')->first()->id,
                'widgetable_type' => Leaderboard::class,
                'widget_module_id' => 3,
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'name' => 'Alert Without AM Visits Widget',
                'cols' => 12,
                'sort' => 2500,
                'type_id' => $types->where('name', 'list')->first()->id,
                'widgetable_type' => AlertWithoutAMVisits::class,
                'widget_module_id' => 3,
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'name' => 'Alert Without PM Visits Widget',
                'cols' => 12,
                'sort' => 2600,
                'type_id' => $types->where('name', 'list')->first()->id,
                'widgetable_type' => AlertWithoutPMVisits::class,
                'widget_module_id' => 3,
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'name' => 'Alert Below Frequencies Widget',
                'cols' => 12,
                'sort' => 2700,
                'type_id' => $types->where('name', 'list')->first()->id,
                'widgetable_type' => AlertBelowFrequencies::class,
                'widget_module_id' => 3,
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'name' => 'M.R Visit Per Member',
                'cols' => 12,
                'sort' => 2800,
                'type_id' => $types->where('name', 'stack_bar')->first()->id,
                'widgetable_type' => MedicalRepVisitPerMember::class,
                'widget_module_id' => 1,
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'name' => 'M.R Today Vacations',
                'cols' => 12,
                'sort' => 2900,
                'type_id' => $types->where('name', 'view_list')->first()->id,
                'widgetable_type' => MedicalRepTodayVacation::class,
                'widget_module_id' => 5,
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'name' => 'M.R Non Working Users',
                'cols' => 12,
                'sort' => 3000,
                'type_id' => $types->where('name', 'view_list')->first()->id,
                'widgetable_type' => MedicalRepNonWorkingUsers::class,
                'widget_module_id' => 1,
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'name' => 'M.R Visit Per Speciality',
                'cols' => 12,
                'sort' => 3100,
                'type_id' => $types->where('name', 'stack_bar')->first()->id,
                'widgetable_type' => MedicalRepVisitPerSpeciality::class,
                'widget_module_id' => 1,
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'name' => 'M.R Visit Per Class',
                'cols' => 12,
                'sort' => 3200,
                'type_id' => $types->where('name', 'stack_bar')->first()->id,
                'widgetable_type' => MedicalRepVisitPerClass::class,
                'widget_module_id' => 1,
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'name' => 'Product Call Per Speciality',
                'cols' => 12,
                'sort' => 3300,
                'type_id' => $types->where('name', 'stack_bar')->first()->id,
                'widgetable_type' => ProductCallPerSpeciality::class,
                'widget_module_id' => 1,
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'name' => 'Product Call Per Class',
                'cols' => 12,
                'sort' => 3400,
                'type_id' => $types->where('name', 'stack_bar')->first()->id,
                'widgetable_type' => ProductCallPerClass::class,
                'widget_module_id' => 1,
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'name' => 'Product Call Per Detailing',
                'cols' => 12,
                'sort' => 3500,
                'type_id' => $types->where('name', 'stack_bar')->first()->id,
                'widgetable_type' => ProductCallPerDetailing::class,
                'widget_module_id' => 1,
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'name' => 'Product Feedback',
                'cols' => 12,
                'sort' => 3600,
                'type_id' => $types->where('name', 'stack_bar')->first()->id,
                'widgetable_type' => ProductFeedback::class,
                'widget_module_id' => 1,
                'created_at' => now(),
                'updated_at' => now()
            ],
        ];

        $chunked_widgets = array_chunk($widgets, 10);

        foreach ($chunked_widgets as $value) {
            Widget::insert($value);
        }
    }
}
