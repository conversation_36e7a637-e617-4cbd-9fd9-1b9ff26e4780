<?php

namespace Database\Seeders;

use App\VisitType;
use Illuminate\Database\Seeder;

class VisitTypeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $row = VisitType::create([
            'name' => 'Single',
            'created_at' => now(),
            'updated_at' => now()
        ]);

        $row = VisitType::create([
            'name' => 'Double',
            'created_at' => now(),
            'updated_at' => now()
        ]);
    }
}
