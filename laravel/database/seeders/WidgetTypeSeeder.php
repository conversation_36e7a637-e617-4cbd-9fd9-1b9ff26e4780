<?php

namespace Database\Seeders;

use App\Models\WidgetType;
use App\Widget;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Schema;

class WidgetTypeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        Schema::disableForeignKeyConstraints();
        WidgetType::truncate();
        Schema::enableForeignKeyConstraints();
        $types = [
            ['name'=>'donut'],
            ['name'=>'pie'],
            ['name'=>'bar'],
            ['name'=>'tabs'],
            ['name'=>'table'],
            ['name'=>'image'],
            ['name'=>'list'],
            ['name'=>'stack_bar'],
            ['name'=>'view_list'],
        ];

        $chunked_widgets = array_chunk($types, 10);


        foreach ($chunked_widgets as $value) {
            WidgetType::insert($value);
        }
    }
}
