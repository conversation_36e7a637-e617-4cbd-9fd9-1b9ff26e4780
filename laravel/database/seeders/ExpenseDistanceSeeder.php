<?php

namespace Database\Seeders;

use App\Models\Expenses\Distance;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Schema;

class ExpenseDistanceSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $expense_settings =
            [
                [
                    'name' => 'Single',
                ],
                [
                    'name' => 'Double'
                ],
            ];

        Schema::disableForeignKeyConstraints();
        Distance::truncate();
        Schema::enableForeignKeyConstraints();

        $chunked_expense_settings = array_chunk($expense_settings, 5);


        foreach ($chunked_expense_settings as $value) {
            Distance::insert($value);
        }
    }
}
