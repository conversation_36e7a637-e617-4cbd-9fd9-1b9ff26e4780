<?php

namespace Database\Seeders;
use App\SupportTypes;
use Illuminate\Database\Seeder;

class SupportTypesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $row = SupportTypes::create([
            'name' => 'Server Outage',
            'created_at' => now(),
            'updated_at' => now()
        ]);
        $row = SupportTypes::create([
            'name' => 'Passwords',
            'created_at' => now(),
            'updated_at' => now()
        ]);
        $row = SupportTypes::create([
            'name' => 'Technical issue',
            'created_at' => now(),
            'updated_at' => now()
        ]);
        $row = SupportTypes::create([
            'name' => 'Other',
            'created_at' => now(),
            'updated_at' => now()
        ]);
    }
}
