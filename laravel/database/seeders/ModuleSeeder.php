<?php

namespace Database\Seeders;
use App\Module;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Schema;

class ModuleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {

        $modules = [
            [
                'id' => '1',
                'module' => 'users',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id' => '2',
                'module' => 'products',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id' => '3',
                'module' => 'lines',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id' => '4',
                'module' => 'accounts',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id' => '5',
                'module' => 'plan_visits',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id' => '6',
                'module' => 'actual_visits',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id' => '7',
                'module' => 'office_works',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id' => '8',
                'module' => 'settings',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id' => '9',
                'module' => 'requests',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id' => '10',
                'module' => 'dashboard_widgets',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id' => '11',
                'module' => 'sales',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id' => '12',
                'module' => 'positions',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id' => '13',
                'module' => 'reports',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id' => '14',
                'module' => 'profiles',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id' => '15',
                'module' => 'Others',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id' => '16',
                'module' => 'help',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id' => '17',
                'module' => 'communication',
                'created_at' => now(),
                'updated_at' => now()
            ],

            [
                'id' => '18',
                'module' => 'frequencies',
                'created_at' => now(),
                'updated_at' => now()
            ],

            [
                'id' => '19',
                'module' => 'coaching',
                'created_at' => now(),
                'updated_at' => now()
            ],

            [
                'id' => '20',
                'module' => 'menu',
                'created_at' => now(),
                'updated_at' => now()
            ],

        ];

        Schema::disableForeignKeyConstraints();
        Module::truncate();
        Schema::enableForeignKeyConstraints();

        $module_chunked = array_chunk($modules, 5);

        foreach ($module_chunked as  $value) {
            Module::insert($value);
        }
    }
}
