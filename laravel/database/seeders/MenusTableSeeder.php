<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use Illuminate\Support\Facades\Schema;

class MenusTableSeeder extends Seeder
{

    private $menuId = null;
    private $dropdownId = array();
    private $dropdown = false;
    private $sequence = 1;
    private $joinData = array();
    private $translationData = array();
    private $defaultTranslation = 'en';
    private $adminRole = null;
    private $userRole = null;


    public function join($roles, $menusId)
    {
        $roles = explode(',', $roles);
        foreach ($roles as $role) {
            array_push($this->joinData, array('role_name' => $role, 'menus_id' => $menusId));
        }
    }

    public function addTranslation($lang, $name, $menuId)
    {
        $this->translationData[] = [
            'name' => $name,
            'lang' => $lang,
            'menus_id' => $menuId
        ];
    }

    /*
        Function insert All translations
        Must by use on end of this seeder
    */
    public function insertAllTranslations()
    {
        DB::beginTransaction();
        foreach ($this->translationData as $data) {
            DB::table('menus_lang')->insert([
                'name' => $data['name'],
                'lang' => $data['lang'],
                'menus_id' => $data['menus_id']
            ]);
        }
        DB::commit();
    }

    public function insertLink($roles = null, $name, $href, $icon = '')
    {
        if ($roles == null) {
            $data = Role::all();
            $roles = '';
            foreach ($data as $key => $role) {
                $roles .= $role->name . ',';
            }
            $roles = substr($roles, 0, -1);
        }
        if ($this->dropdown === false) {
            DB::table('menus')->insert([
                'slug' => 'link',
                'icon' => $icon,
                'href' => $href,
                'menu_id' => $this->menuId,
                'sequence' => $this->sequence
            ]);
        } else {
            DB::table('menus')->insert([
                'slug' => 'link',
                'icon' => $icon,
                'href' => $href,
                'menu_id' => $this->menuId,
                'parent_id' => $this->dropdownId[count($this->dropdownId) - 1],
                'sequence' => $this->sequence
            ]);
        }
        $this->sequence++;
        $lastId = DB::getPdo()->lastInsertId();
        $this->join($roles, $lastId);
        $this->addTranslation($this->defaultTranslation, $name, $lastId);

        if ($this->defaultTranslation == 'en') {
            $permission = Permission::where('name', '=', $name)->get();
            if (empty($permission)) {
                $permission = Permission::create(['name' => 'visit ' . $name]);
            }
            $roles = explode(',', $roles);
            if (in_array('user', $roles)) {
                $this->userRole->givePermissionTo($permission);
            }
            if (in_array('admin', $roles)) {
                $this->adminRole->givePermissionTo($permission);
            }
        }

        return $lastId;
    }

    public function insertTitle($roles = null, $name)
    {
        if ($roles == null) {
            $data = Role::all();
            $roles = '';
            foreach ($data as $key => $role) {
                $roles .= $role->name . ',';
            }
            $roles = substr($roles, 0, -1);
        }
        DB::table('menus')->insert([
            'slug' => 'title',
            'menu_id' => $this->menuId,
            'sequence' => $this->sequence
        ]);
        $this->sequence++;
        $lastId = DB::getPdo()->lastInsertId();
        $this->join($roles, $lastId);
        $this->addTranslation($this->defaultTranslation, $name, $lastId);
        return $lastId;
    }

    public function beginDropdown($roles = null, $name, $href = '', $icon = '')
    {
        if ($roles == null) {
            $data = Role::all();
            $roles = '';
            foreach ($data as $key => $role) {
                $roles .= $role->name . ',';
            }
            $roles = substr($roles, 0, -1);
        }
        if (count($this->dropdownId)) {
            $parentId = $this->dropdownId[count($this->dropdownId) - 1];
        } else {
            $parentId = null;
        }
        DB::table('menus')->insert([
            'slug' => 'dropdown',
            'icon' => $icon,
            'menu_id' => $this->menuId,
            'sequence' => $this->sequence,
            'parent_id' => $parentId,
            'href' => $href,
        ]);
        $lastId = DB::getPdo()->lastInsertId();
        array_push($this->dropdownId, $lastId);
        $this->dropdown = true;
        $this->sequence++;
        $this->join($roles, $lastId);
        $this->addTranslation($this->defaultTranslation, $name, $lastId);
        return $lastId;
    }

    public function endDropdown()
    {
        $this->dropdown = false;
        array_pop($this->dropdownId);
    }

    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        Schema::disableForeignKeyConstraints();
        DB::table('menulist')->truncate();
        DB::table('menus')->truncate();
        DB::table('menus_lang')->truncate();
        DB::table('menu_lang_lists')->truncate();
        Schema::enableForeignKeyConstraints();

        /* Get roles */
        $this->adminRole = Role::where('name', '=', 'admin')->first();
        if (empty($this->adminRole)) {
            $this->adminRole = Role::create(['name' => 'admin']);
        }
        $this->userRole = Role::where('name', '=', 'user')->first();
        if (empty($this->userRole)) {
            $this->userRole = Role::create(['name' => 'user']);
        }
        /* Create Translation languages */
        DB::table('menu_lang_lists')->insert([
            'name' => 'English',
            'short_name' => 'en',
            'is_default' => true
        ]);
        // DB::table('menu_lang_lists')->insert([
        //     'name' => 'Polish',
        //     'short_name' => 'pl'
        // ]);
        DB::table('menu_lang_lists')->insert([
            'name' => 'Arabic',
            'short_name' => 'ar'
        ]);

        /* Create Sidebar menu */
        DB::table('menulist')->insert([
            'name' => 'sidebar menu'
        ]);
        $this->menuId = DB::getPdo()->lastInsertId();  //set menuId

        //Dashboard Menu
        $this->beginDropdown(null, 'Dashboards', '/', 'cil-dashboard');
        $this->insertLink(null, 'Main', '/dashboards/main', 'cil-home');
        $this->insertLink(null, 'KPIs', '/dashboards/kpis', 'cil-kpis');
        $this->insertLink(null, 'Sales', '/dashboards/sales', 'cil-money');
        $this->insertLink(null, 'Statistics', '/dashboards/statistics', 'cil-calculator');
        // $this->insertLink(null, 'Calls', '/dashboards/calls', 'cil-people');
        $this->insertLink(null, 'Maps', '/dashboards/maps', 'cil-map');
        $this->insertLink(null, 'M.R Statistics', '/dashboards/m.r-statistics', 'cib-superuser');
        // $this->insertLink(null, 'Accounts Statistics', '/dashboards/account-types-statistics', 'cil-calculator');
        $this->endDropdown();
        //        $this->insertLink(null, 'Dashboard', '/dashboard', 'cil-home');
        //
        //        $this->beginDropdown(null, 'Analyzer', '/analyzer', 'cil-chart');
        //        $this->insertLink(null, 'KPIS', '/analyzer/kpis', 'cil-kpis');
        //        $this->endDropdown();

        //Calendar Menu

        $id = $this->insertLink(null, 'Calendar', '/calendar', 'cil-calendar');
        $this->addTranslation('ar', 'Calendar', $id);

        // //Login Menu
        // $id = $this->insertLink('guest', 'Login', '/login', 'cil-account-logout');
        // $this->addTranslation('ar', 'تسجيل الدخول', $id);
        // $id = $this->insertLink('guest', 'Register', '/register', 'cil-account-logout');
        // $this->addTranslation('ar', 'تسجيل حساب جديد', $id);


        // Communication Menu

        $id = $this->beginDropdown(null, 'Communication', '/', 'cil-phone');
        $this->addTranslation('ar', 'لوحة التحكم', $id);
        $id = $this->insertLink(null, 'Internal Messaging', '/internal-messaging/inbox', 'cil-speech');
        $id = $this->insertLink(null, 'Tasks', '/tasks', 'cil-task');
        $id = $this->insertLink(null, 'Announcement', '/announcements', 'cil-microphone');
        $id = $this->insertLink(null, 'Live', '/live', 'cil-video');
        // $id = $this->insertLink(null, 'SMS', '/', 'cil-comment-bubble');
        $this->endDropdown();

        //visits Menus (Plan and Actual)
        $id = $this->beginDropdown(null, 'Visits', '/', 'cilColumns');
        $id = $this->beginDropdown(null, 'Plan', '/', 'cilBraille');
        // $id = $this->insertLink(null, 'Plan Visits (Classic View)', '/plans', 'cil-line-spacing');
        $id = $this->insertLink(null, 'Periodical Plan Visits', '/plans', 'cil-loop');
        $id = $this->insertLink(null, 'Plan Scheduler', '/plans/scheduler', 'cil-drag-and-drop');
        $id = $this->insertLink(null, 'Plan Visits (Brick View)', '/plans/bricks', 'cil-list-low-priority');
        $id = $this->insertLink(null, 'Change Plan', '/change-plans', 'cib-plex');
        $id = $this->insertLink(null, 'Plan OW & Activities', '/owplanvisits', 'cil-description');
        $id = $this->insertLink(null, 'Start Point', '/start-point', 'cil-arrow-thick-from-left');
        $id = $this->insertLink(null, 'Plan Approvals', '/planapprovals', 'cil-check');
        $id = $this->insertLink(null, 'Change Plan Approvals', '/change-plan-approvals', 'cib-verizon');
        $id = $this->insertLink(null, 'Plan Double Visit', '/double_plan', 'cil-clone');
        $id = $this->insertLink(null, 'Automatic Plan Visit', '/automatic_plan', 'cib-autotask');
        $this->addTranslation('ar', 'Plan Visits', $id);
        $id = $this->insertLink(null, 'Plan Visit Settings', '/plansettings', 'cil-settings');
        $id = $this->addTranslation('ar', 'الإعدادت العامة للخطة', $id);
        $this->endDropdown();
        $id = $this->beginDropdown(null, 'Actual', '/', 'cil-task');
        $id = $this->insertLink(null, 'Actual Visits', '/actual_visits', 'cil-calendar-check');
        $this->addTranslation('ar', 'Actual Visits', $id);
        $id = $this->insertLink(null, 'Actual OW & Activities', '/owactualvisits', 'cil-description');
        $this->addTranslation('ar', 'Actual OW & Activities', $id);
        $id = $this->insertLink(null, 'Actual Approvals', '/actualapprovals', 'cil-cloudy');
        $id = $this->insertLink(null, 'Double Visit Feedbacks', '/actualdoublefeedbacks', 'cil-speech');
        $id = $this->addTranslation('ar', 'Actual Double Visit Feedback', $id);
        $id = $this->insertLink(null, 'Actual Visit Settings', '/actualvisitsettings', 'cil-settings');
        $id = $this->addTranslation('ar', 'Actual Visit Settings', $id);
        $id = $this->insertLink(null, 'Favourite List', '/favourite-lists', 'cil-fax');
        $id = $this->addTranslation('ar', 'Favourite List', $id);
        $id = $this->insertLink(null, 'Favourite Approvals', '/favourite-list-approvals', 'cil-check');
        $id = $this->insertLink(null, 'Customize Your List', '/kol-lists', 'cil-fax');
        $id = $this->addTranslation('ar', 'Customize Your List', $id);
        $id = $this->insertLink(null, 'List Management', '/list-management', 'cil-briefcase');
        $id = $this->insertLink(null, 'Doctor Frequency', '/doctorfrequencies/create', 'cil-fax');
        $id = $this->addTranslation('ar', 'Doctor Frequencies', $id);
        $this->endDropdown();

        $this->endDropdown();

        // Requests Menu
        $id = $this->beginDropdown(null, 'Requests', '/', 'cil-color-border');
        $id = $this->insertLink(null, 'Commercial and Brandings', '/commercial', 'cil-color-palette');
        $this->addTranslation('ar', 'Commercial & Branding', $id);
        $id = $this->insertLink(null, 'Commercial Approvals', '/commercial-approvals', 'cil-check');
        $id = $this->insertLink(null, 'Commercial Bills', '/commercial-bills', 'cil-list');
        $id = $this->insertLink(null, 'Commercial Bills Approvals', '/commercial-bills-approvals', 'cil-check');
        $id = $this->insertLink(null, 'Custody', '/custody', 'cil-calculator');
        $id = $this->insertLink(null, 'Expenses', '/expenses', 'cil-money');
        $this->addTranslation('ar', 'Expenses', $id);
        $id = $this->insertLink(null, 'Expense Approvals', '/expense-approvals', 'cil-check');
        $id = $this->insertLink(null, 'Location Price', '/expense-location-prices', 'cil-monitor');
        $id = $this->insertLink(null, 'Material', '/material', 'cil-cart');
        $this->addTranslation('ar', 'Material', $id);
        $id = $this->insertLink(null, 'Material Approvals', '/material-approvals', 'cil-check');
        $this->addTranslation('ar', 'Material Approval', $id);
        $id = $this->insertLink(null, 'Material Types', '/promotional-material-types', 'cil-cart');
        $id = $this->insertLink(null, 'Vacations', '/vacations', 'cil-beach-access');
        $this->addTranslation('ar', 'Vacations', $id);
        $id = $this->insertLink(null, 'Vacation Approvals', '/vacation-approvals', 'cil-check');
        $id = $this->insertLink(null, 'Personal Request', '/personal-request', 'cil-user');
        $id = $this->insertLink(null, 'Account Requests', '/account-request', 'cil-color-palette');
        $this->addTranslation('ar', 'Account Requests', $id);
        $id = $this->insertLink(null, 'Account Request Approvals', '/account-request-approvals', 'cil-check');
        $this->addTranslation('ar', 'Personal Request', $id);
        $id = $this->insertLink(null, 'Linked Pharmacies', '/linked-pharmacies', 'cil-clipboard');
        $id = $this->insertLink(null, 'Linked Pharmacies Per Brands', '/linked-pharmacies-per-brand', 'cil-clipboard');
        $this->addTranslation('ar', 'Personal Request', $id);
        $id = $this->insertLink(null, 'Budget Setup', '/budget-setup', 'cil-bank');
        $this->addTranslation('ar', 'Budget Setup', $id);
        $this->endDropdown();

        //Sales Menu

        $id = $this->beginDropdown(null, 'Sales', '/', 'cil-dollar');
        $id = $this->insertLink(null, 'Sales Importer', '/upload', 'cil-data-transfer-down');
        $this->addTranslation('ar', 'Sales Importer', $id);
        $id = $this->insertLink(null, 'Incentive Calculations', '/', 'cil-calculator');
        $this->addTranslation('ar', 'Incentive Calculations', $id);
        $this->endDropdown();
        // Training Menu
        $id = $this->beginDropdown(null, 'Training', '/', 'cil-people');
        $id = $this->insertLink(null, 'Files', '/', 'cil-clipboard');
        $this->addTranslation('ar', 'Files', $id);
        $id = $this->insertLink(null, 'Videos', '/', 'cil-movie');
        $this->addTranslation('ar', 'Videos', $id);
        $id = $this->insertLink(null, 'Live Sessions', '/', 'cil-video');
        $this->addTranslation('ar', 'Live Sessions', $id);
        $id = $this->insertLink(null, 'Coaching', '/coaching', 'cil-list');
        $this->addTranslation('ar', 'Coaching', $id);
        $id = $this->insertLink(null, 'Quizzes', '/quiz', 'cil-info');
        $this->addTranslation('ar', 'Quizzes', $id);
        $this->endDropdown();


        // Tools Menu
        $id = $this->beginDropdown(null, 'Tools', '/', 'cil-briefcase');
        $id = $this->insertLink(null, 'Log Activity', '/logActivities', 'cil-list');
        $this->addTranslation('ar', 'السجل', $id);
        $id = $this->insertLink(null, 'Files Imported', '/imports', 'cil-file');
        $this->endDropdown();


        // Setting Menu
        $id = $this->insertLink(null, 'Settings', '/setting-links', 'cil-settings');
        // $id = $this->addTranslation('ar', 'الخطوط', $id);
        // Groups

        // $id = $this->beginDropdown(null, 'Grouping', '/', 'cil-window');
        // $id = $this->insertLink(null, 'Lines Group', '/lines/group', 'cil-list');
        // $id = $this->insertLink(null, 'Accounts Group', '/accounts/group', 'cil-filter-frames');
        // $id = $this->insertLink(null, 'Account Lines Group', '/account/lines/group', 'cil-envelope-closed');
        // $id = $this->insertLink(null, 'Divisions Group', '/divisions/groups', 'cil-playlist-add');
        // $id = $this->insertLink(null, 'Doctors Group', '/doctors/group', 'cil-flag-alt');
        // $this->endDropdown();

        // Help Link
        // $id = $this->insertLink(null, 'Help', '/help', 'cil-notes');

        // Support

        // $id = $this->insertLink(null, 'Support', '/support', 'cil-life-ring');
        
        // Custom Link
        // $id = $this->insertLink(null, 'Support', '/custom-links', 'cil-life-ring');

        // Report Menu

        // $id = $this->beginDropdown(null, 'Reports', '/reports', 'cil-chart-line');
        // $id = $this->addTranslation('ar', 'التقارير', $id);
        $id = $this->insertLink(null, 'Reports', '/reports', 'cil-align-left');
        // $id = $this->insertLink(null, 'Customize Your Report', '/', 'cil-pencil');

        // $this->endDropdown();


        /* Create top menu */
        DB::table('menulist')->insert([
            'name' => 'top_menu'
        ]);
        $this->menuId = DB::getPdo()->lastInsertId();  //set menuId
        $this->insertAllTranslations();  ///   <===== Must by use on end of this seeder
    }
}
