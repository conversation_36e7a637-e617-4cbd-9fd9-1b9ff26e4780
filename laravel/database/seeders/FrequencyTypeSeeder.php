<?php

namespace Database\Seeders;

use App\Models\FrequencyType;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Schema;

class FrequencyTypeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $frequency_types =
            [
                [
                    'name' => 'class',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'name' => 'doctor',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'name' => 'speciality',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'name' => 'speciality_class',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
            ];

        Schema::disableForeignKeyConstraints();
        FrequencyType::truncate();
        Schema::enableForeignKeyConstraints();

        $chunked_frequency_types = array_chunk($frequency_types, 5);


        foreach ($chunked_frequency_types as $value) {
            FrequencyType::insert($value);
        }
    }
}
