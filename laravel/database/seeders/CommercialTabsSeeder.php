<?php

namespace Database\Seeders;

use App\Models\CommercialTab;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Schema;

class CommercialTabsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $columns =
            [
                [
                    'name' => 'Budget',
                    'select' => 1,
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'name' => 'Main',
                    'select' => 1,
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'name' => 'Cost Types',
                    'select' => 1,
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'name' => 'Products',
                    'select' => 1,
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'name' => 'Assigned',
                    'select' => 1,
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'name' => 'Invited Doctors',
                    'select' => 1,
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'name' => 'Selected Doctors',
                    'select' => 1,
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'name' => 'Doctor Data',
                    'select' => 1,
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'name' => 'Attachments',
                    'select' => 1,
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'name' => 'Cost Elements',
                    'select' => 1,
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'name' => 'Payments',
                    'select' => 1,
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'name' => 'Payment Methods',
                    'select' => 1,
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'name' => 'Agenda',
                    'select' => 1,
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'name' => 'Partial Payment',
                    'select' => 1,
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'name' => 'Service Complete',
                    'select' => 1,
                    'created_at' => now(),
                    'updated_at' => now()
                ],
            ];

        Schema::disableForeignKeyConstraints();
        CommercialTab::truncate();
        Schema::enableForeignKeyConstraints();

        $chunked_columns = array_chunk($columns, 5);


        foreach ($chunked_columns as $value) {
            CommercialTab::insert($value);
        }
    }
}
