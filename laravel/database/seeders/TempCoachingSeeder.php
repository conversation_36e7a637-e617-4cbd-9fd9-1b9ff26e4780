<?php

namespace Database\Seeders;

use App\Form;
use App\Module;
use App\Permission;
use Illuminate\Database\Seeder;

class TempCoachingSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $module =
            [
                'module' => 'coaching',
                'created_at' => now(),
                'updated_at' => now()
            ];
        
        
        $moduleId = Module::insertGetId($module);

        $forms = [
            // coaching types
            [
                'form' => 'types',
                'created_at' => now(),
                'updated_at' => now()
            ],
            // coaching categories
            [
                'form' => 'categories',
                'created_at' => now(),
                'updated_at' => now()
            ],

            // coaching questions
            [
                'form' => 'questions',
                'created_at' => now(),
                'updated_at' => now()
            ],
            // coaching answers
            [
                'form' => 'answers',
                'created_at' => now(),
                'updated_at' => now()
            ],

        ];

        $formIds = [];

        foreach ($forms as $value) {
            $formId = Form::insertGetId($value);
            array_push($formIds, $formId);
        }


        $permissions = [
            // coaching types
            [
                'name'          => 'show_all_coaching_types',
                'guard_name'    => 'api',
                'form_id'       => $formIds[0],
                'action_id'     => '1',
                'module_id'     => $moduleId,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'create_types',
                'guard_name'    => 'api',
                'form_id'       => $formIds[0],
                'action_id'     => '2',
                'module_id'     => $moduleId,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_single_types',
                'guard_name'    => 'api',
                'form_id'       => $formIds[0],
                'action_id'     => '3',
                'module_id'     => $moduleId,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_types',
                'guard_name'    => 'api',
                'form_id'       => $formIds[0],
                'action_id'     => '4',
                'module_id'     => $moduleId,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'delete_types',
                'guard_name'    => 'api',
                'form_id'       => $formIds[0],
                'action_id'     => '5',
                'module_id'     => $moduleId,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'restore_types',
                'guard_name'    => 'api',
                'form_id'       => $formIds[0],
                'action_id'     => '6',
                'module_id'     => $moduleId,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_all_archive_types',
                'guard_name'    => 'api',
                'form_id'       => $formIds[0],
                'action_id'     => '7',
                'module_id'     => $moduleId,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'import_types',
                'guard_name'    => 'api',
                'form_id'       => $formIds[0],
                'action_id'     => '10',
                'module_id'     => $moduleId,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_xlsx_types',
                'guard_name'    => 'api',
                'form_id'       => $formIds[0],
                'action_id'     => '11',
                'module_id'     => $moduleId,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_csv_types',
                'guard_name'    => 'api',
                'form_id'       => $formIds[0],
                'action_id'     => '22',
                'module_id'     => $moduleId,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_pdf_types',
                'guard_name'    => 'api',
                'form_id'       => $formIds[0],
                'action_id'     => '12',
                'module_id'     => $moduleId,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_email_types',
                'guard_name'    => 'api',
                'form_id'       => $formIds[0],
                'action_id'     => '13',
                'module_id'     => $moduleId,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_view_type',
                'guard_name'    => 'api',
                'form_id'       => $formIds[0],
                'action_id'     => '18',
                'module_id'     => $moduleId,
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // coaching categories
            [
                'name'          => 'show_all_categories',
                'guard_name'    => 'api',
                'form_id'       => $formIds[1],
                'action_id'     => '1',
                'module_id'     => $moduleId,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'create_categories',
                'guard_name'    => 'api',
                'form_id'       => $formIds[1],
                'action_id'     => '2',
                'module_id'     => $moduleId,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_single_categories',
                'guard_name'    => 'api',
                'form_id'       => $formIds[1],
                'action_id'     => '3',
                'module_id'     => $moduleId,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_categories',
                'guard_name'    => 'api',
                'form_id'       => $formIds[1],
                'action_id'     => '4',
                'module_id'     => $moduleId,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'delete_categories',
                'guard_name'    => 'api',
                'form_id'       => $formIds[1],
                'action_id'     => '5',
                'module_id'     => $moduleId,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'restore_categories',
                'guard_name'    => 'api',
                'form_id'       => $formIds[1],
                'action_id'     => '6',
                'module_id'     => $moduleId,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_all_archive_categories',
                'guard_name'    => 'api',
                'form_id'       => $formIds[1],
                'action_id'     => '7',
                'module_id'     => $moduleId,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'import_categories',
                'guard_name'    => 'api',
                'form_id'       => $formIds[1],
                'action_id'     => '10',
                'module_id'     => $moduleId,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_xlsx_categories',
                'guard_name'    => 'api',
                'form_id'       => $formIds[1],
                'action_id'     => '11',
                'module_id'     => $moduleId,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_csv_categories',
                'guard_name'    => 'api',
                'form_id'       => $formIds[1],
                'action_id'     => '22',
                'module_id'     => $moduleId,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_pdf_categories',
                'guard_name'    => 'api',
                'form_id'       => $formIds[1],
                'action_id'     => '12',
                'module_id'     => $moduleId,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_email_categories',
                'guard_name'    => 'api',
                'form_id'       => $formIds[1],
                'action_id'     => '13',
                'module_id'     => $moduleId,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_view_category',
                'guard_name'    => 'api',
                'form_id'       => $formIds[1],
                'action_id'     => '18',
                'module_id'     => $moduleId,
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // coaching questions
            [
                'name'          => 'show_all_questions',
                'guard_name'    => 'api',
                'form_id'       => $formIds[2],
                'action_id'     => '1',
                'module_id'     => $moduleId,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'create_questions',
                'guard_name'    => 'api',
                'form_id'       => $formIds[2],
                'action_id'     => '2',
                'module_id'     => $moduleId,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_single_questions',
                'guard_name'    => 'api',
                'form_id'       => $formIds[2],
                'action_id'     => '3',
                'module_id'     => $moduleId,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_questions',
                'guard_name'    => 'api',
                'form_id'       => $formIds[2],
                'action_id'     => '4',
                'module_id'     => $moduleId,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'delete_questions',
                'guard_name'    => 'api',
                'form_id'       => $formIds[2],
                'action_id'     => '5',
                'module_id'     => $moduleId,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'restore_questions',
                'guard_name'    => 'api',
                'form_id'       => $formIds[2],
                'action_id'     => '6',
                'module_id'     => $moduleId,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_all_archive_questions',
                'guard_name'    => 'api',
                'form_id'       => $formIds[2],
                'action_id'     => '7',
                'module_id'     => $moduleId,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'import_questions',
                'guard_name'    => 'api',
                'form_id'       => $formIds[2],
                'action_id'     => '10',
                'module_id'     => $moduleId,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_xlsx_questions',
                'guard_name'    => 'api',
                'form_id'       => $formIds[2],
                'action_id'     => '11',
                'module_id'     => $moduleId,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_csv_questions',
                'guard_name'    => 'api',
                'form_id'       => $formIds[2],
                'action_id'     => '22',
                'module_id'     => $moduleId,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_pdf_questions',
                'guard_name'    => 'api',
                'form_id'       => $formIds[2],
                'action_id'     => '12',
                'module_id'     => $moduleId,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_email_questions',
                'guard_name'    => 'api',
                'form_id'       => $formIds[2],
                'action_id'     => '13',
                'module_id'     => $moduleId,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_view_question',
                'guard_name'    => 'api',
                'form_id'       => $formIds[2],
                'action_id'     => '18',
                'module_id'     => $moduleId,
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // coaching answers
            [
                'name'          => 'show_all_answers',
                'guard_name'    => 'api',
                'form_id'       => $formIds[3],
                'action_id'     => '1',
                'module_id'     => $moduleId,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'create_answers',
                'guard_name'    => 'api',
                'form_id'       => $formIds[3],
                'action_id'     => '2',
                'module_id'     => $moduleId,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_single_answers',
                'guard_name'    => 'api',
                'form_id'       => $formIds[3],
                'action_id'     => '3',
                'module_id'     => $moduleId,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_answers',
                'guard_name'    => 'api',
                'form_id'       => $formIds[3],
                'action_id'     => '4',
                'module_id'     => $moduleId,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'delete_answers',
                'guard_name'    => 'api',
                'form_id'       => $formIds[3],
                'action_id'     => '5',
                'module_id'     => $moduleId,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'restore_answers',
                'guard_name'    => 'api',
                'form_id'       => $formIds[3],
                'action_id'     => '6',
                'module_id'     => $moduleId,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_all_archive_answers',
                'guard_name'    => 'api',
                'form_id'       => $formIds[3],
                'action_id'     => '7',
                'module_id'     => $moduleId,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'import_answers',
                'guard_name'    => 'api',
                'form_id'       => $formIds[3],
                'action_id'     => '10',
                'module_id'     => $moduleId,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_xlsx_answers',
                'guard_name'    => 'api',
                'form_id'       => $formIds[3],
                'action_id'     => '11',
                'module_id'     => $moduleId,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_csv_answers',
                'guard_name'    => 'api',
                'form_id'       => $formIds[3],
                'action_id'     => '22',
                'module_id'     => $moduleId,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_pdf_answers',
                'guard_name'    => 'api',
                'form_id'       => $formIds[3],
                'action_id'     => '12',
                'module_id'     => $moduleId,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_email_answers',
                'guard_name'    => 'api',
                'form_id'       => $formIds[3],
                'action_id'     => '13',
                'module_id'     => $moduleId,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_view_answer',
                'guard_name'    => 'api',
                'form_id'       => $formIds[3],
                'action_id'     => '18',
                'module_id'     => $moduleId,
                'created_at'    => now(),
                'updated_at'    => now()
            ],

        ];

        foreach($permissions as $value){
            Permission::insertOrIgnore($value);
        }
    }
}
