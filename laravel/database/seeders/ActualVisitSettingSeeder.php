<?php

namespace Database\Seeders;

use App\ActualVisitSetting;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Schema;

class ActualVisitSettingSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $actual_settings =
            [
                [
                    'name' => 'No. of Actual Direct',
                    'key' => 'actual_direct',
                    'value' => '2',
                    'type' => 'number',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'name' => 'No. of Products',
                    'key' => 'max_products',
                    'value' => '2',
                    'type' => 'number',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'name' => 'Actual Visit Level',
                    'key' => 'actual_visit_level',
                    'value' => 'Product',
                    'type' => 'select',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'name' => 'Public Holidays',
                    'key' => 'public_holidays',
                    'value' => 'No',
                    'type' => 'checkbox',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'name' => 'Off Days',
                    'key' => 'off_days',
                    'value' => 'No',
                    'type' => 'checkbox',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'name' => 'Actual Start Day',
                    'key' => 'actual_start_day',
                    'value' => 'today',
                    'type' => 'select',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'name' => 'Actual Specific Start Day',
                    'key' => 'specific_actual_start_day',
                    'value' => '',
                    'type' => 'date',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'name' => 'Actual Extra Time',
                    'key' => 'actual_extra_time',
                    'value' => '3',
                    'type' => 'number',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'name' => 'Actual Extra Time work with Vacations',
                    'key' => 'actual_extra_time_with_vacations',
                    'value' => 'No',
                    'type' => 'select',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'name' => 'Actual visits hours range for same account and doctor',
                    'key' => 'actual_hours_range',
                    'value' => '0',
                    'type' => 'number',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'name' => 'Copy Actual visits for managers in double visits',
                    'key' => 'copy_actual_visit',
                    'value' => 'No',
                    'type' => 'select',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'name' => 'Add Presentations For Actual Visits',
                    'key' => 'presentation_actual_visit',
                    'value' => 'No',
                    'type' => 'select',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'name' => 'Accept Visit With Detailing',
                    'key' => 'accept_actual_with_detailing',
                    'value' => 'No',
                    'type' => 'select',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'name' => 'Accept Visit Within Vacations',
                    'key' => 'accept_visits_within_vacations',
                    'value' => 'Yes',
                    'type' => 'select',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'name' => 'Accept Officework With Visits',
                    'key' => 'accept_officework_with_visits',
                    'value' => 'Yes',
                    'type' => 'select',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'name' => 'Accept Single Visit With Manager Double Plan',
                    'key' => 'accept_single_visit_with_manager_double_plan',
                    'value' => 'Yes',
                    'type' => 'select',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'name' => 'Actual Visit With Deviation',
                    'key' => 'allow_actual_with_deviation',
                    'value' => 'No',
                    'type' => 'select',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'name' => 'Actual After Meet Frequency',
                    'key' => 'actual_after_meet_frequency',
                    'value' => 'Yes',
                    'type' => 'select',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'name' => 'Actual Date Disabled',
                    'key' => 'actual_date_disabled',
                    'value' => 'No',
                    'type' => 'select',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'name' => 'Office work with Location',
                    'key' => 'ow_with_location',
                    'value' => 'No',
                    'type' => 'select',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'name' => 'Multiple Doctors visit per account',
                    'key' => 'multiple_doctors_visit',
                    'value' => 'No',
                    'type' => 'select',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'name' => 'Add Shift',
                    'key' => 'add_shift',
                    'value' => 'No',
                    'type' => 'select',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'name' => 'Add Order',
                    'key' => 'add_order',
                    'value' => 'No',
                    'type' => 'select',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'name' => 'Add Stock',
                    'key' => 'add_stock',
                    'value' => 'No',
                    'type' => 'select',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'name' => 'Add Pharmacy Type',
                    'key' => 'add_pharmacy_type',
                    'value' => 'No',
                    'type' => 'select',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'name' => 'Add more than one OW at same day',
                    'key' => 'add_more_than_one_ow_at_same_day',
                    'value' => 'No',
                    'type' => 'select',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
            ];

        Schema::disableForeignKeyConstraints();
        ActualVisitSetting::truncate();
        Schema::enableForeignKeyConstraints();

        $chunked_actual_settings = array_chunk($actual_settings, 5);


        foreach ($chunked_actual_settings as $value) {
            ActualVisitSetting::insert($value);
        }
    }
}
