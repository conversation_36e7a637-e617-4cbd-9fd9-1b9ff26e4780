<?php

namespace Database\Seeders;

use App\Models\PV\PatientSex;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Schema;

class SexSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $age_groups =
            [
                [
                    'id' => 1,
                    'name' => 'Male',
                    'notes' => 'Male',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'id' => 2,
                    'name' => 'Female (Perganent)',
                    'notes' => 'Female (Perganent)',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'id' => 3,
                    'name' => 'Female (Not Perganent)',
                    'notes' => 'Female (Not Perganent)',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
            ];

        Schema::disableForeignKeyConstraints();
        PatientSex::truncate();
        Schema::enableForeignKeyConstraints();

        $chunked_age_groups = array_chunk($age_groups, 5);


        foreach ($chunked_age_groups as $value) {
            PatientSex::insert($value);
        }
    }
}
