<?php

namespace Database\Seeders;

use App\Models\Kpi;
use App\Models\KpiRatio;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class KpisSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $kpis = [
            [
                'id' => 1,
                'name' => 'Coverage',
                'short_name' => 'Cov',
                'sort' => 100,
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id' => 2,
                'name' => 'PM Coverage',
                'short_name' => 'PM Cov',
                'sort' => 200,
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id' => 3,
                'name' => 'AM Coverage',
                'short_name' => 'AM Cov',
                'sort' => 300,
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id' => 4,
                'name' => 'PH Coverage',
                'short_name' => 'PH Cov',
                'sort' => 400,
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id' => 5,
                'name' => 'Call Rate',
                'short_name' => 'C Rate',
                'sort' => 500,
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id' => 6,
                'name' => 'AM Call Rate',
                'short_name' => 'AM C Rate',
                'sort' => 600,
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id' => 7,
                'name' => 'PM Call Rate',
                'short_name' => 'PM C Rate',
                'sort' => 700,
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id' => 8,
                'name' => 'PH Call Rate',
                'short_name' => 'PH C Rate',
                'sort' => 800,
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id' => 9,
                'name' => 'Frequency',
                'short_name' => 'Freq',
                'sort' => 900,
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id' => 10,
                'name' => 'Sales Achievement',
                'short_name' => 'S Ach',
                'sort' => 1000,
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id' => 11,
                'name' => 'Plan Achievement',
                'short_name' => 'P Ach',
                'sort' => 1100,
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id' => 12,
                'name' => 'TEAM Coverage',
                'short_name' => 'TEAM Cov',
                'sort' => 1200,
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id' => 13,
                'name' => 'TEAM AM Coverage',
                'short_name' => 'TEAM AM Cov',
                'sort' => 1300,
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id' => 14,
                'name' => 'TEAM PM Coverage',
                'short_name' => 'TEAM PM Cov',
                'sort' => 1400,
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id' => 15,
                'name' => 'TEAM PH Coverage',
                'short_name' => 'TEAM PH Cov',
                'sort' => 1500,
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id' => 16,
                'name' => 'TEAM Frequency',
                'short_name' => 'TEAM Freq',
                'sort' => 1600,
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id' => 17,
                'name' => 'TEAM Call Rate',
                'short_name' => 'TEAM C Rate',
                'sort' => 1700,
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id' => 18,
                'name' => 'TEAM AM Call Rate',
                'short_name' => 'TEAM AM C Rate',
                'sort' => 1800,
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id' => 19,
                'name' => 'TEAM PM Call Rate',
                'short_name' => 'TEAM PM C Rate',
                'sort' => 1900,
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id' => 20,
                'name' => 'TEAM PH Call Rate',
                'short_name' => 'TEAM PH C Rate',
                'sort' => 2000,
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id' => 21,
                'name' => 'Field Days',
                'short_name' => 'F Days',
                'sort' => 2100,
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id' => 22,
                'name' => 'Team Field Days',
                'short_name' => 'Team F Days',
                'sort' => 2200,
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id' => 23,
                'name' => 'AM Frequency',
                'short_name' => 'AM Frequency',
                'sort' => 2900,
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id' => 24,
                'name' => 'PM Frequency',
                'short_name' => 'PM Frequency',
                'sort' => 3000,
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id' => 25,
                'name' => 'PH Frequency',
                'short_name' => 'PH Frequency',
                'sort' => 3100,
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id' => 26,
                'name' => 'Change Plan',
                'short_name' => 'Change Plan',
                'sort' => 3200,
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id' => 27,
                'name' => 'Other Call Rate',
                'short_name' => 'Other C Rate',
                'sort' => 3300,
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id' => 28,
                'name' => 'Other Coverage',
                'short_name' => 'Other Cov',
                'sort' => 3400,
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id' => 29,
                'name' => 'Vacant Ratio',
                'short_name' => 'V Ratio',
                'sort' => 3500,
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id' => 30,
                'name' => 'Coaching Ratio',
                'short_name' => 'C Ratio',
                'sort' => 3600,
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id' => 31,
                'name' => 'Covered Coaching',
                'short_name' => 'C Ratio',
                'sort' => 3700,
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id' => 32,
                'name' => 'Head Ratio',
                'short_name' => 'H Ratio',
                'sort' => 3800,
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id' => 33,
                'name' => 'D V Mention Ratio',
                'short_name' => 'DV Mention Ratio',
                'sort' => 3900,
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id' => 34,
                'name' => 'Mr Excellent',
                'short_name' => 'Mr Excellent',
                'sort' => 4000,
                'created_at' => now(),
                'updated_at' => now()
            ],
        ];
        Schema::disableForeignKeyConstraints();
        Kpi::truncate();
        KpiRatio::truncate();
        Schema::enableForeignKeyConstraints();

        foreach ($kpis as $value) {
            Kpi::create($value);
        }
    }
}
