<?php

namespace Database\Seeders;

use App\Models\AccountClassification;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Schema;

class AccountClassificationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $classifications =
            [
                [
                    'id' => 1,
                    'name' => 'Private',
                    'sort' => 100,
                ],
                [
                    'id' => 2,
                    'name' => 'Public',
                    'sort' => 200,
                ],
                [
                    'id' => 3,
                    'name' => 'institutional',
                    'sort' => 300,
                ],
            ];

        Schema::disableForeignKeyConstraints();
        AccountClassification::truncate();
        Schema::enableForeignKeyConstraints();

        $chunked_classifications = array_chunk($classifications, 5);


        foreach ($chunked_classifications as $value) {
            AccountClassification::insert($value);
        }
    }
}
