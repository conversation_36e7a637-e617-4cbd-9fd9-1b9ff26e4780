<?php

namespace Database\Seeders;

use App\Models\BudgetSetting;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Schema;

class BudgetSettingSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {

        $budget_settings =
            [
                [
                    'id' => 1,
                    'name' => 'Per Product Or Line',
                    'key' => 'per_product_or_line',
                    'value' => 'Product',
                    'type' => 'select'
                ],
                [
                    'id' => 2,
                    'name' => 'Per Month Or Quarter Or Semester Or Year',
                    'key' => 'budget_period',
                    'value' => 'Month',
                    'type' => 'select'
                ],
                [
                    'id' => 3,
                    'name' => 'accept Requests From User if budget is sufficient',
                    'key' => 'accept_requests_if_budget_is_sufficient',
                    'value' => 'yes',
                    'type' => 'select'
                ],
                [
                    'id' => 4,
                    'name' => 'accept Expenses From User if budget is sufficient',
                    'key' => 'accept_expenses_if_budget_is_sufficient',
                    'value' => 'yes',
                    'type' => 'select'
                ],
                [
                    'id' => 5,
                    'name' => 'accept Materials From User if budget is sufficient',
                    'key' => 'accept_materials_if_budget_is_sufficient',
                    'value' => 'yes',
                    'type' => 'select'
                ],

   
            ];

        Schema::disableForeignKeyConstraints();
        BudgetSetting::truncate();
        Schema::enableForeignKeyConstraints();

        $chunked_budget_settings = array_chunk($budget_settings, 2);


        foreach ($chunked_budget_settings as $value) {
            BudgetSetting::insert($value);
        }
    }
}
