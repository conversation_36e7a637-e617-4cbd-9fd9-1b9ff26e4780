<?php

namespace Database\Seeders;

use App\Models\ItGates\Logo;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Schema;

class ItGatesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Logo::create([
        //     'path' => 'public/logo/images/logo-blue.png',
        // ]);
        // Logo::create([
        //     'path' => 'public/logo/images/white-logo2.png',
        // ]);

        $logo =
            [
                [
                    'path' => 'public/logo/images/logo-blue.png',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'path' => 'public/logo/images/white-logo2.png',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'path' => 'public/logo/images/test.png',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
            ];

        Schema::disableForeignKeyConstraints();
        Logo::truncate();
        Schema::enableForeignKeyConstraints();

        $chunked_logo = array_chunk($logo, 5);


        foreach ($chunked_logo as $value) {
            Logo::insert($value);
        }
    }
}
