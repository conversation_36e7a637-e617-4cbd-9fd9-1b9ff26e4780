<?php

namespace Database\Seeders;

use App\Company;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Storage;

class CompanySettingSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $faker = \Faker\Factory::create();


        Company::create([
            'name' => $faker->company(),
            'tel' => $faker->phoneNumber(),
            'address' => $faker->address(),
            'logo' => '',
        ]);
    }
}
