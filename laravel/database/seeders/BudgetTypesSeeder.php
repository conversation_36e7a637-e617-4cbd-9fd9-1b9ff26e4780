<?php

namespace Database\Seeders;

use App\Models\BudgetType;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Schema;

class BudgetTypesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $budget_types =
            [
                [
                    'id' => 1,
                    'name' => 'Requests',
                    'sort' => 100,
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'id' => 2,
                    'name' => 'Expenses',
                    'sort' => 200,
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'id' => 3,
                    'name' => 'Materials',
                    'sort' => 300,
                    'created_at' => now(),
                    'updated_at' => now()
                ]
            ];

        Schema::disableForeignKeyConstraints();
        BudgetType::truncate();
        Schema::enableForeignKeyConstraints();

        $chunked_budget_types = array_chunk($budget_types, 5);


        foreach ($chunked_budget_types as $value) {
            BudgetType::insert($value);
        }
    }
}
