<?php

namespace Database\Seeders;

use App\SalesSetting;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Schema;

class SalesSettingSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $sales_settings =
            [
                [
                    'name' => 'Number Of Divisions',
                    'key' => 'number_of_divisions',
                    'value' => '10',
                    'type' => 'number',
                    'options' => null,
                ],
                [
                    'name' => 'Contribution Per Product Or Line',
                    'key' => 'contribution_per_product_or_line',
                    'value' => 'Product',
                    'type' => 'select',
                    'options' => json_encode(["values" => ['Product', 'Line']]),
                ],
                [
                    'name' => 'Contribution Level',
                    'key' => 'contribution_level',
                    'value' => 'Division',
                    'type' => 'select',
                    'options' => json_encode(["values" => ['Division', 'Brick']]),
                ],
                [
                    'name' => 'Target Level',
                    'key' => 'target_level',
                    'value' => 'Division',
                    'type' => 'select',
                    'options' => json_encode(["values" => ['Division', 'Brick']]),
                ],
                [
                    'name' => 'Sales Mapping Level',
                    'key' => 'sales_mapping_level',
                    'value' => 'Brick',
                    'type' => 'select',
                    'options' => json_encode(["values" => ['Division', 'Brick', 'Both']]),
                ],
                [
                    'name' => 'Show Sales Date',
                    'key' => 'show_sales_date',
                    'value' => '01/11/2021',
                    'type' => 'date',
                    'options' => null,
                ],
                [
                    'name' => 'Upload Sales Append Or Overwrite',
                    'key' => 'upload_sales_append_or_overwrite',
                    'value' => 'Append',
                    'type' => 'select',
                    'options' => json_encode(["values" => ['Append', 'Overwrite']]),
                ],
                [
                    'name' => 'Sales Price',
                    'key' => 'sales_price',
                    'value' => 'Average',
                    'type' => 'select',
                    'options' => json_encode(["values" => ['Selling', 'Average', 'Tender', 'Target']]),
                ],
                [
                    'name' => 'Target Price',
                    'key' => 'target_price',
                    'value' => 'Average',
                    'type' => 'select',
                    'options' => json_encode(["values" => ['Selling', 'Average', 'Tender', 'Target']]),
                ],
                [
                    'name' => 'Sales Contribution Base on',
                    'key' => 'sales_contribution_base_on',
                    'value' => null,
                    'type' => 'select',
                    'options' => json_encode(["values" => null, "multiple" => true, "lable" => "name"]),
                ],
                [
                    'name' => 'Post Mapping',
                    'key' => 'post_mapping',
                    'value' => 'No',
                    'type' => 'select',
                    'options' => json_encode(["values" => ['Yes', 'No']]),
                ],
                [
                    'name' => 'Mapping With Distributor',
                    'key' => 'mapping_with_distributor',
                    'value' => 'Yes',
                    'type' => 'select',
                    'options' => json_encode(["values" => ['Yes', 'No']]),
                ],
                [
                    'name' => 'Unified Sales',
                    'key' => 'unified_sales',
                    'value' => 'No',
                    'type' => 'select',
                    'options' => json_encode(["values" => ['Yes', 'No']]),
                ],
                [
                    'name' => 'Sales ceiling',
                    'key' => 'sales_ceiling',
                    'value' => 'Difference',
                    'type' => 'select',
                    'options' => json_encode(["values" => ['All', 'Difference']]),
                ],
                [
                    'name' => 'Copy Mapping Into Accounts',
                    'key' => 'copy_mapping_into_accounts',
                    'value' => 'No',
                    'type' => 'select',
                    'options' => json_encode(["values" => ['Yes', 'No']]),
                ],
            ];

        Schema::disableForeignKeyConstraints();
        SalesSetting::truncate();
        Schema::enableForeignKeyConstraints();

        $chunked_sales_settings = array_chunk($sales_settings, 5);


        foreach ($chunked_sales_settings as $value) {
            SalesSetting::insert($value);
        }
    }
}
