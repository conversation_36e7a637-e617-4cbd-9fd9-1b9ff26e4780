<?php

namespace Database\Seeders;
use App\Action;
use Illuminate\Database\Seeder;

class ActionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        //
        $action = Action::create([
            'id' => '1',
            'action' => 'show_all',
            'created_at' => now(),
            'updated_at' => now()
        ]);

        $action = Action::create([
            'id' => '2',
            'action' => 'create',
            'created_at' => now(),
            'updated_at' => now()
        ]);

        $action = Action::create([
            'id' => '3',
            'action' => 'show_single',
            'created_at' => now(),
            'updated_at' => now()
        ]);

        $action = Action::create([
            'id' => '4',
            'action' => 'edit',
            'created_at' => now(),
            'updated_at' => now()
        ]);

        $action = Action::create([
            'id' => '5',
            'action' => 'delete',
            'created_at' => now(),
            'updated_at' => now()
        ]);

        $action = Action::create([
            'id' => '6',
            'action' => 'restore',
            'created_at' => now(),
            'updated_at' => now()
        ]);

        $action = Action::create([
            'id' => '7',
            'action' => 'show_all_archive',
            'created_at' => now(),
            'updated_at' => now()
        ]);

        $action = Action::create([
            'id' => '8',
            'action' => 'destroy',
            'created_at' => now(),
            'updated_at' => now()
        ]);

        $action = Action::create([
            'id' => '9',
            'action' => 'download_template',
            'created_at' => now(),
            'updated_at' => now()
        ]);

        $action = Action::create([
            'id' => '10',
            'action' => 'import',
            'created_at' => now(),
            'updated_at' => now()
        ]);

        $action = Action::create([
            'id' => '11',
            'action' => 'export_xls',
            'created_at' => now(),
            'updated_at' => now()
        ]);

        $action = Action::create([
            'id' => '12',
            'action' => 'export_pdf',
            'created_at' => now(),
            'updated_at' => now()
        ]);

        $action = Action::create([
            'id' => '13',
            'action' => 'export_email',
            'created_at' => now(),
            'updated_at' => now()
        ]);

        $action = Action::create([
            'id' => '14',
            'action' => 'change_password',
            'created_at' => now(),
            'updated_at' => now()
        ]);

        $action = Action::create([
            'id' => '15',
            'action' => 'change_profile_picture',
            'created_at' => now(),
            'updated_at' => now()
        ]);

        $action = Action::create([
            'id' => '16',
            'action' => 'login',
            'created_at' => now(),
            'updated_at' => now()
        ]);

        $action = Action::create([
            'id' => '17',
            'action' => 'logout',
            'created_at' => now(),
            'updated_at' => now()
        ]);

        $action = Action::create([
            'id' => '18',
            'action' => 'edit_view',
            'created_at' => now(),
            'updated_at' => now()
        ]);

        $action = Action::create([
            'id' => '19',
            'action' => 'edit_view_single',
            'created_at' => now(),
            'updated_at' => now()
        ]);

        $action = Action::create([
            'id' => '20',
            'action' => 'download',
            'created_at' => now(),
            'updated_at' => now()
        ]);

        $action = Action::create([
            'id' => '21',
            'action' => 'access_button',
            'created_at' => now(),
            'updated_at' => now()
        ]);
        $action = Action::create([
            'id' => '22',
            'action' => 'export_csv',
            'created_at' => now(),
            'updated_at' => now()
        ]);

        $action = Action::create([
            'id' => '23',
            'action' => 'get_all',
            'created_at' => now(),
            'updated_at' => now()
        ]);
        $action = Action::create([
            'id' => '24',
            'action' => 'reset',
            'created_at' => now(),
            'updated_at' => now()
        ]);
    }
}
