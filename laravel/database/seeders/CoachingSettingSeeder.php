<?php

namespace Database\Seeders;

use App\Models\CoachingSetting;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Schema;

class CoachingSettingSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {

        $coaching_settings =
            [
                [
                    'name' => 'Question Mandatory',
                    'key' => 'question_mandatory',
                    'value' => 'No',
                    'type' => 'select',
                    'options' => json_encode(["values" => ['Yes', 'No']]),
                ],
                [
                    'name' => 'Reason Mandatory',
                    'key' => 'reason_mandatory',
                    'value' => 'No',
                    'type' => 'select',
                    'options' => json_encode(["values" => ['Yes', 'No']]),
                ],
                [
                    'name' => 'Coaching Time Work With Visit Time',
                    'key' => 'coaching_time_work_with_visit_time',
                    'value' => 'No',
                    'type' => 'select',
                    'options' => json_encode(["values" => ['Yes', 'No']]),
                ],
            ];

        Schema::disableForeignKeyConstraints();
        CoachingSetting::truncate();
        Schema::enableForeignKeyConstraints();

        $chunked_coaching_settings = array_chunk($coaching_settings, 5);


        foreach ($chunked_coaching_settings as $value) {
            CoachingSetting::insert($value);
        }
    }
}
