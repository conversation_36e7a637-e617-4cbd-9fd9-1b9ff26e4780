<?php

namespace Database\Seeders;

use App\Models\PV\AgeUnit;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Schema;

class AgeUnitSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $age_units =
            [
                [
                    'id' => 1,
                    'name' => 'D',
                    'notes' => 'D',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'id' => 2,
                    'name' => 'M',
                    'notes' => 'M',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'id' => 3,
                    'name' => 'Y',
                    'notes' => 'Y',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
            ];

        Schema::disableForeignKeyConstraints();
        AgeUnit::truncate();
        Schema::enableForeignKeyConstraints();

        $chunked_age_units = array_chunk($age_units, 5);


        foreach ($chunked_age_units as $value) {
            AgeUnit::insert($value);
        }
    }
}
