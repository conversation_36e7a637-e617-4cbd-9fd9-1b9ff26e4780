<?php

namespace Database\Seeders\Permissions;

use App\Form;
use App\Module;
use App\Permission;
use Illuminate\Database\Seeder;

class NotificationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // notification 

        resetPermissionModule("notification");
        $module = Module::firstOrCreate([
            "module" => "notification",
            "icon" => "cil-list",
        ]);

        $forms = [
            [
                'form' => 'notification_center'
            ],


        ];

        $formIds = [];
        foreach ($forms as $value) {
            $form = Form::firstOrCreate($value);
            array_push($formIds, $form->id);
        }

        $permissions = [
            // coaching types
            [
                'name'          => 'plan_creation_notification',
                'guard_name'    => 'api',
                'form_id'       => $formIds[0],
                'action_id'     => '1',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'plan_approved_disapproved_notification',
                'guard_name'    => 'api',
                'form_id'       => $formIds[0],
                'action_id'     => '2',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'request_notification',
                'guard_name'    => 'api',
                'form_id'       => $formIds[0],
                'action_id'     => '3',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'request_action_notification',
                'guard_name'    => 'api',
                'form_id'       => $formIds[0],
                'action_id'     => '4',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], 


        ];

        foreach ($permissions as $value) {
            Permission::insert($value);
        }
    }
}
