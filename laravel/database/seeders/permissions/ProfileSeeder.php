<?php

namespace Database\Seeders\Permissions;

use App\Form;
use App\Module;
use App\Permission;
use Illuminate\Database\Seeder;

class ProfileSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // profiles

        resetPermissionModule("profiles");
        $module = Module::firstOrCreate([
            "module" => "profiles",
            "icon" => "cil-voice-over-record",
        ]);

        $forms = [
            [
                'form' => 'profile_data'
            ],
        ];

        $formIds = [];
        foreach ($forms as $value) {
            $form = Form::firstOrCreate($value);
            array_push($formIds, $form->id);
        }

        $permissions = [
            // profiles
            [
                'name'          => 'change_profile_pic',
                'guard_name'    => 'api',
                'form_id'       => $formIds[0],
                'action_id'     => '15',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'change_password',
                'guard_name'    => 'api',
                'form_id'       => $formIds[0],
                'action_id'     => '14',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'show_single_user_profile',
                'guard_name'    => 'api',
                'form_id'       => $formIds[0],
                'action_id'     => '3',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'lock_account',
                'guard_name'    => 'api',
                'form_id'       => $formIds[0],
                'action_id'     => '21',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'show_all_support',
                'guard_name'    => 'api',
                'form_id'       => $formIds[0],
                'action_id'     => '1',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'show_user_profile_image',
                'guard_name'    => 'api',
                'form_id'       => $formIds[0],
                'action_id'     => '1',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'show_all_logos',
                'guard_name'    => 'api',
                'form_id'       => $formIds[0],
                'action_id'     => '1',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'show_all_company_logos',
                'guard_name'    => 'api',
                'form_id'       => $formIds[0],
                'action_id'     => '1',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
        ];

        foreach ($permissions as $value) {
            Permission::insert($value);
        }
    }
}
