<?php

namespace Database\Seeders\Permissions;

use App\Form;
use App\Module;
use App\Permission;
use Illuminate\Database\Seeder;

class BudgetSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // budget

        resetPermissionModule("budget");
        // $module = Module::firstOrCreate([
        //     "module" => "budget",
        //     "icon" => "cil-dollar",
        // ]);

        // $forms = [
        //     [
        //         'form' => 'budget_settings'
        //     ],

        // ];

        // $formIds = [];
        // foreach ($forms as $value) {
        //     $form = Form::firstOrCreate($value);
        //     array_push($formIds, $form->id);
        // }

        // $permissions = [
        //     // Budget Settings
        //     [
        //         'name'          => 'show_all_budget_settings',
        //         'guard_name'    => 'api',
        //         'form_id'       => $formIds[0],
        //         'action_id'     => '1',
        //         'module_id'     => $module->id,
        //         'created_at'    => now(),
        //         'updated_at'    => now()
        //     ],
        //     [
        //         'name'          => 'create_budget_settings',
        //         'guard_name'    => 'api',
        //         'form_id'       => $formIds[0],
        //         'action_id'     => '2',
        //         'module_id'     => $module->id,
        //         'created_at'    => now(),
        //         'updated_at'    => now()
        //     ],
        //     [
        //         'name'          => 'show_single_budget_settings',
        //         'guard_name'    => 'api',
        //         'form_id'       => $formIds[0],
        //         'action_id'     => '3',
        //         'module_id'     => $module->id,
        //         'created_at'    => now(),
        //         'updated_at'    => now()
        //     ],
        //     [
        //         'name'          => 'edit_budget_settings',
        //         'guard_name'    => 'api',
        //         'form_id'       => $formIds[0],
        //         'action_id'     => '4',
        //         'module_id'     => $module->id,
        //         'created_at'    => now(),
        //         'updated_at'    => now()
        //     ],

           

        // ];

        // foreach ($permissions as $value) {
        //     Permission::insert($value);
        // }
    }
}
