<?php

namespace Database\Seeders\Permissions;

use App\Form;
use App\Module;
use App\Permission;
use Illuminate\Database\Seeder;

class SideBarMenuSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        resetPermissionModule("menu");
        $module = [
            'module' => 'menu',
            'icon' => 'cil-menu',
        ];

        $Module = Module::firstOrCreate($module);

        $forms = [
            [
                'form' => 'main_menu',
            ],

            // Sidebar Submenu items
            [
                'form' => 'communication_menu',
            ],

            [
                'form' => 'visits_menu',
            ],

            [
                'form' => 'requests_menu',
            ],

            [
                'form' => 'sales_menu',
            ],

            [
                'form' => 'training_menu',
            ],

            [
                'form' => 'tools_menu',
            ],

            [
                'form' => 'reports_menu',
            ],

            [
                'form' => 'plan_visits_menu',
            ],

            [
                'form' => 'actual_visits_menu',
            ],
            [
                'form' => 'dashboard_menu',
            ],

        ];

        $formIds = [];

        foreach ($forms as $value) {
            $form = Form::firstOrCreate($value);
            array_push($formIds, $form->id);
        }

        $permissions = [
            // show dashboard item - menu
            [
                'name'          => 'show_menu_dashboards',
                'guard_name'    => 'api',
                'form_id'       => $formIds[0],
                'action_id'     => '1',
                'module_id'     => $Module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // show calendar item - menu
            [
                'name'          => 'show_menu_calendar',
                'guard_name'    => 'api',
                'form_id'       => $formIds[0],
                'action_id'     => '1',
                'module_id'     => $Module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // show communication item - menu
            [
                'name'          => 'show_menu_communication',
                'guard_name'    => 'api',
                'form_id'       => $formIds[0],
                'action_id'     => '1',
                'module_id'     => $Module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // show visits item - menu
            [
                'name'          => 'show_menu_visits',
                'guard_name'    => 'api',
                'form_id'       => $formIds[0],
                'action_id'     => '1',
                'module_id'     => $Module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // show requests item - menu
            [
                'name'          => 'show_menu_requests',
                'guard_name'    => 'api',
                'form_id'       => $formIds[0],
                'action_id'     => '1',
                'module_id'     => $Module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // show sales item - menu
            [
                'name'          => 'show_menu_sales',
                'guard_name'    => 'api',
                'form_id'       => $formIds[0],
                'action_id'     => '1',
                'module_id'     => $Module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],


            // show training item - menu
            [
                'name'          => 'show_menu_training',
                'guard_name'    => 'api',
                'form_id'       => $formIds[0],
                'action_id'     => '1',
                'module_id'     => $Module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // show tools item - menu
            [
                'name'          => 'show_menu_tools',
                'guard_name'    => 'api',
                'form_id'       => $formIds[0],
                'action_id'     => '1',
                'module_id'     => $Module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // show settings item - menu
            [
                'name'          => 'show_menu_settings',
                'guard_name'    => 'api',
                'form_id'       => $formIds[0],
                'action_id'     => '1',
                'module_id'     => $Module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // show help item - menu
            [
                'name'          => 'show_menu_help',
                'guard_name'    => 'api',
                'form_id'       => $formIds[0],
                'action_id'     => '1',
                'module_id'     => $Module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // show support item - menu
            [
                'name'          => 'show_menu_support',
                'guard_name'    => 'api',
                'form_id'       => $formIds[0],
                'action_id'     => '1',
                'module_id'     => $Module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // show reports item - menu
            [
                'name'          => 'show_menu_reports',
                'guard_name'    => 'api',
                'form_id'       => $formIds[0],
                'action_id'     => '1',
                'module_id'     => $Module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // show internal messaging item - communication
            [
                'name'          => 'show_menu_internal_messaging',
                'guard_name'    => 'api',
                'form_id'       => $formIds[1],
                'action_id'     => '1',
                'module_id'     => $Module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // show tasks item - communication
            [
                'name'          => 'show_menu_tasks',
                'guard_name'    => 'api',
                'form_id'       => $formIds[1],
                'action_id'     => '1',
                'module_id'     => $Module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // show announcement item - communication
            [
                'name'          => 'show_menu_announcement',
                'guard_name'    => 'api',
                'form_id'       => $formIds[1],
                'action_id'     => '1',
                'module_id'     => $Module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // show communication_live item - communication
            [
                'name'          => 'show_menu_live',
                'guard_name'    => 'api',
                'form_id'       => $formIds[1],
                'action_id'     => '1',
                'module_id'     => $Module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // show plan item - visits
            [
                'name'          => 'show_menu_plan',
                'guard_name'    => 'api',
                'form_id'       => $formIds[2],
                'action_id'     => '1',
                'module_id'     => $Module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // show actual item - visits
            [
                'name'          => 'show_menu_actual',
                'guard_name'    => 'api',
                'form_id'       => $formIds[2],
                'action_id'     => '1',
                'module_id'     => $Module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // show commercial & branding item - requests
            [
                'name'          => 'show_menu_commercial_and_brandings',
                'guard_name'    => 'api',
                'form_id'       => $formIds[3],
                'action_id'     => '1',
                'module_id'     => $Module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'show_menu_commercial_approvals',
                'guard_name'    => 'api',
                'form_id'       => $formIds[3],
                'action_id'     => '1',
                'module_id'     => $Module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'show_menu_commercial_bills',
                'guard_name'    => 'api',
                'form_id'       => $formIds[3],
                'action_id'     => '1',
                'module_id'     => $Module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'show_menu_commercial_bills_approvals',
                'guard_name'    => 'api',
                'form_id'       => $formIds[3],
                'action_id'     => '1',
                'module_id'     => $Module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            [
                'name'          => 'show_menu_custody',
                'guard_name'    => 'api',
                'form_id'       => $formIds[3],
                'action_id'     => '1',
                'module_id'     => $Module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // show expenses item - requests
            [
                'name'          => 'show_menu_expenses',
                'guard_name'    => 'api',
                'form_id'       => $formIds[3],
                'action_id'     => '1',
                'module_id'     => $Module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'show_menu_expense_approvals',
                'guard_name'    => 'api',
                'form_id'       => $formIds[3],
                'action_id'     => '1',
                'module_id'     => $Module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'show_menu_location_price',
                'guard_name'    => 'api',
                'form_id'       => $formIds[3],
                'action_id'     => '1',
                'module_id'     => $Module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],


            // show material item - requests
            [
                'name'          => 'show_menu_material',
                'guard_name'    => 'api',
                'form_id'       => $formIds[3],
                'action_id'     => '1',
                'module_id'     => $Module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // show material Approval item - requests
            [
                'name'          => 'show_menu_material_approvals',
                'guard_name'    => 'api',
                'form_id'       => $formIds[3],
                'action_id'     => '1',
                'module_id'     => $Module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // show material types - requests
            [
                'name'          => 'show_menu_material_types',
                'guard_name'    => 'api',
                'form_id'       => $formIds[3],
                'action_id'     => '1',
                'module_id'     => $Module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            // show vacations item - requests
            [
                'name'          => 'show_menu_vacations',
                'guard_name'    => 'api',
                'form_id'       => $formIds[3],
                'action_id'     => '1',
                'module_id'     => $Module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'show_menu_vacation_approvals',
                'guard_name'    => 'api',
                'form_id'       => $formIds[3],
                'action_id'     => '1',
                'module_id'     => $Module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            // show personal requests item - requests
            [
                'name'          => 'show_menu_personal_requests',
                'guard_name'    => 'api',
                'form_id'       => $formIds[3],
                'action_id'     => '1',
                'module_id'     => $Module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            [
                'name'          => 'show_menu_account_requests',
                'guard_name'    => 'api',
                'form_id'       => $formIds[3],
                'action_id'     => '1',
                'module_id'     => $Module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'show_menu_account_request_approvals',
                'guard_name'    => 'api',
                'form_id'       => $formIds[3],
                'action_id'     => '1',
                'module_id'     => $Module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            [
                'name'          => 'show_menu_linked_pharmacies',
                'guard_name'    => 'api',
                'form_id'       => $formIds[3],
                'action_id'     => '1',
                'module_id'     => $Module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'show_menu_linked_pharmacies_per_brands',
                'guard_name'    => 'api',
                'form_id'       => $formIds[3],
                'action_id'     => '1',
                'module_id'     => $Module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // show budget setup item - requests
            [
                'name'          => 'show_menu_budget_setup',
                'guard_name'    => 'api',
                'form_id'       => $formIds[3],
                'action_id'     => '1',
                'module_id'     => $Module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // show sales importer item - sales
            [
                'name'          => 'show_menu_sales_importer',
                'guard_name'    => 'api',
                'form_id'       => $formIds[4],
                'action_id'     => '1',
                'module_id'     => $Module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // show incentive calculations item - sales
            [
                'name'          => 'show_menu_incentive_calculations',
                'guard_name'    => 'api',
                'form_id'       => $formIds[4],
                'action_id'     => '1',
                'module_id'     => $Module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // show files item - training
            [
                'name'          => 'show_menu_files',
                'guard_name'    => 'api',
                'form_id'       => $formIds[5],
                'action_id'     => '1',
                'module_id'     => $Module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // show videos item - training
            [
                'name'          => 'show_menu_videos',
                'guard_name'    => 'api',
                'form_id'       => $formIds[5],
                'action_id'     => '1',
                'module_id'     => $Module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // show training_live item - training
            [
                'name'          => 'show_menu_live_sessions',
                'guard_name'    => 'api',
                'form_id'       => $formIds[5],
                'action_id'     => '1',
                'module_id'     => $Module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // show coaching item - training
            [
                'name'          => 'show_menu_coaching',
                'guard_name'    => 'api',
                'form_id'       => $formIds[5],
                'action_id'     => '1',
                'module_id'     => $Module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // show quizzes item - training
            [
                'name'          => 'show_menu_quizzes',
                'guard_name'    => 'api',
                'form_id'       => $formIds[5],
                'action_id'     => '1',
                'module_id'     => $Module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // show log activity item - tools
            [
                'name'          => 'show_menu_log_activity',
                'guard_name'    => 'api',
                'form_id'       => $formIds[6],
                'action_id'     => '1',
                'module_id'     => $Module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // show files imported item - tools
            [
                'name'          => 'show_menu_files_imported',
                'guard_name'    => 'api',
                'form_id'       => $formIds[6],
                'action_id'     => '1',
                'module_id'     => $Module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // show reports item - reports
            [
                'name'          => 'show_menu_main_reports',
                'guard_name'    => 'api',
                'form_id'       => $formIds[7],
                'action_id'     => '1',
                'module_id'     => $Module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // show customize your report item - reports
            [
                'name'          => 'show_menu_customize_your_report',
                'guard_name'    => 'api',
                'form_id'       => $formIds[7],
                'action_id'     => '1',
                'module_id'     => $Module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],


            // plan visits menu

            // show periodical plan visits item - plan
            [
                'name'          => 'show_menu_periodical_plan_visits',
                'guard_name'    => 'api',
                'form_id'       => $formIds[8],
                'action_id'     => '1',
                'module_id'     => $Module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'show_menu_plan_scheduler',
                'guard_name'    => 'api',
                'form_id'       => $formIds[8],
                'action_id'     => '1',
                'module_id'     => $Module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // show plan visits (brick view) item - plan
            [
                'name'          => 'show_menu_plan_visits_brick_view',
                'guard_name'    => 'api',
                'form_id'       => $formIds[8],
                'action_id'     => '1',
                'module_id'     => $Module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // Change Plan - plan
            [
                'name'          => 'show_menu_change_plan',
                'guard_name'    => 'api',
                'form_id'       => $formIds[8],
                'action_id'     => '1',
                'module_id'     => $Module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // show periodical plan visits item - plan
            [
                'name'          => 'show_menu_plan_ow_activities',
                'guard_name'    => 'api',
                'form_id'       => $formIds[8],
                'action_id'     => '1',
                'module_id'     => $Module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'show_menu_start_point',
                'guard_name'    => 'api',
                'form_id'       => $formIds[8],
                'action_id'     => '1',
                'module_id'     => $Module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // show plan approvals item - plan
            [
                'name'          => 'show_menu_plan_approvals',
                'guard_name'    => 'api',
                'form_id'       => $formIds[8],
                'action_id'     => '1',
                'module_id'     => $Module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            // Change Plan - plan
            [
                'name'          => 'show_menu_change_plan_approvals',
                'guard_name'    => 'api',
                'form_id'       => $formIds[8],
                'action_id'     => '1',
                'module_id'     => $Module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],


            // show plan double visit item - plan
            [
                'name'          => 'show_menu_plan_double_visit',
                'guard_name'    => 'api',
                'form_id'       => $formIds[8],
                'action_id'     => '1',
                'module_id'     => $Module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // show Automatic Create plan double visit item - plan
            [
                'name'          => 'show_menu_automatic_plan_visit',
                'guard_name'    => 'api',
                'form_id'       => $formIds[8],
                'action_id'     => '1',
                'module_id'     => $Module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // show plan visits settings item - plan
            [
                'name'          => 'show_menu_plan_visit_settings',
                'guard_name'    => 'api',
                'form_id'       => $formIds[8],
                'action_id'     => '1',
                'module_id'     => $Module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // actual visits menu

            // show actual visits item - actual
            [
                'name'          => 'show_menu_actual_visits',
                'guard_name'    => 'api',
                'form_id'       => $formIds[9],
                'action_id'     => '1',
                'module_id'     => $Module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // show actual ow & activities item - actual
            [
                'name'          => 'show_menu_actual_ow_activities',
                'guard_name'    => 'api',
                'form_id'       => $formIds[9],
                'action_id'     => '1',
                'module_id'     => $Module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // show Actual visit Approval - actual
            [
                'name'          => 'show_menu_actual_approvals',
                'guard_name'    => 'api',
                'form_id'       => $formIds[9],
                'action_id'     => '1',
                'module_id'     => $Module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // show actual visit settings item - actual
            [
                'name'          => 'show_menu_actual_visit_settings',
                'guard_name'    => 'api',
                'form_id'       => $formIds[9],
                'action_id'     => '1',
                'module_id'     => $Module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // show Actual Double visit Feedback - actual
            [
                'name'          => 'show_menu_double_visit_feedbacks',
                'guard_name'    => 'api',
                'form_id'       => $formIds[9],
                'action_id'     => '1',
                'module_id'     => $Module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // show Favourite List - actual
            [
                'name'          => 'show_menu_favourite_list',
                'guard_name'    => 'api',
                'form_id'       => $formIds[9],
                'action_id'     => '1',
                'module_id'     => $Module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            // show Favourite List - actual
            [
                'name'          => 'show_menu_favourite_approvals',
                'guard_name'    => 'api',
                'form_id'       => $formIds[9],
                'action_id'     => '1',
                'module_id'     => $Module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // show Kol List - actual
            [
                'name'          => 'show_menu_customize_your_list',
                'guard_name'    => 'api',
                'form_id'       => $formIds[9],
                'action_id'     => '1',
                'module_id'     => $Module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // show PV Approvals - actual
[
                'name'          => 'show_menu_pv_approvals',
                'guard_name'    => 'api',
                'form_id'       => $formIds[9],
                'action_id'     => '1',
                'module_id'     => $Module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],


            // show List Management
            [
                'name'          => 'show_menu_list_management',
                'guard_name'    => 'api',
                'form_id'       => $formIds[9],
                'action_id'     => '1',
                'module_id'     => $Module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // show doctor frequencies - actual
            [
                'name'          => 'show_menu_doctor_frequency',
                'guard_name'    => 'api',
                'form_id'       => $formIds[9],
                'action_id'     => '1',
                'module_id'     => $Module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],


            // dashboard menu

            [
                'name'          => 'show_menu_main',
                'guard_name'    => 'api',
                'form_id'       => $formIds[10],
                'action_id'     => '1',
                'module_id'     => $Module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'show_menu_kpis',
                'guard_name'    => 'api',
                'form_id'       => $formIds[10],
                'action_id'     => '1',
                'module_id'     => $Module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'show_menu_link_sales',
                'guard_name'    => 'api',
                'form_id'       => $formIds[10],
                'action_id'     => '1',
                'module_id'     => $Module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'show_menu_statistics',
                'guard_name'    => 'api',
                'form_id'       => $formIds[10],
                'action_id'     => '1',
                'module_id'     => $Module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'show_menu_calls',
                'guard_name'    => 'api',
                'form_id'       => $formIds[10],
                'action_id'     => '1',
                'module_id'     => $Module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'show_menu_maps',
                'guard_name'    => 'api',
                'form_id'       => $formIds[10],
                'action_id'     => '1',
                'module_id'     => $Module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'show_menu_mr_statistics',
                'guard_name'    => 'api',
                'form_id'       => $formIds[10],
                'action_id'     => '1',
                'module_id'     => $Module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'show_menu_account-types-statistics',
                'guard_name'    => 'api',
                'form_id'       => $formIds[10],
                'action_id'     => '1',
                'module_id'     => $Module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],

        ];

        foreach ($permissions as $value) {
            Permission::insert($value);
        }
    }
}
