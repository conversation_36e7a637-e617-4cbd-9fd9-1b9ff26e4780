<?php

namespace Database\Seeders\Permissions;

use App\Permission;
use App\Role;
use Illuminate\Database\Seeder;

class Index extends Seeder
{

    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        Permission::firstOrCreate([
            'name'          => 'all_permissions',
            'guard_name'    => 'api',
            'form_id'       => null,
            'action_id'     => null,
            'module_id'     => null,
        ]);

        $permissionsSeeder = [
            UserSeeder::class,
            ProductSeeder::class,
            LineSeeder::class,
            AccountSeeder::class,
            PlanVisitSeeder::class,
            ActualVisitSeeder::class,
            OfficeWorkSeeder::class,
            BudgetSeeder::class,
            RequestSeeder::class,
            DashboardWidgetSeeder::class,
            SaleSeeder::class,
            PositionSeeder::class,
            ProfileSeeder::class,
            HelpSeeder::class,
            FrequencySeeder::class,
            CommunicationSeeder::class,
            SideBarMenuSeeder::class,
            ReportSeeder::class,
            OtherSeeder::class,
            SettingSeeder::class,
            GeneralSettingSeeder::class,
            TrainingSeeder::class,
            NotificationSeeder::class,
            LinkedPharmacySeeder::class,
            ListManagementSeeder::class
        ];

        foreach ($permissionsSeeder as $seeder) {
            $this->call($seeder);
        }
        
        $adminRole = Role::firstOrCreate(['name' => 'admin']);

        if (!$adminRole->hasPermissionTo('all_permissions')) {
            $adminRole->givePermissionTo('all_permissions');
        }
    }
}
