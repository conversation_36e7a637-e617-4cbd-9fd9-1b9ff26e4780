<?php

namespace Database\Seeders\Permissions;

use App\Form;
use App\Module;
use App\Permission;
use Illuminate\Database\Seeder;

class HelpSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // help

        resetPermissionModule("help");
        $module = Module::firstOrCreate([
            "module" => "help",
            "icon" => "cil-life-ring",
        ]);

        $forms = [
            [
                'form' => 'help'
            ],
            [
                'form' => 'main_topics'
            ],
            [
                'form' => 'sub_topics'
            ],
            [
                'form' => 'articles'
            ],
            [
                'form' => 'keywords'
            ],
            [
                'form' => 'topics'
            ],
        ];

        $formIds = [];
        foreach ($forms as $value) {
            $form = Form::firstOrCreate($value);
            array_push($formIds, $form->id);
        }

        $permissions = [
            // Help Module Permissions

            // help permissions
            [
                'name'          => 'access_button_help_data',
                'guard_name'    => 'api',
                'form_id'       => $formIds[0],
                'action_id'     => '21',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // Main topics

            [
                'name'          => 'show_all_main_topics',
                'guard_name'    => 'api',
                'form_id'       => $formIds[1],
                'action_id'     => '1',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'create_main_topics',
                'guard_name'    => 'api',
                'form_id'       => $formIds[1],
                'action_id'     => '2',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_single_main_topics',
                'guard_name'    => 'api',
                'form_id'       => $formIds[1],
                'action_id'     => '3',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_main_topics',
                'guard_name'    => 'api',
                'form_id'       => $formIds[1],
                'action_id'     => '4',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'delete_main_topics',
                'guard_name'    => 'api',
                'form_id'       => $formIds[1],
                'action_id'     => '5',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'restore_main_topics',
                'guard_name'    => 'api',
                'form_id'       => $formIds[1],
                'action_id'     => '6',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_all_archive_main_topics',
                'guard_name'    => 'api',
                'form_id'       => $formIds[1],
                'action_id'     => '7',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'destroy_main_topics',
                'guard_name'    => 'api',
                'form_id'       => $formIds[1],
                'action_id'     => '8',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'import_main_topics',
                'guard_name'    => 'api',
                'form_id'       => $formIds[1],
                'action_id'     => '10',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_xlsx_main_topics',
                'guard_name'    => 'api',
                'form_id'       => $formIds[1],
                'action_id'     => '11',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_csv_main_topics',
                'guard_name'    => 'api',
                'form_id'       => $formIds[1],
                'action_id'     => '22',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_pdf_main_topics',
                'guard_name'    => 'api',
                'form_id'       => $formIds[1],
                'action_id'     => '12',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_email_main_topics',
                'guard_name'    => 'api',
                'form_id'       => $formIds[1],
                'action_id'     => '13',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_view_main_topic',
                'guard_name'    => 'api',
                'form_id'       => $formIds[1],
                'action_id'     => '18',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // Sub topics

            [
                'name'          => 'show_all_sub_topics',
                'guard_name'    => 'api',
                'form_id'       => $formIds[2],
                'action_id'     => '1',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'create_sub_topics',
                'guard_name'    => 'api',
                'form_id'       => $formIds[2],
                'action_id'     => '2',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_single_sub_topics',
                'guard_name'    => 'api',
                'form_id'       => $formIds[2],
                'action_id'     => '3',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_sub_topics',
                'guard_name'    => 'api',
                'form_id'       => $formIds[2],
                'action_id'     => '4',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'delete_sub_topics',
                'guard_name'    => 'api',
                'form_id'       => $formIds[2],
                'action_id'     => '5',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'restore_sub_topics',
                'guard_name'    => 'api',
                'form_id'       => $formIds[2],
                'action_id'     => '6',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_all_archive_sub_topics',
                'guard_name'    => 'api',
                'form_id'       => $formIds[2],
                'action_id'     => '7',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'destroy_sub_topics',
                'guard_name'    => 'api',
                'form_id'       => $formIds[2],
                'action_id'     => '8',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'import_sub_topics',
                'guard_name'    => 'api',
                'form_id'       => $formIds[2],
                'action_id'     => '10',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_xlsx_sub_topics',
                'guard_name'    => 'api',
                'form_id'       => $formIds[2],
                'action_id'     => '11',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_csv_sub_topics',
                'guard_name'    => 'api',
                'form_id'       => $formIds[2],
                'action_id'     => '22',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_pdf_sub_topics',
                'guard_name'    => 'api',
                'form_id'       => $formIds[2],
                'action_id'     => '12',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_email_sub_topics',
                'guard_name'    => 'api',
                'form_id'       => $formIds[2],
                'action_id'     => '13',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_view_sub_topic',
                'guard_name'    => 'api',
                'form_id'       => $formIds[2],
                'action_id'     => '18',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // articles

            [
                'name'          => 'show_all_articles',
                'guard_name'    => 'api',
                'form_id'       => $formIds[3],
                'action_id'     => '1',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'create_articles',
                'guard_name'    => 'api',
                'form_id'       => $formIds[3],
                'action_id'     => '2',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_single_articles',
                'guard_name'    => 'api',
                'form_id'       => $formIds[3],
                'action_id'     => '3',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_articles',
                'guard_name'    => 'api',
                'form_id'       => $formIds[3],
                'action_id'     => '4',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'delete_articles',
                'guard_name'    => 'api',
                'form_id'       => $formIds[3],
                'action_id'     => '5',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'restore_articles',
                'guard_name'    => 'api',
                'form_id'       => $formIds[3],
                'action_id'     => '6',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_all_archive_articles',
                'guard_name'    => 'api',
                'form_id'       => $formIds[3],
                'action_id'     => '7',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'destroy_articles',
                'guard_name'    => 'api',
                'form_id'       => $formIds[3],
                'action_id'     => '8',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'import_articles',
                'guard_name'    => 'api',
                'form_id'       => $formIds[3],
                'action_id'     => '10',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_xlsx_articles',
                'guard_name'    => 'api',
                'form_id'       => $formIds[3],
                'action_id'     => '11',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_csv_articles',
                'guard_name'    => 'api',
                'form_id'       => $formIds[3],
                'action_id'     => '22',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_pdf_articles',
                'guard_name'    => 'api',
                'form_id'       => $formIds[3],
                'action_id'     => '12',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_email_articles',
                'guard_name'    => 'api',
                'form_id'       => $formIds[3],
                'action_id'     => '13',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_view_articles',
                'guard_name'    => 'api',
                'form_id'       => $formIds[3],
                'action_id'     => '18',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // Keywords

            [
                'name'          => 'show_all_keywords',
                'guard_name'    => 'api',
                'form_id'       => $formIds[4],
                'action_id'     => '1',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'create_keywords',
                'guard_name'    => 'api',
                'form_id'       => $formIds[4],
                'action_id'     => '2',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_single_keywords',
                'guard_name'    => 'api',
                'form_id'       => $formIds[4],
                'action_id'     => '3',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_keywords',
                'guard_name'    => 'api',
                'form_id'       => $formIds[4],
                'action_id'     => '4',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'delete_keywords',
                'guard_name'    => 'api',
                'form_id'       => $formIds[4],
                'action_id'     => '5',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'restore_keywords',
                'guard_name'    => 'api',
                'form_id'       => $formIds[4],
                'action_id'     => '6',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_all_archive_keywords',
                'guard_name'    => 'api',
                'form_id'       => $formIds[4],
                'action_id'     => '7',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'destroy_keywords',
                'guard_name'    => 'api',
                'form_id'       => $formIds[4],
                'action_id'     => '8',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'import_keywords',
                'guard_name'    => 'api',
                'form_id'       => $formIds[4],
                'action_id'     => '10',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_xlsx_keywords',
                'guard_name'    => 'api',
                'form_id'       => $formIds[4],
                'action_id'     => '11',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_csv_keywords',
                'guard_name'    => 'api',
                'form_id'       => $formIds[4],
                'action_id'     => '22',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_pdf_keywords',
                'guard_name'    => 'api',
                'form_id'       => $formIds[4],
                'action_id'     => '12',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_email_keywords',
                'guard_name'    => 'api',
                'form_id'       => $formIds[4],
                'action_id'     => '13',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_view_keyword',
                'guard_name'    => 'api',
                'form_id'       => $formIds[4],
                'action_id'     => '18',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // topics

            [
                'name'          => 'show_all_topics',
                'guard_name'    => 'api',
                'form_id'       => $formIds[5],
                'action_id'     => '1',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'create_topics',
                'guard_name'    => 'api',
                'form_id'       => $formIds[5],
                'action_id'     => '2',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_single_topics',
                'guard_name'    => 'api',
                'form_id'       => $formIds[5],
                'action_id'     => '3',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_topics',
                'guard_name'    => 'api',
                'form_id'       => $formIds[5],
                'action_id'     => '4',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'delete_topics',
                'guard_name'    => 'api',
                'form_id'       => $formIds[5],
                'action_id'     => '5',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'restore_topics',
                'guard_name'    => 'api',
                'form_id'       => $formIds[5],
                'action_id'     => '6',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_all_archive_topics',
                'guard_name'    => 'api',
                'form_id'       => $formIds[5],
                'action_id'     => '7',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'destroy_topics',
                'guard_name'    => 'api',
                'form_id'       => $formIds[5],
                'action_id'     => '8',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],  [
                'name'          => 'import_topics',
                'guard_name'    => 'api',
                'form_id'       => $formIds[5],
                'action_id'     => '10',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_xlsx_topics',
                'guard_name'    => 'api',
                'form_id'       => $formIds[5],
                'action_id'     => '11',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_csv_topics',
                'guard_name'    => 'api',
                'form_id'       => $formIds[5],
                'action_id'     => '22',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_pdf_topics',
                'guard_name'    => 'api',
                'form_id'       => $formIds[5],
                'action_id'     => '12',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_email_topics',
                'guard_name'    => 'api',
                'form_id'       => $formIds[5],
                'action_id'     => '13',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_view_topic',
                'guard_name'    => 'api',
                'form_id'       => $formIds[5],
                'action_id'     => '18',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
        ];

        foreach ($permissions as $value) {
            Permission::insert($value);
        }
    }
}
