<?php

namespace Database\Seeders\Permissions;

use App\Form;
use App\Module;
use App\Permission;
use Illuminate\Database\Seeder;

class ListManagementSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        resetPermissionModule("list_management");
        $module = [
            'module' => 'list_management',
            'icon' => 'cil-menu',
        ];

        $Module = Module::firstOrCreate($module);

        $forms = [
            [
                'form' => 'actions_menu',
            ],

            // Sidebar Submenu items
            [
                'form' => 'copy_menu',
            ],

            [
                'form' => 'move_menu',
            ],
        ];

        $formIds = [];

        foreach ($forms as $value) {
            $form = Form::firstOrCreate($value);
            array_push($formIds, $form->id);
        }

        $permissions = [
            // show dashboard item - menu
            [
                'name'          => 'list_management_copy',
                'guard_name'    => 'api',
                'form_id'       => $formIds[0],
                'action_id'     => '1',
                'module_id'     => $Module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'list_management_move',
                'guard_name'    => 'api',
                'form_id'       => $formIds[0],
                'action_id'     => '1',
                'module_id'     => $Module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            

            // Copy List Management
            [
                'name'          => 'list_management_copy_active_from',
                'guard_name'    => 'api',
                'form_id'       => $formIds[1],
                'action_id'     => '1',
                'module_id'     => $Module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
           

            // Move List Management
            [
                'name'          => 'list_management_move_account_type',
                'guard_name'    => 'api',
                'form_id'       => $formIds[2],
                'action_id'     => '1',
                'module_id'     => $Module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'list_management_move_speciality',
                'guard_name'    => 'api',
                'form_id'       => $formIds[2],
                'action_id'     => '1',
                'module_id'     => $Module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'list_management_move_account_class',
                'guard_name'    => 'api',
                'form_id'       => $formIds[2],
                'action_id'     => '1',
                'module_id'     => $Module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'list_management_move_doctor_class',
                'guard_name'    => 'api',
                'form_id'       => $formIds[2],
                'action_id'     => '1',
                'module_id'     => $Module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
        ];

        foreach ($permissions as $value) {
            Permission::insert($value);
        }
    }
}
