<?php

namespace Database\Seeders\Permissions;

use App\Form;
use App\Module;
use App\Permission;
use Illuminate\Database\Seeder;

class LineSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // lines

        resetPermissionModule("lines");
        $module = Module::firstOrCreate([
            "module" => "lines",
            "icon" => "cib-letterboxd",
        ]);

        $forms = [
            [
                'form' => 'countries'
            ],
            [
                'form' => 'currencies'
            ],
            [
                'form' => 'division_types'
            ],
            [
                'form' => 'lines'
            ],
            [
                'form' => 'line_division_types'
            ],
            [
                'form' => 'line_divisions'
            ],
            [
                'form' => 'line_div_parents'
            ],
            [
                'form' => 'line_products'
            ],
            [
                'form' => 'line_users'
            ],
            [
                'form' => 'line_user_divisions'
            ],
            [
                'form' => 'line_classes'
            ],
            [
                'form' => 'line_bricks'
            ],
            [
                'form' => 'line_specialities'
            ],
            [
                'form' => 'line_giveaways'
            ],
        ];

        $formIds = [];
        foreach ($forms as $value) {
            $form = Form::firstOrCreate($value);
            array_push($formIds, $form->id);
        }

        $permissions = [
            // Countries
            [
                'name'          => 'show_all_countries',
                'guard_name'    => 'api',
                'form_id'       => $formIds[0],
                'action_id'     => '1',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'create_countries',
                'guard_name'    => 'api',
                'form_id'       => $formIds[0],
                'action_id'     => '2',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_single_countries',
                'guard_name'    => 'api',
                'form_id'       => $formIds[0],
                'action_id'     => '3',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_countries',
                'guard_name'    => 'api',
                'form_id'       => $formIds[0],
                'action_id'     => '4',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'delete_countries',
                'guard_name'    => 'api',
                'form_id'       => $formIds[0],
                'action_id'     => '5',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'restore_countries',
                'guard_name'    => 'api',
                'form_id'       => $formIds[0],
                'action_id'     => '6',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_all_archive_countries',
                'guard_name'    => 'api',
                'form_id'       => $formIds[0],
                'action_id'     => '7',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'import_countries',
                'guard_name'    => 'api',
                'form_id'       => $formIds[0],
                'action_id'     => '10',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_xlsx_countries',
                'guard_name'    => 'api',
                'form_id'       => $formIds[0],
                'action_id'     => '11',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_csv_countries',
                'guard_name'    => 'api',
                'form_id'       => $formIds[0],
                'action_id'     => '22',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_pdf_countries',
                'guard_name'    => 'api',
                'form_id'       => $formIds[0],
                'action_id'     => '12',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_email_countries',
                'guard_name'    => 'api',
                'form_id'       => $formIds[0],
                'action_id'     => '13',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_view_country',
                'guard_name'    => 'api',
                'form_id'       => $formIds[0],
                'action_id'     => '18',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // Currencies
            [
                'name'          => 'show_all_currencies',
                'guard_name'    => 'api',
                'form_id'       => $formIds[1],
                'action_id'     => '1',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'create_currencies',
                'guard_name'    => 'api',
                'form_id'       => $formIds[1],
                'action_id'     => '2',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_single_currencies',
                'guard_name'    => 'api',
                'form_id'       => $formIds[1],
                'action_id'     => '3',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_currencies',
                'guard_name'    => 'api',
                'form_id'       => $formIds[1],
                'action_id'     => '4',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'delete_currencies',
                'guard_name'    => 'api',
                'form_id'       => $formIds[1],
                'action_id'     => '5',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'restore_currencies',
                'guard_name'    => 'api',
                'form_id'       => $formIds[1],
                'action_id'     => '6',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_all_archive_currencies',
                'guard_name'    => 'api',
                'form_id'       => $formIds[1],
                'action_id'     => '7',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'import_currencies',
                'guard_name'    => 'api',
                'form_id'       => $formIds[1],
                'action_id'     => '10',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_xlsx_currencies',
                'guard_name'    => 'api',
                'form_id'       => $formIds[1],
                'action_id'     => '11',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_csv_currencies',
                'guard_name'    => 'api',
                'form_id'       => $formIds[1],
                'action_id'     => '22',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_pdf_currencies',
                'guard_name'    => 'api',
                'form_id'       => $formIds[1],
                'action_id'     => '12',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_email_currencies',
                'guard_name'    => 'api',
                'form_id'       => $formIds[1],
                'action_id'     => '13',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            [
                'name'          => 'edit_view_currency',
                'guard_name'    => 'api',
                'form_id'       => $formIds[1],
                'action_id'     => '18',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // Division Types

            [
                'name'          => 'show_all_divisiontypes',
                'guard_name'    => 'api',
                'form_id'       => $formIds[2],
                'action_id'     => '1',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'create_divisiontypes',
                'guard_name'    => 'api',
                'form_id'       => $formIds[2],
                'action_id'     => '2',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_single_divisiontypes',
                'guard_name'    => 'api',
                'form_id'       => $formIds[2],
                'action_id'     => '3',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_divisiontypes',
                'guard_name'    => 'api',
                'form_id'       => $formIds[2],
                'action_id'     => '4',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'delete_divisiontypes',
                'guard_name'    => 'api',
                'form_id'       => $formIds[2],
                'action_id'     => '5',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'restore_divisiontypes',
                'guard_name'    => 'api',
                'form_id'       => $formIds[2],
                'action_id'     => '6',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_all_archive_divisiontypes',
                'guard_name'    => 'api',
                'form_id'       => $formIds[2],
                'action_id'     => '7',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'destroy_divisiontypes',
                'guard_name'    => 'api',
                'form_id'       => $formIds[2],
                'action_id'     => '8',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'import_divisiontypes',
                'guard_name'    => 'api',
                'form_id'       => $formIds[2],
                'action_id'     => '10',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_xlsx_divisiontypes',
                'guard_name'    => 'api',
                'form_id'       => $formIds[2],
                'action_id'     => '11',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_csv_divisiontypes',
                'guard_name'    => 'api',
                'form_id'       => $formIds[2],
                'action_id'     => '22',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_pdf_divisiontypes',
                'guard_name'    => 'api',
                'form_id'       => $formIds[2],
                'action_id'     => '12',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_email_divisiontypes',
                'guard_name'    => 'api',
                'form_id'       => $formIds[2],
                'action_id'     => '13',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_view_divisiontype',
                'guard_name'    => 'api',
                'form_id'       => $formIds[2],
                'action_id'     => '18',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // Lines
            [
                'name'          => 'show_all_lines',
                'guard_name'    => 'api',
                'form_id'       => $formIds[3],
                'action_id'     => '1',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'create_lines',
                'guard_name'    => 'api',
                'form_id'       => $formIds[3],
                'action_id'     => '2',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_single_lines',
                'guard_name'    => 'api',
                'form_id'       => $formIds[3],
                'action_id'     => '3',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_lines',
                'guard_name'    => 'api',
                'form_id'       => $formIds[3],
                'action_id'     => '4',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'delete_lines',
                'guard_name'    => 'api',
                'form_id'       => $formIds[3],
                'action_id'     => '5',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'restore_lines',
                'guard_name'    => 'api',
                'form_id'       => $formIds[3],
                'action_id'     => '6',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_all_archive_lines',
                'guard_name'    => 'api',
                'form_id'       => $formIds[3],
                'action_id'     => '7',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'import_lines',
                'guard_name'    => 'api',
                'form_id'       => $formIds[3],
                'action_id'     => '10',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_xlsx_lines',
                'guard_name'    => 'api',
                'form_id'       => $formIds[3],
                'action_id'     => '11',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_csv_lines',
                'guard_name'    => 'api',
                'form_id'       => $formIds[3],
                'action_id'     => '22',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_pdf_lines',
                'guard_name'    => 'api',
                'form_id'       => $formIds[3],
                'action_id'     => '12',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_email_lines',
                'guard_name'    => 'api',
                'form_id'       => $formIds[3],
                'action_id'     => '13',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_view_line',
                'guard_name'    => 'api',
                'form_id'       => $formIds[3],
                'action_id'     => '18',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // line division types
            [
                'name'          => 'show_all_line_division_types',
                'guard_name'    => 'api',
                'form_id'       => $formIds[4],
                'action_id'     => '1',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'create_line_division_types',
                'guard_name'    => 'api',
                'form_id'       => $formIds[4],
                'action_id'     => '2',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_single_line_division_types',
                'guard_name'    => 'api',
                'form_id'       => $formIds[4],
                'action_id'     => '3',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_line_division_types',
                'guard_name'    => 'api',
                'form_id'       => $formIds[4],
                'action_id'     => '4',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'delete_line_division_types',
                'guard_name'    => 'api',
                'form_id'       => $formIds[4],
                'action_id'     => '5',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'restore_line_division_types',
                'guard_name'    => 'api',
                'form_id'       => $formIds[4],
                'action_id'     => '6',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_all_archive_line_division_types',
                'guard_name'    => 'api',
                'form_id'       => $formIds[4],
                'action_id'     => '7',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'destroy_line_division_types',
                'guard_name'    => 'api',
                'form_id'       => $formIds[4],
                'action_id'     => '8',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'import_line_division_types',
                'guard_name'    => 'api',
                'form_id'       => $formIds[4],
                'action_id'     => '10',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_xlsx_line_division_types',
                'guard_name'    => 'api',
                'form_id'       => $formIds[4],
                'action_id'     => '11',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_csv_line_division_types',
                'guard_name'    => 'api',
                'form_id'       => $formIds[4],
                'action_id'     => '22',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_pdf_line_division_types',
                'guard_name'    => 'api',
                'form_id'       => $formIds[4],
                'action_id'     => '12',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_email_line_division_types',
                'guard_name'    => 'api',
                'form_id'       => $formIds[4],
                'action_id'     => '13',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_view_line_division_types',
                'guard_name'    => 'api',
                'form_id'       => $formIds[4],
                'action_id'     => '18',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_view_single_line_division_type',
                'guard_name'    => 'api',
                'form_id'       => $formIds[4],
                'action_id'     => '19',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // Line Divisions

            [
                'name'          => 'show_all_line_divisions',
                'guard_name'    => 'api',
                'form_id'       => $formIds[5],
                'action_id'     => '1',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'create_line_divisions',
                'guard_name'    => 'api',
                'form_id'       => $formIds[5],
                'action_id'     => '2',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_single_line_divisions',
                'guard_name'    => 'api',
                'form_id'       => $formIds[5],
                'action_id'     => '3',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_line_divisions',
                'guard_name'    => 'api',
                'form_id'       => $formIds[5],
                'action_id'     => '4',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'delete_line_divisions',
                'guard_name'    => 'api',
                'form_id'       => $formIds[5],
                'action_id'     => '5',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'restore_line_divisions',
                'guard_name'    => 'api',
                'form_id'       => $formIds[5],
                'action_id'     => '6',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_all_archive_line_divisions',
                'guard_name'    => 'api',
                'form_id'       => $formIds[5],
                'action_id'     => '7',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'destroy_line_divisions',
                'guard_name'    => 'api',
                'form_id'       => $formIds[3],
                'action_id'     => '8',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'import_line_divisions',
                'guard_name'    => 'api',
                'form_id'       => $formIds[5],
                'action_id'     => '10',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_xlsx_line_divisions',
                'guard_name'    => 'api',
                'form_id'       => $formIds[5],
                'action_id'     => '11',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_csv_line_divisions',
                'guard_name'    => 'api',
                'form_id'       => $formIds[5],
                'action_id'     => '22',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_pdf_line_divisions',
                'guard_name'    => 'api',
                'form_id'       => $formIds[5],
                'action_id'     => '12',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_email_line_divisions',
                'guard_name'    => 'api',
                'form_id'       => $formIds[5],
                'action_id'     => '13',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_view_line_divisions',
                'guard_name'    => 'api',
                'form_id'       => $formIds[5],
                'action_id'     => '18',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_view_single_line_division',
                'guard_name'    => 'api',
                'form_id'       => $formIds[5],
                'action_id'     => '19',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // Line Division Parents
            [
                'name'          => 'show_all_line_div_parents',
                'guard_name'    => 'api',
                'form_id'       => $formIds[6],
                'action_id'     => '1',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'create_line_div_parents',
                'guard_name'    => 'api',
                'form_id'       => $formIds[6],
                'action_id'     => '2',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_single_line_div_parents',
                'guard_name'    => 'api',
                'form_id'       => $formIds[6],
                'action_id'     => '3',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_line_div_parents',
                'guard_name'    => 'api',
                'form_id'       => $formIds[6],
                'action_id'     => '4',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'delete_line_div_parents',
                'guard_name'    => 'api',
                'form_id'       => $formIds[6],
                'action_id'     => '5',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'restore_line_div_parents',
                'guard_name'    => 'api',
                'form_id'       => $formIds[6],
                'action_id'     => '6',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_all_archive_line_div_parents',
                'guard_name'    => 'api',
                'form_id'       => $formIds[6],
                'action_id'     => '7',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'import_line_div_parents',
                'guard_name'    => 'api',
                'form_id'       => $formIds[6],
                'action_id'     => '10',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_xlsx_line_div_parents',
                'guard_name'    => 'api',
                'form_id'       => $formIds[6],
                'action_id'     => '11',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_csv_line_div_parents',
                'guard_name'    => 'api',
                'form_id'       => $formIds[6],
                'action_id'     => '22',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_pdf_line_div_parents',
                'guard_name'    => 'api',
                'form_id'       => $formIds[6],
                'action_id'     => '12',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_email_line_div_parents',
                'guard_name'    => 'api',
                'form_id'       => $formIds[6],
                'action_id'     => '13',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_view_line_div_parents',
                'guard_name'    => 'api',
                'form_id'       => $formIds[6],
                'action_id'     => '18',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_view_single_line_div_parent',
                'guard_name'    => 'api',
                'form_id'       => $formIds[6],
                'action_id'     => '19',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // line products
            [
                'name'          => 'show_all_line_products',
                'guard_name'    => 'api',
                'form_id'       => $formIds[7],
                'action_id'     => '1',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'create_line_products',
                'guard_name'    => 'api',
                'form_id'       => $formIds[7],
                'action_id'     => '2',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_single_line_products',
                'guard_name'    => 'api',
                'form_id'       => $formIds[7],
                'action_id'     => '3',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_line_products',
                'guard_name'    => 'api',
                'form_id'       => $formIds[7],
                'action_id'     => '4',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'delete_line_products',
                'guard_name'    => 'api',
                'form_id'       => $formIds[7],
                'action_id'     => '5',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'restore_line_products',
                'guard_name'    => 'api',
                'form_id'       => $formIds[7],
                'action_id'     => '6',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_all_archive_line_products',
                'guard_name'    => 'api',
                'form_id'       => $formIds[7],
                'action_id'     => '7',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'import_line_products',
                'guard_name'    => 'api',
                'form_id'       => $formIds[7],
                'action_id'     => '10',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_xlsx_line_products',
                'guard_name'    => 'api',
                'form_id'       => $formIds[7],
                'action_id'     => '11',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_csv_line_products',
                'guard_name'    => 'api',
                'form_id'       => $formIds[7],
                'action_id'     => '22',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_pdf_line_products',
                'guard_name'    => 'api',
                'form_id'       => $formIds[7],
                'action_id'     => '12',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_email_line_products',
                'guard_name'    => 'api',
                'form_id'       => $formIds[7],
                'action_id'     => '13',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_view_line_products',
                'guard_name'    => 'api',
                'form_id'       => $formIds[7],
                'action_id'     => '18',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_view_single_line_product',
                'guard_name'    => 'api',
                'form_id'       => $formIds[7],
                'action_id'     => '19',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // line users

            [
                'name'          => 'show_all_line_users',
                'guard_name'    => 'api',
                'form_id'       => $formIds[8],
                'action_id'     => '1',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'create_line_users',
                'guard_name'    => 'api',
                'form_id'       => $formIds[8],
                'action_id'     => '2',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_single_line_users',
                'guard_name'    => 'api',
                'form_id'       => $formIds[8],
                'action_id'     => '3',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_line_users',
                'guard_name'    => 'api',
                'form_id'       => $formIds[8],
                'action_id'     => '4',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'delete_line_users',
                'guard_name'    => 'api',
                'form_id'       => $formIds[8],
                'action_id'     => '5',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'restore_line_users',
                'guard_name'    => 'api',
                'form_id'       => $formIds[8],
                'action_id'     => '6',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_all_archive_line_users',
                'guard_name'    => 'api',
                'form_id'       => $formIds[8],
                'action_id'     => '7',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'import_line_users',
                'guard_name'    => 'api',
                'form_id'       => $formIds[8],
                'action_id'     => '10',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_xlsx_line_users',
                'guard_name'    => 'api',
                'form_id'       => $formIds[8],
                'action_id'     => '11',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_csv_line_users',
                'guard_name'    => 'api',
                'form_id'       => $formIds[8],
                'action_id'     => '22',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_pdf_line_users',
                'guard_name'    => 'api',
                'form_id'       => $formIds[8],
                'action_id'     => '12',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_email_line_users',
                'guard_name'    => 'api',
                'form_id'       => $formIds[8],
                'action_id'     => '13',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_view_line_users',
                'guard_name'    => 'api',
                'form_id'       => $formIds[8],
                'action_id'     => '18',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_view_single_line_user',
                'guard_name'    => 'api',
                'form_id'       => $formIds[8],
                'action_id'     => '19',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],



            // line user divisions

            [
                'name'          => 'show_all_line_user_divisions',
                'guard_name'    => 'api',
                'form_id'       => $formIds[9],
                'action_id'     => '1',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'create_line_user_divisions',
                'guard_name'    => 'api',
                'form_id'       => $formIds[9],
                'action_id'     => '2',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_single_line_user_divisions',
                'guard_name'    => 'api',
                'form_id'       => $formIds[9],
                'action_id'     => '3',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_line_user_divisions',
                'guard_name'    => 'api',
                'form_id'       => $formIds[9],
                'action_id'     => '4',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'delete_line_user_divisions',
                'guard_name'    => 'api',
                'form_id'       => $formIds[9],
                'action_id'     => '5',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'restore_line_user_divisions',
                'guard_name'    => 'api',
                'form_id'       => $formIds[9],
                'action_id'     => '6',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_all_archive_line_user_divisions',
                'guard_name'    => 'api',
                'form_id'       => $formIds[9],
                'action_id'     => '7',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'import_line_user_divisions',
                'guard_name'    => 'api',
                'form_id'       => $formIds[9],
                'action_id'     => '10',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_xlsx_line_user_divisions',
                'guard_name'    => 'api',
                'form_id'       => $formIds[9],
                'action_id'     => '11',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_csv_line_user_divisions',
                'guard_name'    => 'api',
                'form_id'       => $formIds[9],
                'action_id'     => '22',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_pdf_line_user_divisions',
                'guard_name'    => 'api',
                'form_id'       => $formIds[9],
                'action_id'     => '12',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_email_line_user_divisions',
                'guard_name'    => 'api',
                'form_id'       => $formIds[9],
                'action_id'     => '13',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_view_line_user_divisions',
                'guard_name'    => 'api',
                'form_id'       => $formIds[9],
                'action_id'     => '18',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_view_single_line_user_division',
                'guard_name'    => 'api',
                'form_id'       => $formIds[9],
                'action_id'     => '19',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // Line Classes
            [
                'name'          => 'show_all_line_classes',
                'guard_name'    => 'api',
                'form_id'       => $formIds[10],
                'action_id'     => '1',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'create_line_classes',
                'guard_name'    => 'api',
                'form_id'       => $formIds[10],
                'action_id'     => '2',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'show_single_line_classes',
                'guard_name'    => 'api',
                'form_id'       => $formIds[10],
                'action_id'     => '3',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_line_classes',
                'guard_name'    => 'api',
                'form_id'       => $formIds[10],
                'action_id'     => '4',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'delete_line_classes',
                'guard_name'    => 'api',
                'form_id'       => $formIds[10],
                'action_id'     => '5',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'restore_line_classes',
                'guard_name'    => 'api',
                'form_id'       => $formIds[10],
                'action_id'     => '6',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'show_all_archive_line_classes',
                'guard_name'    => 'api',
                'form_id'       => $formIds[10],
                'action_id'     => '7',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'destroy_line_classes',
                'guard_name'    => 'api',
                'form_id'       => $formIds[10],
                'action_id'     => '8',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],


            [
                'name'          => 'import_line_classes',
                'guard_name'    => 'api',
                'form_id'       => $formIds[10],
                'action_id'     => '10',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_xlsx_line_classes',
                'guard_name'    => 'api',
                'form_id'       => $formIds[10],
                'action_id'     => '11',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_csv_line_classes',
                'guard_name'    => 'api',
                'form_id'       => $formIds[10],
                'action_id'     => '22',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_pdf_line_classes',
                'guard_name'    => 'api',
                'form_id'       => $formIds[10],
                'action_id'     => '12',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_email_line_classes',
                'guard_name'    => 'api',
                'form_id'       => $formIds[10],
                'action_id'     => '13',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_view_line_classes',
                'guard_name'    => 'api',
                'form_id'       => $formIds[10],
                'action_id'     => '18',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_view_single_line_class',
                'guard_name'    => 'api',
                'form_id'       => $formIds[10],
                'action_id'     => '19',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // Line Bricks
            [
                'name'          => 'show_all_line_bricks',
                'guard_name'    => 'api',
                'form_id'       => $formIds[11],
                'action_id'     => '1',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            // line division brick

            [
                'name'          => 'show_all_line_division_bricks',
                'guard_name'    => 'api',
                'form_id'       => $formIds[11],
                'action_id'     => '1',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'create_line_bricks',
                'guard_name'    => 'api',
                'form_id'       => $formIds[11],
                'action_id'     => '2',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_single_line_bricks',
                'guard_name'    => 'api',
                'form_id'       => $formIds[11],
                'action_id'     => '3',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_line_bricks',
                'guard_name'    => 'api',
                'form_id'       => $formIds[11],
                'action_id'     => '4',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'delete_line_bricks',
                'guard_name'    => 'api',
                'form_id'       => $formIds[11],
                'action_id'     => '5',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'restore_line_bricks',
                'guard_name'    => 'api',
                'form_id'       => $formIds[11],
                'action_id'     => '6',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_all_archive_line_bricks',
                'guard_name'    => 'api',
                'form_id'       => $formIds[11],
                'action_id'     => '7',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'destroy_line_bricks',
                'guard_name'    => 'api',
                'form_id'       => $formIds[11],
                'action_id'     => '8',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'import_line_bricks',
                'guard_name'    => 'api',
                'form_id'       => $formIds[11],
                'action_id'     => '10',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_xlsx_line_bricks',
                'guard_name'    => 'api',
                'form_id'       => $formIds[11],
                'action_id'     => '11',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_csv_line_bricks',
                'guard_name'    => 'api',
                'form_id'       => $formIds[11],
                'action_id'     => '11',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_pdf_line_bricks',
                'guard_name'    => 'api',
                'form_id'       => $formIds[11],
                'action_id'     => '12',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_email_line_bricks',
                'guard_name'    => 'api',
                'form_id'       => $formIds[11],
                'action_id'     => '13',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_view_line_bricks',
                'guard_name'    => 'api',
                'form_id'       => $formIds[11],
                'action_id'     => '18',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_view_single_line_brick',
                'guard_name'    => 'api',
                'form_id'       => $formIds[11],
                'action_id'     => '19',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // Line Specialities
            [
                'name'          => 'show_all_line_specialities',
                'guard_name'    => 'api',
                'form_id'       => $formIds[12],
                'action_id'     => '1',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'create_line_specialities',
                'guard_name'    => 'api',
                'form_id'       => $formIds[12],
                'action_id'     => '2',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_single_line_specialities',
                'guard_name'    => 'api',
                'form_id'       => $formIds[12],
                'action_id'     => '3',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_line_specialities',
                'guard_name'    => 'api',
                'form_id'       => $formIds[12],
                'action_id'     => '4',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'delete_line_specialities',
                'guard_name'    => 'api',
                'form_id'       => $formIds[12],
                'action_id'     => '5',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'restore_line_specialities',
                'guard_name'    => 'api',
                'form_id'       => $formIds[12],
                'action_id'     => '6',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_all_archive_line_specialities',
                'guard_name'    => 'api',
                'form_id'       => $formIds[12],
                'action_id'     => '7',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'destroy_line_specialities',
                'guard_name'    => 'api',
                'form_id'       => $formIds[12],
                'action_id'     => '8',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'import_line_specialities',
                'guard_name'    => 'api',
                'form_id'       => $formIds[12],
                'action_id'     => '10',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_xlsx_line_specialities',
                'guard_name'    => 'api',
                'form_id'       => $formIds[12],
                'action_id'     => '11',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_csv_line_specialities',
                'guard_name'    => 'api',
                'form_id'       => $formIds[12],
                'action_id'     => '22',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_pdf_line_specialities',
                'guard_name'    => 'api',
                'form_id'       => $formIds[12],
                'action_id'     => '12',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_email_line_specialities',
                'guard_name'    => 'api',
                'form_id'       => $formIds[12],
                'action_id'     => '13',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_view_line_specialities',
                'guard_name'    => 'api',
                'form_id'       => $formIds[12],
                'action_id'     => '18',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_view_single_line_speciality',
                'guard_name'    => 'api',
                'form_id'       => $formIds[12],
                'action_id'     => '19',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // Line Giveaways
            [
                'name'          => 'show_all_line_giveaways',
                'guard_name'    => 'api',
                'form_id'       => $formIds[13],
                'action_id'     => '1',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'create_line_giveaways',
                'guard_name'    => 'api',
                'form_id'       => $formIds[13],
                'action_id'     => '2',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'show_single_line_giveaways',
                'guard_name'    => 'api',
                'form_id'       => $formIds[13],
                'action_id'     => '3',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_line_giveaways',
                'guard_name'    => 'api',
                'form_id'       => $formIds[13],
                'action_id'     => '4',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'delete_line_giveaways',
                'guard_name'    => 'api',
                'form_id'       => $formIds[13],
                'action_id'     => '5',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'destroy_line_giveaways',
                'guard_name'    => 'api',
                'form_id'       => $formIds[13],
                'action_id'     => '8',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'restore_line_giveaways',
                'guard_name'    => 'api',
                'form_id'       => $formIds[13],
                'action_id'     => '6',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_view_line_giveaways',
                'guard_name'    => 'api',
                'form_id'       => $formIds[13],
                'action_id'     => '18',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],

        ];

        foreach ($permissions as $value) {
            Permission::insert($value);
        }
    }
}
