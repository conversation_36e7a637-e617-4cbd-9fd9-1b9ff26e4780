<?php

namespace Database\Seeders\Permissions;

use App\Form;
use App\Module;
use App\Permission;
use Illuminate\Database\Seeder;

class FrequencySeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // frequencies

        resetPermissionModule("frequencies");
        $module = Module::firstOrCreate([
            "module" => "frequencies",
            "icon" => "cil-calculator",
        ]);

        $forms = [
            [
                'form' => 'call_rate'
            ],
            [
                'form' => 'class_frequencies'
            ],
            [
                'form' => 'specialities_frequency'
            ],
            [
                'form' => 'doctor_frequency'
            ],
            [
                'form' => 'general_other_setting'
            ],
        ];

        $formIds = [];
        foreach ($forms as $value) {
            $form = Form::firstOrCreate($value);
            array_push($formIds, $form->id);
        }

        $permissions = [
            // Call Rate
            [
                'name'          => 'show_all_call_rates',
                'guard_name'    => 'api',
                'form_id'       => $formIds[0],
                'action_id'     => '1',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'create_call_rates',
                'guard_name'    => 'api',
                'form_id'       => $formIds[0],
                'action_id'     => '2',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_single_call_rates',
                'guard_name'    => 'api',
                'form_id'       => $formIds[0],
                'action_id'     => '3',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_call_rates',
                'guard_name'    => 'api',
                'form_id'       => $formIds[0],
                'action_id'     => '4',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'delete_call_rates',
                'guard_name'    => 'api',
                'form_id'       => $formIds[0],
                'action_id'     => '5',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'destroy_call_rates',
                'guard_name'    => 'api',
                'form_id'       => $formIds[0],
                'action_id'     => '8',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            [
                'name'          => 'import_call_rates',
                'guard_name'    => 'api',
                'form_id'       => $formIds[0],
                'action_id'     => '10',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            [
                'name'          => 'restore_call_rates',
                'guard_name'    => 'api',
                'form_id'       => $formIds[0],
                'action_id'     => '6',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_view_call_rates',
                'guard_name'    => 'api',
                'form_id'       => $formIds[0],
                'action_id'     => '18',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_view_single_call_rates',
                'guard_name'    => 'api',
                'form_id'       => $formIds[0],
                'action_id'     => '19',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // Class Frequencies
            [
                'name'          => 'show_all_class_frequencies',
                'guard_name'    => 'api',
                'form_id'       => $formIds[1],
                'action_id'     => '1',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'create_class_frequencies',
                'guard_name'    => 'api',
                'form_id'       => $formIds[1],
                'action_id'     => '2',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_single_class_frequencies',
                'guard_name'    => 'api',
                'form_id'       => $formIds[1],
                'action_id'     => '3',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_class_frequencies',
                'guard_name'    => 'api',
                'form_id'       => $formIds[1],
                'action_id'     => '4',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'delete_class_frequencies',
                'guard_name'    => 'api',
                'form_id'       => $formIds[1],
                'action_id'     => '5',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'destroy_class_frequencies',
                'guard_name'    => 'api',
                'form_id'       => $formIds[1],
                'action_id'     => '6',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'restore_class_frequencies',
                'guard_name'    => 'api',
                'form_id'       => $formIds[1],
                'action_id'     => '7',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_view_class_frequencies',
                'guard_name'    => 'api',
                'form_id'       => $formIds[1],
                'action_id'     => '8',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_view_single_class_frequencies',
                'guard_name'    => 'api',
                'form_id'       => $formIds[1],
                'action_id'     => '9',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'import_class_frequencies',
                'guard_name'    => 'api',
                'form_id'       => $formIds[1],
                'action_id'     => '10',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_xlsx_class_frequencies',
                'guard_name'    => 'api',
                'form_id'       => $formIds[1],
                'action_id'     => '11',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_csv_class_frequencies',
                'guard_name'    => 'api',
                'form_id'       => $formIds[1],
                'action_id'     => '12',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_pdf_class_frequencies',
                'guard_name'    => 'api',
                'form_id'       => $formIds[1],
                'action_id'     => '13',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_email_class_frequencies',
                'guard_name'    => 'api',
                'form_id'       => $formIds[1],
                'action_id'     => '14',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], 


            // Speciality Frequency

            [
                'name'          => 'show_all_speciality_frequencies',
                'guard_name'    => 'api',
                'form_id'       => $formIds[2],
                'action_id'     => '1',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'create_speciality_frequencies',
                'guard_name'    => 'api',
                'form_id'       => $formIds[2],
                'action_id'     => '2',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_single_speciality_frequencies',
                'guard_name'    => 'api',
                'form_id'       => $formIds[2],
                'action_id'     => '3',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_speciality_frequencies',
                'guard_name'    => 'api',
                'form_id'       => $formIds[2],
                'action_id'     => '4',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'delete_speciality_frequencies',
                'guard_name'    => 'api',
                'form_id'       => $formIds[2],
                'action_id'     => '5',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'destroy_speciality_frequencies',
                'guard_name'    => 'api',
                'form_id'       => $formIds[2],
                'action_id'     => '8',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'restore_speciality_frequencies',
                'guard_name'    => 'api',
                'form_id'       => $formIds[2],
                'action_id'     => '6',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_view_speciality_frequencies',
                'guard_name'    => 'api',
                'form_id'       => $formIds[2],
                'action_id'     => '18',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_view_single_speciality_frequencies',
                'guard_name'    => 'api',
                'form_id'       => $formIds[2],
                'action_id'     => '19',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // Doctor Frequency

            [
                'name'          => 'show_all_doctor_frequencies',
                'guard_name'    => 'api',
                'form_id'       => $formIds[3],
                'action_id'     => '1',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [ 
                'name'          => 'create_doctor_frequencies',
                'guard_name'    => 'api',
                'form_id'       => $formIds[3],
                'action_id'     => '2',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_single_doctor_frequencies',
                'guard_name'    => 'api',
                'form_id'       => $formIds[3],
                'action_id'     => '3',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_doctor_frequencies',
                'guard_name'    => 'api',
                'form_id'       => $formIds[3],
                'action_id'     => '4',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'delete_doctor_frequencies',
                'guard_name'    => 'api',
                'form_id'       => $formIds[3],
                'action_id'     => '5',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'destroy_doctor_frequencies',
                'guard_name'    => 'api',
                'form_id'       => $formIds[3],
                'action_id'     => '8',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'restore_doctor_frequencies',
                'guard_name'    => 'api',
                'form_id'       => $formIds[3],
                'action_id'     => '6',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'import_doctor_frequencies',
                'guard_name'    => 'api',
                'form_id'       => $formIds[3],
                'action_id'     => '10',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_xlsx_doctor_frequencies',
                'guard_name'    => 'api',
                'form_id'       => $formIds[3],
                'action_id'     => '11',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_csv_doctor_frequencies',
                'guard_name'    => 'api',
                'form_id'       => $formIds[3],
                'action_id'     => '22',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_pdf_doctor_frequencies',
                'guard_name'    => 'api',
                'form_id'       => $formIds[3],
                'action_id'     => '12',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_email_doctor_frequencies',
                'guard_name'    => 'api',
                'form_id'       => $formIds[3],
                'action_id'     => '13',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], 
            [
                'name'          => 'edit_view_doctor_frequencies',
                'guard_name'    => 'api',
                'form_id'       => $formIds[3],
                'action_id'     => '18',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_view_single_doctor_frequencies',
                'guard_name'    => 'api',
                'form_id'       => $formIds[3],
                'action_id'     => '19',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // Other Setting Frequency

            [
                'name'          => 'show_all_other_settings',
                'guard_name'    => 'api',
                'form_id'       => $formIds[4],
                'action_id'     => '1',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_single_other_settings',
                'guard_name'    => 'api',
                'form_id'       => $formIds[4],
                'action_id'     => '3',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_other_settings',
                'guard_name'    => 'api',
                'form_id'       => $formIds[4],
                'action_id'     => '4',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],

        ];

        foreach ($permissions as $value) {
            Permission::insert($value);
        }
    }
}
