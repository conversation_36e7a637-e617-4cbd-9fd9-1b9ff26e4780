<?php

namespace Database\Seeders\Permissions;

use App\Form;
use App\Module;
use App\Permission;
use Illuminate\Database\Seeder;

class OtherSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Others

        resetPermissionModule("Others");
        $module = Module::firstOrCreate([
            "module" => "Others",
            "icon" => "cil-room",
        ]);

        $forms = [

            [
                'form' => 'permissions'
            ],
            [
                'form' => 'roles'
            ],

            [
                'form' => 'log_activities'
            ],
            [
                'form' => 'imports'
            ],
            [
                'form' => 'supports'
            ],
            [
                'form' => 'mails'
            ],
            [
                'form' => 'files'
            ]
        ];

        $formIds = [];
        foreach ($forms as $value) {
            $form = Form::firstOrCreate($value);
            array_push($formIds, $form->id);
        }

        $permissions = [
            // Permissions
            [
                'name'          => 'show_all_permissions',
                'guard_name'    => 'api',
                'form_id'       => $formIds[0],
                'action_id'     => '1',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'create_permissions',
                'guard_name'    => 'api',
                'form_id'       => $formIds[0],
                'action_id'     => '2',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_single_permissions',
                'guard_name'    => 'api',
                'form_id'       => $formIds[0],
                'action_id'     => '3',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_permissions',
                'guard_name'    => 'api',
                'form_id'       => $formIds[0],
                'action_id'     => '4',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'delete_permissions',
                'guard_name'    => 'api',
                'form_id'       => $formIds[0],
                'action_id'     => '5',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'restore_permissions',
                'guard_name'    => 'api',
                'form_id'       => $formIds[0],
                'action_id'     => '6',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_all_archive_permissions',
                'guard_name'    => 'api',
                'form_id'       => $formIds[0],
                'action_id'     => '7',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'import_permissions',
                'guard_name'    => 'api',
                'form_id'       => $formIds[0],
                'action_id'     => '10',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_xlsx_permissions',
                'guard_name'    => 'api',
                'form_id'       => $formIds[0],
                'action_id'     => '11',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_csv_permissions',
                'guard_name'    => 'api',
                'form_id'       => $formIds[0],
                'action_id'     => '22',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_pdf_permissions',
                'guard_name'    => 'api',
                'form_id'       => $formIds[0],
                'action_id'     => '12',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_email_permissions',
                'guard_name'    => 'api',
                'form_id'       => $formIds[0],
                'action_id'     => '13',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            // [
            //     'name'          => 'edit_view_permission',
            //     'guard_name'    => 'api',
            //     'form_id'       => $formIds[1],
            //     'action_id'     => '18',
            //     'module_id'     => null,
            //     'created_at'    => now(),
            //     'updated_at'    => now()
            // ],

            // Roles
            [
                'name'          => 'show_all_roles',
                'guard_name'    => 'api',
                'form_id'       => $formIds[1],
                'action_id'     => '1',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'create_roles',
                'guard_name'    => 'api',
                'form_id'       => $formIds[1],
                'action_id'     => '2',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_single_roles',
                'guard_name'    => 'api',
                'form_id'       => $formIds[1],
                'action_id'     => '3',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            [
                'name'          => 'edit_roles',
                'guard_name'    => 'api',
                'form_id'       => $formIds[1],
                'action_id'     => '4',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'assign_role_permissions',
                'guard_name'    => 'api',
                'form_id'       => $formIds[1],
                'action_id'     => '2',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'delete_roles',
                'guard_name'    => 'api',
                'form_id'       => $formIds[1],
                'action_id'     => '5',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'restore_roles',
                'guard_name'    => 'api',
                'form_id'       => $formIds[1],
                'action_id'     => '6',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_all_archive_roles',
                'guard_name'    => 'api',
                'form_id'       => $formIds[1],
                'action_id'     => '7',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'import_roles',
                'guard_name'    => 'api',
                'form_id'       => $formIds[1],
                'action_id'     => '10',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_xlsx_roles',
                'guard_name'    => 'api',
                'form_id'       => $formIds[1],
                'action_id'     => '11',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_csv_roles',
                'guard_name'    => 'api',
                'form_id'       => $formIds[1],
                'action_id'     => '22',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_pdf_roles',
                'guard_name'    => 'api',
                'form_id'       => $formIds[1],
                'action_id'     => '12',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_email_roles',
                'guard_name'    => 'api',
                'form_id'       => $formIds[1],
                'action_id'     => '13',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // [
            //     'name'          => 'edit_view_role',
            //     'guard_name'    => 'api',
            //     'form_id'       => $formIds[2],
            //     'action_id'     => '18',
            //     'module_id'     => null,
            //     'created_at'    => now(),
            //     'updated_at'    => now()
            // ],

            // // Modules
            // [
            //     'name'          => 'show_all_modules',
            //     'guard_name'    => 'api',
            //     'form_id'       => $formIds[4],
            //     'action_id'     => '1',
            //     'module_id'     => $module->id,
            //     'created_at'    => now(),
            //     'updated_at'    => now()
            // ], [
            //     'name'          => 'create_modules',
            //     'guard_name'    => 'api',
            //     'form_id'       => $formIds[4],
            //     'action_id'     => '2',
            //     'module_id'     => $module->id,
            //     'created_at'    => now(),
            //     'updated_at'    => now()
            // ], [
            //     'name'          => 'show_single_modules',
            //     'guard_name'    => 'api',
            //     'form_id'       => $formIds[4],
            //     'action_id'     => '3',
            //     'module_id'     => $module->id,
            //     'created_at'    => now(),
            //     'updated_at'    => now()
            // ], [
            //     'name'          => 'edit_modules',
            //     'guard_name'    => 'api',
            //     'form_id'       => $formIds[4],
            //     'action_id'     => '4',
            //     'module_id'     => $module->id,
            //     'created_at'    => now(),
            //     'updated_at'    => now()
            // ], [
            //     'name'          => 'delete_modules',
            //     'guard_name'    => 'api',
            //     'form_id'       => $formIds[4],
            //     'action_id'     => '5',
            //     'module_id'     => $module->id,
            //     'created_at'    => now(),
            //     'updated_at'    => now()
            // ], [
            //     'name'          => 'restore_modules',
            //     'guard_name'    => 'api',
            //     'form_id'       => $formIds[4],
            //     'action_id'     => '6',
            //     'module_id'     => $module->id,
            //     'created_at'    => now(),
            //     'updated_at'    => now()
            // ], [
            //     'name'          => 'show_all_archive_modules',
            //     'guard_name'    => 'api',
            //     'form_id'       => $formIds[4],
            //     'action_id'     => '7',
            //     'module_id'     => $module->id,
            //     'created_at'    => now(),
            //     'updated_at'    => now()
            // ], [
            //     'name'          => 'destroy_modules',
            //     'guard_name'    => 'api',
            //     'form_id'       => $formIds[4],
            //     'action_id'     => '8',
            //     'module_id'     => $module->id,
            //     'created_at'    => now(),
            //     'updated_at'    => now()
            // ],
            // [
            //     'name'          => 'import_modules',
            //     'guard_name'    => 'api',
            //     'form_id'       => $formIds[4],
            //     'action_id'     => '10',
            //     'module_id'     => $module->id,
            //     'created_at'    => now(),
            //     'updated_at'    => now()
            // ], [
            //     'name'          => 'export_xlsx_modules',
            //     'guard_name'    => 'api',
            //     'form_id'       => $formIds[4],
            //     'action_id'     => '11',
            //     'module_id'     => $module->id,
            //     'created_at'    => now(),
            //     'updated_at'    => now()
            // ], [
            //     'name'          => 'export_csv_modules',
            //     'guard_name'    => 'api',
            //     'form_id'       => $formIds[4],
            //     'action_id'     => '22',
            //     'module_id'     => $module->id,
            //     'created_at'    => now(),
            //     'updated_at'    => now()
            // ], [
            //     'name'          => 'export_pdf_modules',
            //     'guard_name'    => 'api',
            //     'form_id'       => $formIds[4],
            //     'action_id'     => '12',
            //     'module_id'     => $module->id,
            //     'created_at'    => now(),
            //     'updated_at'    => now()
            // ], [
            //     'name'          => 'export_email_modules',
            //     'guard_name'    => 'api',
            //     'form_id'       => $formIds[4],
            //     'action_id'     => '13',
            //     'module_id'     => $module->id,
            //     'created_at'    => now(),
            //     'updated_at'    => now()
            // ], [
            //     'name'          => 'edit_view_module',
            //     'guard_name'    => 'api',
            //     'form_id'       => $formIds[4],
            //     'action_id'     => '18',
            //     'module_id'     => $module->id,
            //     'created_at'    => now(),
            //     'updated_at'    => now()
            // ],

            // Log Activity
            [
                'name'          => 'show_all_log_activities',
                'guard_name'    => 'api',
                'form_id'       => $formIds[2],
                'action_id'     => '1',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'create_log_activities',
                'guard_name'    => 'api',
                'form_id'       => $formIds[2],
                'action_id'     => '2',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_single_log_activities',
                'guard_name'    => 'api',
                'form_id'       => $formIds[2],
                'action_id'     => '3',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_log_activities',
                'guard_name'    => '88',
                'form_id'       => $formIds[2],
                'action_id'     => '4',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'delete_log_activities',
                'guard_name'    => 'api',
                'form_id'       => $formIds[2],
                'action_id'     => '5',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'restore_log_activities',
                'guard_name'    => 'api',
                'form_id'       => $formIds[2],
                'action_id'     => '6',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_all_archive_log_activities',
                'guard_name'    => 'api',
                'form_id'       => $formIds[2],
                'action_id'     => '7',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'destroy_log_activities',
                'guard_name'    => 'api',
                'form_id'       => $formIds[2],
                'action_id'     => '8',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'import_log_activities',
                'guard_name'    => 'api',
                'form_id'       => $formIds[2],
                'action_id'     => '10',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_xlsx_log_activities',
                'guard_name'    => 'api',
                'form_id'       => $formIds[2],
                'action_id'     => '11',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_csv_log_activities',
                'guard_name'    => 'api',
                'form_id'       => $formIds[2],
                'action_id'     => '22',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_pdf_log_activities',
                'guard_name'    => 'api',
                'form_id'       => $formIds[2],
                'action_id'     => '12',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_email_log_activities',
                'guard_name'    => 'api',
                'form_id'       => $formIds[2],
                'action_id'     => '13',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_view_log_activity',
                'guard_name'    => 'api',
                'form_id'       => $formIds[2],
                'action_id'     => '18',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // Files Imported
            [
                'name'          => 'show_all_imports',
                'guard_name'    => 'api',
                'form_id'       => $formIds[3],
                'action_id'     => '1',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'show_all_archived_imports',
                'guard_name'    => 'api',
                'form_id'       => $formIds[3],
                'action_id'     => '7',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'delete_imports',
                'guard_name'    => 'api',
                'form_id'       => $formIds[3],
                'action_id'     => '5',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'restore_imports',
                'guard_name'    => 'api',
                'form_id'       => $formIds[3],
                'action_id'     => '6',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'download_imports',
                'guard_name'    => 'api',
                'form_id'       => $formIds[3],
                'action_id'     => '20',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],


            // Support
            [
                'name'          => 'show_all_supports',
                'guard_name'    => 'api',
                'form_id'       => $formIds[4],
                'action_id'     => '1',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'create_supports',
                'guard_name'    => 'api',
                'form_id'       => $formIds[4],
                'action_id'     => '2',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_single_supports',
                'guard_name'    => 'api',
                'form_id'       => $formIds[4],
                'action_id'     => '3',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_supports',
                'guard_name'    => 'api',
                'form_id'       => $formIds[4],
                'action_id'     => '4',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'delete_supports',
                'guard_name'    => 'api',
                'form_id'       => $formIds[4],
                'action_id'     => '5',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'restore_supports',
                'guard_name'    => 'api',
                'form_id'       => $formIds[4],
                'action_id'     => '6',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_all_archive_supports',
                'guard_name'    => 'api',
                'form_id'       => $formIds[4],
                'action_id'     => '7',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],  [
                'name'          => 'import_supports',
                'guard_name'    => 'api',
                'form_id'       => $formIds[4],
                'action_id'     => '10',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_xlsx_supports',
                'guard_name'    => 'api',
                'form_id'       => $formIds[4],
                'action_id'     => '11',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_csv_supports',
                'guard_name'    => 'api',
                'form_id'       => $formIds[4],
                'action_id'     => '22',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_pdf_supports',
                'guard_name'    => 'api',
                'form_id'       => $formIds[4],
                'action_id'     => '12',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_email_supports',
                'guard_name'    => 'api',
                'form_id'       => $formIds[4],
                'action_id'     => '13',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_view_supports',
                'guard_name'    => 'api',
                'form_id'       => $formIds[4],
                'action_id'     => '18',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // Mail Sender
            [
                'name'          => 'show_all_mails',
                'guard_name'    => 'api',
                'form_id'       => $formIds[5],
                'action_id'     => '1',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'create_mails',
                'guard_name'    => 'api',
                'form_id'       => $formIds[5],
                'action_id'     => '2',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_single_mails',
                'guard_name'    => 'api',
                'form_id'       => $formIds[5],
                'action_id'     => '3',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'edit_mails',
                'guard_name'    => 'api',
                'form_id'       => $formIds[5],
                'action_id'     => '4',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'delete_mails',
                'guard_name'    => 'api',
                'form_id'       => $formIds[5],
                'action_id'     => '5',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'restore_mails',
                'guard_name'    => 'api',
                'form_id'       => $formIds[5],
                'action_id'     => '6',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'show_all_archive_mails',
                'guard_name'    => 'api',
                'form_id'       => $formIds[5],
                'action_id'     => '7',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],  [
                'name'          => 'import_mails',
                'guard_name'    => 'api',
                'form_id'       => $formIds[5],
                'action_id'     => '10',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_xlsx_mails',
                'guard_name'    => 'api',
                'form_id'       => $formIds[5],
                'action_id'     => '11',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_csv_mails',
                'guard_name'    => 'api',
                'form_id'       => $formIds[5],
                'action_id'     => '22',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_pdf_mails',
                'guard_name'    => 'api',
                'form_id'       => $formIds[5],
                'action_id'     => '12',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ], [
                'name'          => 'export_email_mails',
                'guard_name'    => 'api',
                'form_id'       => $formIds[5],
                'action_id'     => '13',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_view_mails',
                'guard_name'    => 'api',
                'form_id'       => $formIds[5],
                'action_id'     => '18',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],


            // // Download Templates

            [
                'name'          => 'download_all_templates',
                'guard_name'    => 'api',
                'form_id'       => $formIds[6],
                'action_id'     => '9',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_files',
                'guard_name'    => 'api',
                'form_id'       => $formIds[6],
                'action_id'     => '11',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'import_bulk_edit',
                'guard_name'    => 'api',
                'form_id'       => $formIds[6],
                'action_id'     => '10',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],


        ];

        foreach ($permissions as $value) {
            Permission::insert($value);
        }
    }
}
