<?php

namespace Database\Seeders;

use App\Models\PV\PV;
use App\Models\PV\PvModule;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Schema;

class PVTypeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $pv_modules =
            [
                [
                    'id' => 1,
                    'name' => 'PV',
                    'notes' => 'PV',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'id' => 2,
                    'name' => 'Quality',
                    'notes' => 'Quality',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
            ];

        Schema::disableForeignKeyConstraints();
        PvModule::truncate();
        Schema::enableForeignKeyConstraints();

        $chunked_pv_modules = array_chunk($pv_modules, 5);


        foreach ($chunked_pv_modules as $value) {
            PvModule::insert($value);
        }
    }
}
