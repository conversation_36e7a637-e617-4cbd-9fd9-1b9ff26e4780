<?php

namespace Database\Seeders;

use App\Models\AccountClassification;
use App\Models\FrequencyType;
use App\Models\StartExpenseMonth;
use Database\Seeders\Permissions\Index;
use Illuminate\Database\Seeder;
//use database\seeds\NotesTableSeeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     *
     * @return void
     */
    public function run()
    {

        $this->call(CompanySettingSeeder::class);
        // $this->call(ModuleSeeder::class);
        // $this->call(FormSeeder::class);
        // $this->call(PermissionSeeder::class);
        $this->call(Index::class);
        $this->call(UsersAndNotesSeeder::class);
        $this->call(MenusTableSeeder::class);
        $this->call(FolderTableSeeder::class);
        $this->call(BREADSeeder::class);
        $this->call(EmailSeeder::class);
        $this->call(ActionSeeder::class);
        $this->call(FrequencyTypeSeeder::class);
        $this->call(OtherSettingSeeder::class);
        $this->call(SettingSeeder::class);
        $this->call(CountrySeeder::class);
        $this->call(CurrencySeeder::class);
        $this->call(PlanSettingSeeder::class);
        $this->call(QuizQuestionLevelSeeder::class);
        $this->call(KpisSeeder::class);
        $this->call(BudgetSettingSeeder::class);
        $this->call(PaymentMethodsSeeder::class);
        $this->call(CommercialTabsSeeder::class);
        $this->call(AccountClassificationSeeder::class);
        $this->call(OverNightSeeder::class);
        $this->call(ExpenseDistanceSeeder::class);


        //Create Demo Data - Comment or replace data for real application deployment
        $this->call(DemoDataSeeder::class);

        //Create ErrorMessagesSeeder
        $this->call(ErrorMessagesSeeder::class);

        //Create Fake Bulk data for products
        // $this->call(ProductsSeeder::class);

        $this->call(StartPlanDaysSeeder::class);
        $this->call(ActualVisitSettingSeeder::class);
        $this->call(AvRequiredInputSeeder::class);
        $this->call(VisitTypeSeeder::class);
        $this->call(PlanVisitColumnSeeder::class);
        $this->call(VisitFeedbacksSeeder::class);
        $this->call(SalesSettingSeeder::class);
        $this->call(SalesTypesSeeder::class);
        $this->call(TargetTypesSeeder::class);
        $this->call(BudgetTypesSeeder::class);
        $this->call(WidgetSettingSeeder::class);
        $this->call(CostSeeder::class);
        $this->call(ListTypeSeeder::class);
        $this->call(DisapprovalReasonsSeeder::class);
        $this->call(LevelsSeeder::class);
        $this->call(PersonalityTypesSeeder::class);
        $this->call(SpeakerTypesSeeder::class);
        $this->call(ItGatesSeeder::class);
        $this->call(SupportTypesSeeder::class);
        $this->call(ShiftSeeder::class);
        $this->call(DashboardSettingSeeder::class);
        $this->call(ApprovalSettingSeeder::class);
        $this->call(NotificationCenterSeeder::class);
        $this->call(SaleFactorSeeder::class);
        $this->call(UnplannedVisitNumberSeeder::class);
        $this->call(MealSeeder::class);
        $this->call(DoubleVisitTypesSeeder::class);
        $this->call(ExpenseSettingSeeder::class);
        $this->call(StartExpenseMonthSeeder::class);
        $this->call(ListSettingSeeder::class);
        $this->call(CoachingSettingSeeder::class);
        $this->call(QuizSettingSeeder::class);
    }
}
