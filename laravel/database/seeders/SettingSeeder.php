<?php

namespace Database\Seeders;

use App\Setting;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Schema;

class SettingSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $settings =
            [
                [
                    'name' => 'Session Timeout',
                    'key' => 'session_timeout',
                    'value' => '5256000', // 1 * (60 * 24 * 365), // 1 year
                    'type' => 'number',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'name' => 'Gemstone Font Size',
                    'key' => 'gemstone_font_size',
                    'value' => '0.9rem',
                    'type' => 'select',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'name' => 'PDF Logo',
                    'key' => 'pdf_logo',
                    'value' => storage_path('app/public/pulpopharma.png'),
                    'type' => 'text',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'name' => 'PDF Header',
                    'key' => 'pdf_header',
                    'value' => 'Pulpo Pharma',
                    'type' => 'text',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'name' => 'PDF Footer',
                    'key' => 'pdf_footer',
                    'value' => 'Pulpo Pharma',
                    'type' => 'text',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'name' => 'Mail Driver',
                    'key' => 'mail.driver',
                    'value' => 'smtp',
                    'type' => 'text',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'name' => 'Mail Host',
                    'key' => 'mail.host',
                    'value' => 'zaiedcool.netfirms.com',
                    'type' => 'text',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'name' => 'Mail Port',
                    'key' => 'mail.port',
                    'value' => '587',
                    'type' => 'number',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'name' => 'Mail Username',
                    'key' => 'mail.username',
                    'value' => '<EMAIL>',
                    'type' => 'email',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'name' => 'Mail Password',
                    'key' => 'mail.password',
                    'value' => 'Itgates_0106599850',
                    'type' => 'text',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'name' => 'Mail Encryption',
                    'key' => 'mail.encryption',
                    'value' => 'tls',
                    'type' => 'text',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'name' => 'Mail From Address',
                    'key' => 'mail.from.address',
                    'value' => '<EMAIL>',
                    'type' => 'email',
                    'created_at' => now(),
                    'updated_at' => now()
                ],

                [
                    'name' => 'Mail From Name',
                    'key' => 'mail.from.name',
                    'value' => 'Pulpo-Pharma',
                    'type' => 'text',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'name' => 'Mail Title Prefix',
                    'key' => 'mail.title.prefix',
                    'value' => 'Pulpo-Pharma',
                    'type' => 'text',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'name' => 'Allowed Attachment Extentions',
                    'key' => 'allowed_attchment_extentions',
                    'value' => 'xlsx,xls,pdf,jpg,png',
                    'type' => 'text',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'name' => 'Allow Multiple Sessions',
                    'key' => 'allow_multiple_sessions',
                    'value' => 'true',
                    'type' => 'select',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'name' => 'Date Columns Format',
                    'key' => 'date_format',
                    //for format options https://momentjs.com/docs/#/displaying/format/
                    // 'value' => '["DD/MM/YYYY","D/M/Y", "MM/DD/YYYY","M/D/Y","DD-MM-YYYY","D-M-Y","MM-DD-YYYY","M-D-Y"]',
                    'value' => 'DD/MM/YYYY',
                    'type' => 'select',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'name' => ' Google Maps Key',
                    'key' => 'gmaps_key',
                    'value' => 'AIzaSyC0gF0oaizu8CnLJ1Mz8azBGiXl-Q3JaL4',
                    'type' => 'text',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'name' => 'Location Google Maps',
                    'key' => 'location_gmaps',
                    'value' => 'yes',
                    'type' => 'select',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'name' => 'TWILIO ACCOUNT SID',
                    'key' => 'TWILIO_ACCOUNT_SID',
                    'value' => '**********************************',
                    'type' => 'text',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'name' => 'TWILIO AUTH TOKEN',
                    'key' => 'TWILIO_AUTH_TOKEN',
                    'value' => 'ec4a5c0852e5840df72bf37c7c16ea1c',
                    'type' => 'text',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'name' => 'TWILIO SMS SERVICE TOKEN',
                    'key' => 'TWILIO_SMS_SERVICE_TOKEN',
                    'value' => 'MG8a3b8692f8c9c666dac193c1321a3324',
                    'type' => 'text',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'name' => 'TWILIO WHATSAPP FROM',
                    'key' => 'TWILIO_WHATSAPP_FROM',
                    'value' => '+14155238886',
                    'type' => 'number',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'name' => 'App Logout Button',
                    'key' => 'app_logout',
                    'value' => 'yes',
                    'type' => 'select',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'name' => 'App Checkin and Checkout',
                    'key' => 'check_inout',
                    'value' => '0',
                    'type' => 'number',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'name' => 'Product With Line Or Division',
                    'key' => 'product_with_line_or_division',
                    'value' => 'Line',
                    'type' => 'select',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'name' => 'Working Days',
                    'key' => 'fixed_working_days',
                    'value' => '0',
                    'type' => 'number',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'name' => 'Reports Level',
                    'key' => 'reports_level',
                    'value' => 'Product',
                    'type' => 'select',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'name' => 'Authorize Position Show Data at Same Level',
                    'key' => 'authorize_position_show_data',
                    'value' => 'No',
                    'type' => 'select',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'name' => 'Required Notes',
                    'key' => 'required_notes',
                    'value' => 'No',
                    'type' => 'select',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'name' => 'Notification By Mail',
                    'key' => 'notification_by_mail',
                    'value' => 'No',
                    'type' => 'select',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'name' => 'Message By Mail',
                    'key' => 'message_by_mail',
                    'value' => 'No',
                    'type' => 'select',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'name' => 'App Log Timer',
                    'key' => 'app_log_timer',
                    'value' => '300',
                    'type' => 'number',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'name' => 'check_inout',
                    'key' => 'check_inout',
                    'value' => '0',
                    'type' => 'number',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
            ];

        Schema::disableForeignKeyConstraints();
        Setting::truncate();
        Schema::enableForeignKeyConstraints();

        $chunked_settings = array_chunk($settings, 20);


        foreach ($chunked_settings as $value) {
            Setting::insert($value);
        }
    }
}
