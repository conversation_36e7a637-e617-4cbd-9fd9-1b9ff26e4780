<?php

namespace Database\Seeders;

use App\Models\WidgetType;
use App\Widget;
use App\WidgetModule;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Schema;

class WidgetSettingSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        Schema::disableForeignKeyConstraints();
        WidgetType::truncate();
        WidgetModule::truncate();
        Widget::truncate();
        Schema::enableForeignKeyConstraints();
        $this->call(WidgetTypeSeeder::class);
        $this->call(WidgetModuleSeeder::class);
        $this->call(WidgetSeeder::class);
    }
}
