<?php

namespace Database\Seeders;

use App\Level;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Schema;

class LevelsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $levels =
            [
                [
                    'name' => 'Consultant',
                    'sort' => 100
                ],
                [
                    'name' => 'Generalist',
                    'sort' => 200
                ],
                [
                    'name' => 'Professor',
                    'sort' => 300
                ],
                [
                    'name' => 'Specialist',
                    'sort' => 400
                ],
            ];

        Schema::disableForeignKeyConstraints();
        Level::truncate();
        Schema::enableForeignKeyConstraints();

        $chunked_levels = array_chunk($levels, 5);


        foreach ($chunked_levels as $value) {
            Level::insert($value);
        }
    }
}
