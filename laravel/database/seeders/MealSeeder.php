<?php

namespace Database\Seeders;

use App\Models\Meal;
use App\Shift;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Schema;

class MealSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $meals =
            [
                [
                    'name' => 'Half Meal',
                    'price' => 100,
                    'sort' => 100
                ],
                [
                    'name' => 'Full Meal',
                    'price' => 200,
                    'sort' => 200
                ],
            ];

        Schema::disableForeignKeyConstraints();
        Meal::truncate();
        Schema::enableForeignKeyConstraints();

        $chunked_meals = array_chunk($meals, 5);


        foreach ($chunked_meals as $value) {
            Meal::insert($value);
        }
    }
}
