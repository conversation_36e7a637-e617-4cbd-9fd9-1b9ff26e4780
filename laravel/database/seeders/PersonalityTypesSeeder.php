<?php

namespace Database\Seeders;

use App\PersonalityType;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Schema;

class PersonalityTypesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $types =
            [
                [
                    'name' => 'Amiable',
                    'sort' => 100
                ],
                [
                    'name' => 'Driver',
                    'sort' => 200
                ],
            ];

        Schema::disableForeignKeyConstraints();
        PersonalityType::truncate();
        Schema::enableForeignKeyConstraints();

        $chunked_types = array_chunk($types, 5);


        foreach ($chunked_types as $value) {
            PersonalityType::insert($value);
        }
    }
}
