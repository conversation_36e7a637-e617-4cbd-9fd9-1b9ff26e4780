<?php

namespace Database\Seeders;

use App\WidgetModule;
use Illuminate\Database\Seeder;

class WidgetModuleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $widgetModules = [
            [
                'id' => 1,
                'name' => 'Visits',
                'icon' => 'cil-columns',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id' => 2,
                'name' => 'Sales',
                'icon' => 'cil-money',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id' => 3,
                'name' => 'Communications',
                'icon' => 'cil-phone',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id' => 4,
                'name' => 'Trainning',
                'icon' => 'cil-list',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id' => 5,
                'name' => 'Requests',
                'icon' => 'cil-color-border',
                'created_at' => now(),
                'updated_at' => now()
            ],
        ];

        $widgetModulesChunked = array_chunk($widgetModules, 2);

        foreach ($widgetModulesChunked as  $value) {
            WidgetModule::insert($value);
        }
    }
}
