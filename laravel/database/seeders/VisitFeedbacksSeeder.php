<?php

namespace Database\Seeders;

use App\VisitFeedbacks;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Schema;

class VisitFeedbacksSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $feedbacks = [
            [
                'notes' => 'Un-aware',
                'sort'  => 100,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'notes' => 'Aware',
                'sort'  => 200,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'notes' => 'Testing',
                'sort'  => 300,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'notes' => 'Prescribing',
                'sort'  => 400,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'notes' => 'Advocate',
                'sort'  => 500,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
        ];
        Schema::disableForeignKeyConstraints();
        VisitFeedbacks::truncate();
        Schema::enableForeignKeyConstraints();

        $feedbacks_chunked = array_chunk($feedbacks, 10);

        foreach ($feedbacks_chunked as  $value) {
            VisitFeedbacks::insert($value);
        }
    }
}
