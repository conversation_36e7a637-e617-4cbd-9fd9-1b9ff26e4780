<?php

namespace Database\Seeders;

use App\PlanVisit;
use App\Reason;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Schema;

class DisapprovalReasonsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $reasons =
            [
                [
                    'reason' => 'Exceed Frequency',
                    'sort' => 100,
                    'request_type' => PlanVisit::class,
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'reason' => 'We need to focus on another speciality',
                    'sort' => 200,
                    'request_type' => PlanVisit::class,
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'reason' => 'Change the plan',
                    'sort' => 300,
                    'request_type' => PlanVisit::class,
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'reason' => 'another reason',
                    'sort' => 400,
                    'request_type' => PlanVisit::class,
                    'created_at' => now(),
                    'updated_at' => now()
                ],
            ];

        Schema::disableForeignKeyConstraints();
        Reason::truncate();
        Schema::enableForeignKeyConstraints();

        $chunked_reasons = array_chunk($reasons, 5);


        foreach ($chunked_reasons as $value) {
            Reason::insert($value);
        }
    }
}
