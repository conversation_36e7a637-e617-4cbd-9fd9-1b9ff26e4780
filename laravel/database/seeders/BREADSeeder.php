<?php

namespace Database\Seeders;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class BREADSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        DB::table('form')->insert([
            'name' => 'Example',
            'table_name' => 'example',
            'read' => 1,
            'edit' => 1,
            'add' => 1,
            'delete' => 1,
            'pagination' => 5
        ]);
        $formId = DB::getPdo()->lastInsertId();
        DB::table('form_field')->insert([
            'name' => 'Title',
            'type' => 'text',
            'browse' => 1,
            'read' => 1,
            'edit' => 1,
            'add' => 1,
            'form_id' => $formId,
            'column_name' => 'name'
        ]);
        DB::table('form_field')->insert([
            'name' => 'Description',
            'type' => 'text_area',
            'browse' => 1,
            'read' => 1,
            'edit' => 1,
            'add' => 1,
            'form_id' => $formId,
            'column_name' => 'description'
        ]);
        DB::table('form_field')->insert([
            'name' => 'Status',
            'type' => 'relation_select',
            'browse' => 1,
            'read' => 1,
            'edit' => 1,
            'add' => 1,
            'form_id' => $formId,
            'column_name' => 'status_id',
            'relation_table' => 'status',
            'relation_column' => 'name'
        ]);
        // $role = Role::where('name', '=', 'guest')->first();
        // Permission::create(['name' => 'browse bread '   . $formId]);
        // Permission::create(['name' => 'read bread '     . $formId]);
        // Permission::create(['name' => 'edit bread '     . $formId]);
        // Permission::create(['name' => 'add bread '      . $formId]);
        // Permission::create(['name' => 'delete bread '   . $formId]);
        // $role->givePermissionTo('browse bread '     . $formId);
        // $role->givePermissionTo('read bread '       . $formId);
        // $role->givePermissionTo('edit bread '       . $formId);
        // $role->givePermissionTo('add bread '        . $formId);
        // $role->givePermissionTo('delete bread '     . $formId);
    }
}
