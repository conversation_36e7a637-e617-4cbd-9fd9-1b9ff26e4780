<?php

namespace Database\Seeders;
use App\DashboardSetting;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Schema;

class DashboardSettingSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $actual_settings =
            [
                [
                    'name' => 'Appear All Approval Employees',
                    'key' => 'appear_all_approval_employees',
                    'value' => 'No',
                    'type' => 'select',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
            ];

        Schema::disableForeignKeyConstraints();
        DashboardSetting::truncate();
        Schema::enableForeignKeyConstraints();

        $chunked_actual_settings = array_chunk($actual_settings, 5);


        foreach ($chunked_actual_settings as $value) {
            DashboardSetting::insert($value);
        }
    }
}
