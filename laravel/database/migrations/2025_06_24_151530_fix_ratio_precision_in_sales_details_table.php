<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * Fix the ratio column precision to accommodate larger values
     * that can occur during distribution processing.
     *
     * Previous: DECIMAL(12, 10) - max value 99.9999999999
     * New: DECIMAL(15, 10) - max value 99999.9999999999
     */
    public function up(): void
    {
        Schema::table('sales_details', function (Blueprint $table) {
            // Increase precision to handle larger ratio values from distribution calculations
            $table->decimal('ratio', 15, 10)->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('sales_details', function (Blueprint $table) {
            // Revert to previous precision
            $table->decimal('ratio', 12, 10)->unsigned()->change();
        });
    }
};
