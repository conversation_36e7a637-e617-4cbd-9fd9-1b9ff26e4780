<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('kpi_percents', function (Blueprint $table) {
            $table->id();
            $table->foreignId('kpi_ratio_id')->nullable()->constrained('kpi_ratios')->cascadeOnDelete();

            $table->integer("from_percent"); // 90 up to 120 percentage

            $table->integer("to_percent"); // 90 up to 120 percentage

            $table->decimal('value');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('kpi_percents');
    }
};
