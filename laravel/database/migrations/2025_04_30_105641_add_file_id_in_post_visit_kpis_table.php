<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('post_visit_kpis', function (Blueprint $table) {
            $table->unsignedBigInteger('file_id')->nullable()->after('date');
            $table->foreign('file_id')->references('id')->on('files_imported')->cascadeOnDelete();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('post_visit_kpis', function (Blueprint $table) {
            //
        });
    }
};
