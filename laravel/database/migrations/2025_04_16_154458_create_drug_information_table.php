<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('drug_information', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('pv_id');
            $table->foreign('pv_id')->references('id')->on('p_v_s')->cascadeOnDelete();
            $table->unsignedBigInteger('product_id');
            $table->foreign('product_id')->references('id')->on('products')->cascadeOnDelete();
            $table->string('conc');
            $table->string('formulation');
            $table->string('api');
            $table->string('dosages');
            $table->string('route_adminstration');
            $table->string('indication_for');
            $table->timestamp('start_date')->nullable();
            $table->timestamp('end_date')->nullable();
            $table->boolean('retake')->nullable();
            $table->boolean('is_happened_again')->nullable();
            $table->text('comment')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('drug_information');
    }
};
