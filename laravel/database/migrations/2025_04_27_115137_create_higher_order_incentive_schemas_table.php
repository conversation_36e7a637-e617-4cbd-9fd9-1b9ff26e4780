<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('higher_order_incentive_schemas', function (Blueprint $table) {
            $table->id();

            $table->integer('year');

            $table->unsignedDecimal('value'); // 1.7

            $table->foreignId('role_id')->nullable()->constrained('roles')->cascadeOnDelete();

            $table->unique(['role_id', 'year'], 'unique_kpi_incentive_schema');

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('higher_order_incentive_schemas');
    }
};
