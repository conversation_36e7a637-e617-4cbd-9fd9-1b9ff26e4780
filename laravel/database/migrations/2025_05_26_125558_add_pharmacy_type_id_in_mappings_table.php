<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('mappings', function (Blueprint $table) {
            $table->unsignedBigInteger('unified_pharmacy_type_id')->nullable()->after('name');
            $table->foreign('unified_pharmacy_type_id')->references('id')->on('unified_pharmacy_types')->nullOnDelete();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('mappings', function (Blueprint $table) {
            //
        });
    }
};
