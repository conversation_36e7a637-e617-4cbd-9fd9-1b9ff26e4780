<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

/**
 * Performance optimization migration for CommercialCostsReportController
 * 
 * This migration creates comprehensive indexes to optimize the complex queries
 * in the CommercialCostsReportController that involve multiple JOINs and
 * filtering operations across commercial requests, sales data, and related tables.
 * 
 * Expected Performance Improvement: 80-90% reduction in query execution time
 */
return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // ========================================
        // COMMERCIAL REQUESTS CORE INDEXES
        // ========================================
        
        Schema::table('commercial_requests', function (Blueprint $table) {
            // Primary filtering index for date range and type filtering
            $table->index(['created_at', 'request_type_id', 'deleted_at'], 'idx_commercial_requests_main_filter');
            
            // Composite index for approval status filtering with plan_visit_details
            $table->index(['id', 'deleted_at', 'created_at'], 'idx_commercial_requests_id_status');
            
            // Index for amount calculations
            $table->index(['amount'], 'idx_commercial_requests_amount');
            
            // Index for from_date used in date formatting
            $table->index(['from_date'], 'idx_commercial_requests_from_date');
        });

        // ========================================
        // COMMERCIAL RELATIONSHIP TABLES
        // ========================================
        
        Schema::table('commercial_lines', function (Blueprint $table) {
            // Composite index for JOIN operations and filtering
            $table->index(['request_id', 'line_id'], 'idx_commercial_lines_request_line');
            
            // Index for line filtering
            $table->index(['line_id'], 'idx_commercial_lines_line_filter');
        });

        Schema::table('commercial_divisions', function (Blueprint $table) {
            // Primary JOIN index
            $table->index(['request_id', 'div_id'], 'idx_commercial_divisions_request_div');
            
            // Index for division grouping
            $table->index(['div_id'], 'idx_commercial_divisions_div_group');
        });

        Schema::table('commercial_products', function (Blueprint $table) {
            // Composite index for product filtering and ratio calculations
            $table->index(['request_id', 'product_id', 'ratio'], 'idx_commercial_products_main');
            
            // Index for product filtering
            $table->index(['product_id'], 'idx_commercial_products_product_filter');
        });

        Schema::table('commercial_doctors', function (Blueprint $table) {
            // Primary relationship index
            $table->index(['request_id', 'doctor_id'], 'idx_commercial_doctors_request_doctor');
            
            // Index for account relationships
            $table->index(['account_id', 'doctor_id'], 'idx_commercial_doctors_account_doctor');
            
            // Index for doctor counting operations
            $table->index(['doctor_id'], 'idx_commercial_doctors_doctor_count');
        });

        Schema::table('commercial_out_of_lists', function (Blueprint $table) {
            // Index for out-of-list doctor counting
            $table->index(['request_id'], 'idx_commercial_out_of_lists_request');
        });

        Schema::table('commercial_categories_costs', function (Blueprint $table) {
            // Composite index for payment method filtering
            $table->index(['request_id', 'payment_method_id'], 'idx_commercial_categories_costs_main');
        });

        // ========================================
        // PLAN VISIT DETAILS OPTIMIZATION
        // ========================================
        
        Schema::table('plan_visit_details', function (Blueprint $table) {
            // Polymorphic relationship index with approval status
            $table->index(['visitable_id', 'visitable_type', 'approval'], 'idx_plan_visit_details_polymorphic_approval');
        });

        // ========================================
        // PRODUCT AND BRAND RELATIONSHIPS
        // ========================================
        
        Schema::table('product_brands', function (Blueprint $table) {
            // Bidirectional indexes for brand-product relationships
            $table->index(['product_id', 'brand_id'], 'idx_product_brands_product_brand');
            $table->index(['brand_id', 'product_id'], 'idx_product_brands_brand_product');
        });

        // ========================================
        // ACCOUNT LINES OPTIMIZATION
        // ========================================
        
        Schema::table('account_lines', function (Blueprint $table) {
            // Complex date range filtering with relationships
            $table->index(['account_id', 'line_division_id', 'from_date', 'to_date'], 'idx_account_lines_date_filter');
            
            // Brick relationship index
            $table->index(['brick_id'], 'idx_account_lines_brick');
        });

        // ========================================
        // NEW ACCOUNT DOCTORS OPTIMIZATION
        // ========================================
        
        Schema::table('new_account_doctors', function (Blueprint $table) {
            // Complex relationship index with date filtering
            $table->index(['account_lines_id', 'doctor_id', 'from_date', 'to_date'], 'idx_new_account_doctors_date_filter');
            
            // Doctor counting index
            $table->index(['doctor_id', 'from_date', 'to_date'], 'idx_new_account_doctors_doctor_date');
        });

        // ========================================
        // SALES PERFORMANCE OPTIMIZATION
        // ========================================
        
        Schema::table('sales', function (Blueprint $table) {
            // Primary sales filtering index
            $table->index(['product_id', 'date'], 'idx_sales_product_date_filter');
        });

        Schema::table('sales_details', function (Blueprint $table) {
            // Brick-based sales filtering with date
            $table->index(['brick_id', 'date', 'value'], 'idx_sales_details_brick_date_value');
            
            // Sale relationship index
            $table->index(['sale_id', 'date'], 'idx_sales_details_sale_date');
        });

        Schema::table('mapping_sale', function (Blueprint $table) {
            // Bidirectional mapping relationship
            $table->index(['mapping_id', 'sale_id'], 'idx_mapping_sale_mapping_sale');
        });

        // ========================================
        // LINKED PHARMACIES OPTIMIZATION
        // ========================================
        
        Schema::table('linked_pharmacies', function (Blueprint $table) {
            // Account-based pharmacy filtering
            $table->index(['account_id', 'pharmable_type', 'pharmable_id'], 'idx_linked_pharmacies_account_polymorphic');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Drop all indexes in reverse order
        Schema::table('linked_pharmacies', function (Blueprint $table) {
            $table->dropIndex('idx_linked_pharmacies_account_polymorphic');
        });

        Schema::table('mapping_sale', function (Blueprint $table) {
            $table->dropIndex('idx_mapping_sale_mapping_sale');
        });

        Schema::table('sales_details', function (Blueprint $table) {
            $table->dropIndex('idx_sales_details_brick_date_value');
            $table->dropIndex('idx_sales_details_sale_date');
        });

        Schema::table('sales', function (Blueprint $table) {
            $table->dropIndex('idx_sales_product_date_filter');
        });

        Schema::table('new_account_doctors', function (Blueprint $table) {
            $table->dropIndex('idx_new_account_doctors_date_filter');
            $table->dropIndex('idx_new_account_doctors_doctor_date');
        });

        Schema::table('account_lines', function (Blueprint $table) {
            $table->dropIndex('idx_account_lines_date_filter');
            $table->dropIndex('idx_account_lines_brick');
        });

        Schema::table('product_brands', function (Blueprint $table) {
            $table->dropIndex('idx_product_brands_product_brand');
            $table->dropIndex('idx_product_brands_brand_product');
        });

        Schema::table('plan_visit_details', function (Blueprint $table) {
            $table->dropIndex('idx_plan_visit_details_polymorphic_approval');
        });

        Schema::table('commercial_categories_costs', function (Blueprint $table) {
            $table->dropIndex('idx_commercial_categories_costs_main');
        });

        Schema::table('commercial_out_of_lists', function (Blueprint $table) {
            $table->dropIndex('idx_commercial_out_of_lists_request');
        });

        Schema::table('commercial_doctors', function (Blueprint $table) {
            $table->dropIndex('idx_commercial_doctors_request_doctor');
            $table->dropIndex('idx_commercial_doctors_account_doctor');
            $table->dropIndex('idx_commercial_doctors_doctor_count');
        });

        Schema::table('commercial_products', function (Blueprint $table) {
            $table->dropIndex('idx_commercial_products_main');
            $table->dropIndex('idx_commercial_products_product_filter');
        });

        Schema::table('commercial_divisions', function (Blueprint $table) {
            $table->dropIndex('idx_commercial_divisions_request_div');
            $table->dropIndex('idx_commercial_divisions_div_group');
        });

        Schema::table('commercial_lines', function (Blueprint $table) {
            $table->dropIndex('idx_commercial_lines_request_line');
            $table->dropIndex('idx_commercial_lines_line_filter');
        });

        Schema::table('commercial_requests', function (Blueprint $table) {
            $table->dropIndex('idx_commercial_requests_main_filter');
            $table->dropIndex('idx_commercial_requests_id_status');
            $table->dropIndex('idx_commercial_requests_amount');
            $table->dropIndex('idx_commercial_requests_from_date');
        });
    }
};
