<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

class OptimizeDoctorFrequenciesPerformance extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Add computed columns for year and month to avoid function-based queries
        Schema::table('doctor_frequencies', function (Blueprint $table) {
            $table->unsignedSmallInteger('year')->after('date')->nullable();
            $table->unsignedTinyInteger('month')->after('year')->nullable();
        });

        // Update existing records with computed year and month values
        DB::statement('UPDATE crm_doctor_frequencies SET year = YEAR(date), month = MONTH(date) WHERE date IS NOT NULL');

        // Add comprehensive indexes for performance
        Schema::table('doctor_frequencies', function (Blueprint $table) {
            // Primary composite index for most common queries
            $table->index(['doctor_id', 'account_id', 'line_id', 'year', 'month'], 'idx_doctor_freq_composite');

            // Index for line-based queries
            $table->index(['line_id', 'year', 'month'], 'idx_doctor_freq_line_date');

            // Index for doctor-based queries
            $table->index(['doctor_id', 'year', 'month'], 'idx_doctor_freq_doctor_date');

            // Index for account-based queries
            $table->index(['account_id', 'year', 'month'], 'idx_doctor_freq_account_date');

            // Index for soft deletes
            $table->index(['deleted_at'], 'idx_doctor_freq_deleted');

            // Index for date range queries
            $table->index(['date'], 'idx_doctor_freq_date');
        });


    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // Drop indexes and columns in reverse order
        Schema::table('doctor_frequencies', function (Blueprint $table) {
            $table->dropIndex('idx_doctor_freq_composite');
            $table->dropIndex('idx_doctor_freq_line_date');
            $table->dropIndex('idx_doctor_freq_doctor_date');
            $table->dropIndex('idx_doctor_freq_account_date');
            $table->dropIndex('idx_doctor_freq_deleted');
            $table->dropIndex('idx_doctor_freq_date');
            $table->dropColumn(['year', 'month']);
        });
    }
}
