<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {

        Schema::table('line_divisions', function (Blueprint $table) {

            $table->unsignedBigInteger('line_id')->change();
            $table->unsignedBigInteger('division_type_id')->change();

            $table->foreign('line_id')->references('id')->on('lines');
            $table->foreign('division_type_id')->references('id')->on('division_types');
        });
    }

};
