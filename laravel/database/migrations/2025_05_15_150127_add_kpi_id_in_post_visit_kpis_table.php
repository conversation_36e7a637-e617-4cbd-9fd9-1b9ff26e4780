<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('post_visit_kpis', function (Blueprint $table) {
            $table->foreignId('kpi_id')->nullable()->constrained('kpis')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('post_visit_kpis', function (Blueprint $table) {
            //
        });
    }
};
