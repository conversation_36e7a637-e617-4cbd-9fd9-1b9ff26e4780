<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('patient_information', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('pv_id');
            $table->foreign('pv_id')->references('id')->on('p_v_s')->cascadeOnDelete();
            $table->string('name');
            $table->unsignedBigInteger('age_unit_id')->nullable();
            $table->foreign('age_unit_id')->references('id')->on('age_units')->onDelete('set null');
            $table->integer('exact_age');
            $table->unsignedBigInteger('age_group_id')->nullable();
            $table->foreign('age_group_id')->references('id')->on('age_groups')->onDelete('set null');
            $table->unsignedBigInteger('sex_id')->nullable();
            $table->foreign('sex_id')->references('id')->on('patient_sex')->onDelete('set null');
            $table->string('medical_history');
            $table->string('taking_medications');
            $table->text('comment')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('patient_information');
    }
};
