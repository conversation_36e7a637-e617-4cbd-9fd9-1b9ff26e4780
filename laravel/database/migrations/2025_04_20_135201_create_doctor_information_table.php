<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('doctor_information', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('doctor_id');
            $table->foreign('doctor_id')->references('id')->on('doctors')->cascadeOnDelete();
            $table->unsignedBigInteger('pv_id');
            $table->foreign('pv_id')->references('id')->on('p_v_s')->cascadeOnDelete();
            $table->unsignedBigInteger('qualification_id')->nullable();
            $table->text('other_info')->nullable();
            $table->string('email');
            $table->string('phone');
            $table->foreign('qualification_id')->references('id')->on('qualifications')->onDelete('set null');
            $table->text('comment')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('doctor_information');
    }
};
