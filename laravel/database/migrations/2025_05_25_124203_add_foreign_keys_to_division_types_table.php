<?php

use App\DivisionType;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('division_types', function (Blueprint $table) {
            $table->unsignedBigInteger('parent_id')->change()->nullable();
        });

        Schema::table('division_types', function (Blueprint $table) {
            DivisionType::where('parent_id', '=', 0)->update(['parent_id' => null]);
            $table->foreign('parent_id')->references('id')->on('division_types')->nullOnDelete();
        });

    }
};
