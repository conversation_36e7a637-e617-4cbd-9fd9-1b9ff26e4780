<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('account_requests', function (Blueprint $table) {
            $table->unsignedBigInteger('line_id')->change()->nullable();
            $table->unsignedBigInteger('div_id')->change()->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('account_requests', function (Blueprint $table) {
            //
        });
    }
};
