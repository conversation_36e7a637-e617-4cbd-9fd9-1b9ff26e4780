<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('event_information', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('pv_id');
            $table->foreign('pv_id')->references('id')->on('p_v_s')->cascadeOnDelete();
            $table->unsignedBigInteger('action_id')->nullable();
            $table->foreign('action_id')->references('id')->on('event_actions')->onDelete('set null');;
            $table->text('narrative');
            $table->text('comment');
            $table->timestamp('reaction_start_date')->nullable();
            $table->timestamp('reaction_end_date')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('event_information');
    }
};
