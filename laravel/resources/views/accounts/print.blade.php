<!DOCTYPE html>
<html class="">

<style>
    @page {
        size: auto;
        margin-top: 3cm;
	    margin-bottom: 2cm;
        header: myheader;
}
</style>
    <body class="">

    <htmlpageheader name="myheader" style="margin: 20%; padding:20%">
    <img src="{{$image}}" style="width: 50px; height: 50px"  class="img-thumbnail" alt="">
    <strong>{{$header}}</strong>
        <br/>
    </htmlpageheader>

    <div class="col-xl-12">
        <section class="box">

            <div class="content-body">
                <div class="row">
                    <div class="col-lg-12 col-md-12 col-12">
                        @if ($accounts->count() > 0)
                        <table class="table table-bordered table-striped table-hover">
                            <thead>
                                <tr>
                                    <th colspan="10">Accounts</th>
                                </tr>
                                <tr>
                                    <th style="font-weight: bold">#</th>
                                    <th style="font-weight: bold">ID</th>
                                    <th style="font-weight: bold">Name</th>
                                    <th style="font-weight: bold">Notes</th>
                                    <th style="font-weight: bold">Type</th>
                                    <th style="font-weight: bold">Sub Type</th>
                                    <th style="font-weight: bold">Address</th>
                                    <th style="font-weight: bold">Tel</th>
                                    <th style="font-weight: bold">Mobile</th>
                                    <th style="font-weight: bold">Email</th>
                                    <th style="font-weight: bold">Active Date</th>
                                    <th style="font-weight: bold">Inactive Date</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach ($accounts as $index=>$account)
                                    <tr>
                                        <th colspan="10">Account {{$account->name}}</th>
                                    </tr>
                                    <tr>
                                        <td>{{ $index + 1 }}</td>
                                        <td>{{ $account->id }}</td>
                                        <td>{{ $account->name }}</td>
                                        <td>{{ $account->notes }}</td>
                                        {{-- <td>{{ $account->type }}</td> --}}
                                        <td>{{ $account->address }}</td>
                                        <td>{{ $account->tel }}</td>
                                        <td>{{ $account->mobile }}</td>
                                        <td>{{ $account->email }}</td>
                                        <td>{{ $account->active_date }}</td>
                                        <td>{{ $account->inactive_date }}</td>
                                    </tr>

                                    {{-- <tr>
                                        <th colspan="10" style="font-weight: bold">Line of {{$account->name}}</th>
                                    </tr> --}}
                                    {{-- @if ($account->accountlines->count() > 0)
                                    <tr>
                                        <th style="font-weight: bold">#</th>
                                        <th style="font-weight: bold">ID</th>
                                        <th style="font-weight: bold" colspan="4">Line Name</th>
                                        <th style="font-weight: bold" colspan="4">Line Division Name</th>
                                        <th style="font-weight: bold" colspan="4">Brick Name</th>
                                        <th style="font-weight: bold" colspan="2">From</th>
                                        <th style="font-weight: bold" colspan="2">To</th>
                                    </tr>
                                        @foreach ($account->accountlines as $index=>$accountline)
                                            <tr>
                                                <td>{{ $index + 1 }}</td>
                                                <td>{{ $accountline->id }}</td>
                                                <td colspan="4">{{ $accountline->line->name }}</td>
                                                <td colspan="4">{{ $accountline->line_division->name }}</td>
                                                <td colspan="4">{{ $accountline->brick->name }}</td>
                                                <td colspan="2">{{ $accountline->from_date }}</td>
                                                <td colspan="2">{{ $accountline->to_date }}</td>
                                            </tr>
                                        @endforeach
                                    @else
                                    <tr>
                                        <th colspan="10">No Data Found</th>
                                    </tr>
                                    @endif --}}

                                    {{-- <tr>
                                        <th colspan="10" style="font-weight: bold">Doctors of {{$account->name}}</th>
                                    </tr>
                                    @if ($account->accountdoctors->count() > 0)
                                    <tr>
                                        <th style="font-weight: bold">#</th>
                                        <th style="font-weight: bold">ID</th>
                                        <th style="font-weight: bold" colspan="4">Doctor Name</th>
                                        <th style="font-weight: bold" colspan="2">From</th>
                                        <th style="font-weight: bold" colspan="2">To</th>
                                    </tr>
                                        @foreach ($account->accountdoctors as $index=>$accountdoctor)
                                            <tr>
                                                <td>{{ $index + 1 }}</td>
                                                <td>{{ $accountdoctor->id }}</td>
                                                <td colspan="4">{{ $accountdoctor->doctor->name }}</td>
                                                <td colspan="2">{{ $accountdoctor->from_date }}</td>
                                                <td colspan="2">{{ $accountdoctor->to_date }}</td>
                                            </tr>
                                        @endforeach
                                    @else
                                    <tr>
                                        <th colspan="10">No Data Found</th>
                                    </tr>
                                    @endif


                                    <tr>
                                        <th colspan="10">--------------------------------------------</th>
                                    </tr> --}}

                                @endforeach
                            </tbody>
                        </table>
                    @else
                        <h2>No Data Found</h2>
                    @endif
                    </div>
                </div>
            </div>
        </section>
    </div>


    <br/>
    <div style="text-align: right">
        <strong>User ID: {{$user_id}} </strong>
        <br/>
        <strong>User Name: {{$user_name}} </strong>
        <br/>
        <strong>Date: {{$exported_date}} </strong>
        <br/>
    </div>

    <br/>
    <strong>{{$footer}}</strong>

    </body>
</html>
