<!DOCTYPE html>
<html class=" ">

<style>
    @page {
        size: auto;
        margin-top: 3cm;
	    margin-bottom: 2cm;
        header: myheader;
}
</style>
    <body class=" ">

    <htmlpageheader name="myheader" style="margin: 20%; padding:20%">
    <img src="{{$image}}" style="width: 50px; height: 50px"  class="img-thumbnail" alt="">
    <strong>{{$header}}</strong>
    <br/>
    </htmlpageheader>

    <div class="col-xl-12">
        <section class="box">

            <div class="content-body">
                <div class="row">
                    <div class="col-lg-12 col-md-12 col-12">
                        @if ($answers->count() > 0)
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>#</th>
                                        <th>ID</th>
                                        <th>Name</th>
                                        <th>Weight</th>
                                        <th>Notes</th>
                                    </tr>
                                </thead>
                                {{-- <tbody>
                                    <tr>
                                        <th scope="row">1</th>
                                        <td>Mark</td>
                                        <td>Otto</td>
                                        <td>@mdo</td>
                                    </tr>
                                </tbody> --}}
                                @foreach ($answers as $index=>$answer)

                                <tbody>
                                    <tr>
                                        <td>{{ $index + 1 }}</td>
                                        <td>{{ $answer->id }}</td>
                                        <td>{{ $answer->name }}</td>
                                        <td>{{ $answer->weight }}</td>
                                        <td>{{ $answer->notes }}</td>
                                    </tr>
                                </tbody>

                            @endforeach
                            </table>
                            @else
                            <h2>No Data Found</h2>
                        @endif
                    </div>
                </div>
            </div>
        </section>
    </div>

    <br/>
    <div style="text-align: right">
        <strong>User ID: {{$user_id}} </strong>
        <br/>
        <strong>User Name: {{$user_name}} </strong>
        <br/>
        <strong>Date: {{$exported_date}} </strong>
        <br/>
    </div>

    <br/>
    <strong>{{$footer}}</strong>

    </body>
</html>
