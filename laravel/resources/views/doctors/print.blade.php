<!DOCTYPE html>
<html class=" ">

<style>
    @page {
        size: auto;
        margin-top: 4cm;
        margin-bottom: 2cm;
        header: myheader;
    }
    img {
        width: 200px;
        height: 100px;
    }
</style>

<body class=" ">
    <htmlpageheader name="myheader">
        <img src="{{ $image }}" class="img-thumbnail" alt="">
    </htmlpageheader>
    <div style="text-align: center; margin:0">
        <strong style="color: blue;">{{ $header }}</strong>
    </div>

    <div class="col-xl-12">
        <section class="box">

            <div class="content-body">
                <div class="row">
                    <div class="col-lg-12 col-md-12 col-12">
                        @if ($doctors->count() > 0)
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>#</th>
                                        <th>ID</th>
                                        <th>Ucode</th>
                                        <th>Name</th>
                                        <th>Tel</th>
                                        <th>Mobile</th>
                                        <th>Email</th>
                                        <th>Speciality</th>
                                        <th>Sub Speciality</th>
                                        <th>Date Of Birth</th>
                                        <th>Date Of Marriage</th>
                                        <th>Active Date</th>
                                        <th>Inactive Date</th>
                                    </tr>
                                </thead>
                                @foreach ($doctors as $index=>$doctor)

                                <tbody>
                                    <tr>
                                        <td>{{ $index + 1 }}</td>
                                        <td>{{ $doctor->id }}</td>
                                        <td>{{ $doctor->ucode }}</td>
                                        <td>{{ $doctor->name }}</td>
                                        <td>{{ $doctor->tel }}</td>
                                        <td>{{ $doctor->mobile }}</td>
                                        <td>{{ $doctor->email }}</td>
                                        <td>{{ $doctor->speciality }}</td>
                                        <td>{{ $doctor->sub_speciality }}</td>
                                        <td>{{ $doctor->dob }}</td>
                                        <td>{{ $doctor->dom }}</td>
                                        <td>{{ $doctor->active_date }}</td>
                                        <td>{{ $doctor->inactive_date }}</td>
                                    </tr>
                                </tbody>

                            @endforeach
                            </table>
                            @else
                            <h2>No Data Found</h2>
                        @endif
                    </div>
                </div>
            </div>
        </section>
    </div>


    <br/>
    <div style="text-align: right">
        <strong>User ID: {{$user_id}} </strong>
        <br/>
        <strong>User Name: {{$user_name}} </strong>
        <br/>
        <strong>Date: {{$exported_date}} </strong>
        <br/>
    </div>

    <br/>
    <strong>{{$footer}}</strong>

    </body>
</html>
