<!DOCTYPE html>
<html class=" ">

<style>
    @page {
        size: auto;
        margin-top: 4cm;
        margin-bottom: 2cm;
        header: myheader;
    }
    img {
        width: 200px;
        height: 100px;
    }
</style>

<body class=" ">
    <htmlpageheader name="myheader">
        <img src="{{ $image }}" class="img-thumbnail" alt="">
    </htmlpageheader>
    <div style="text-align: center; margin:0">
        <strong style="color: blue;">{{ $header }}</strong>
    </div>

    <div class="col-xl-12">
        <section class="box">

            <div class="content-body">
                <div class="row">
                    <div class="col-lg-12 col-md-12 col-12">
                        @if ($products->count() > 0)
                            <table class="table table-bordered table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th colspan="10">Products</th>
                                    </tr>
                                    <tr>
                                        <th style="font-weight: bold">#</th>
                                        <th style="font-weight: bold">ID</th>
                                        <th style="font-weight: bold">Ucode</th>
                                        <th style="font-weight: bold">Name</th>
                                        <th style="font-weight: bold">Short Name</th>
                                        <th style="font-weight: bold">Quantity</th>
                                        <th style="font-weight: bold">Type</th>
                                        <th style="font-weight: bold">Family</th>
                                        <th style="font-weight: bold">Classification</th>
                                        <th style="font-weight: bold">Launch Date</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach ($products as $index=>$product)
                                        <tr>
                                            <th colspan="10">Product {{$product->name}}</th>
                                        </tr>
                                        <tr>
                                            <td>{{ $index + 1 }}</td>
                                            <td>{{ $product->id }}</td>
                                            <td>{{ $product->ucode }}</td>
                                            <td>{{ $product->name }}</td>
                                            <td>{{ $product->short_name }}</td>
                                            <td>{{ $product->quantity }}</td>
                                            <td>{{ $product->type->name }}</td>
                                            <td>{{ $product->family->name }}</td>
                                            <td>{{ $product->classification->name }}</td>
                                            <td>{{ $product->launch_date }}</td>
                                        </tr>

                                        <tr>
                                            <th colspan="10" style="font-weight: bold">Brands of {{$product->name}}</th>
                                        </tr>
                                        @if ($product->productbrands->count() > 0)
                                        <tr>
                                            <th style="font-weight: bold">#</th>
                                            <th style="font-weight: bold">ID</th>
                                            <th style="font-weight: bold" colspan="4">Brand Name</th>
                                            <th style="font-weight: bold" colspan="2">From</th>
                                            <th style="font-weight: bold" colspan="2">To</th>
                                        </tr>
                                            @foreach ($product->productbrands as $index=>$productbrand)
                                                <tr>
                                                    <td>{{ $index + 1 }}</td>
                                                    <td>{{ $productbrand->id }}</td>
                                                    <td colspan="4">{{ $productbrand->brand->name }}</td>
                                                    <td colspan="2">{{ $productbrand->from_date }}</td>
                                                    <td colspan="2">{{ $productbrand->to_date }}</td>
                                                </tr>
                                            @endforeach
                                        @else
                                        <tr>
                                            <th colspan="10">No Data Found</th>
                                        </tr>
                                        @endif

                                        <tr>
                                            <th colspan="10" style="font-weight: bold">Manufacturers of {{$product->name}}</th>
                                        </tr>
                                        @if ($product->productmanufacturers->count() > 0)
                                        <tr>
                                            <th style="font-weight: bold">#</th>
                                            <th style="font-weight: bold">ID</th>
                                            <th style="font-weight: bold" colspan="4">Manufacturer Name</th>
                                            <th style="font-weight: bold" colspan="2">From</th>
                                            <th style="font-weight: bold" colspan="2">To</th>
                                        </tr>
                                            @foreach ($product->productmanufacturers as $index=>$productmanufacturer)
                                                <tr>
                                                    <td>{{ $index + 1 }}</td>
                                                    <td>{{ $productmanufacturer->id }}</td>
                                                    <td colspan="4">{{ $productmanufacturer->manufacturer->name }}</td>
                                                    <td colspan="2">{{ $productmanufacturer->from_date }}</td>
                                                    <td colspan="2">{{ $productmanufacturer->to_date }}</td>
                                                </tr>
                                            @endforeach
                                        @else
                                        <tr>
                                            <th colspan="10">No Data Found</th>
                                        </tr>
                                        @endif

                                        <tr>
                                            <th colspan="10" style="font-weight: bold">Prices of {{$product->name}}</th>
                                        </tr>
                                        @if ($product->productprices->count() > 0)
                                        <tr>
                                            <th style="font-weight: bold">#</th>
                                            <th style="font-weight: bold">ID</th>
                                            <th style="font-weight: bold" colspan="2">Distributor Name</th>
                                            <th style="font-weight: bold">Selling Price</th>
                                            <th style="font-weight: bold">Average Price</th>
                                            <th style="font-weight: bold">Average Tender Price</th>
                                            <th style="font-weight: bold">Average Target Price</th>
                                            <th style="font-weight: bold">From</th>
                                            <th style="font-weight: bold">To</th>
                                        </tr>
                                            @foreach ($product->productprices as $index=>$productprice)
                                                <tr>
                                                    <td>{{ $index + 1 }}</td>
                                                    <td>{{ $productprice->id }}</td>
                                                    <td colspan="2">{{ $productprice->distributor->name }}</td>
                                                    <td>{{ $productprice->selling_price }}</td>
                                                    <td>{{ $productprice->avg_price }}</td>
                                                    <td>{{ $productprice->avg_tender_price }}</td>
                                                    <td>{{ $productprice->avg_target_price }}</td>
                                                    <td>{{ $productprice->from_date }}</td>
                                                    <td>{{ $productprice->to_date }}</td>
                                                </tr>
                                            @endforeach
                                        @else
                                        <tr>
                                            <th colspan="10">No Data Found</th>
                                        </tr>
                                        @endif

                                        <tr>
                                            <th colspan="10">--------------------------------------------</th>
                                        </tr>

                                    @endforeach
                                </tbody>
                            </table>
                        @else
                            <h2>No Data Found</h2>
                        @endif
                    </div>
                </div>
            </div>
        </section>
    </div>


    <br/>
    <div style="text-align: right">
        <strong>User ID: {{$user_id}} </strong>
        <br/>
        <strong>User Name: {{$user_name}} </strong>
        <br/>
        <strong>Date: {{$exported_date}} </strong>
        <br/>
    </div>

    <br/>
    <strong>{{$footer}}</strong>

    </body>
</html>
