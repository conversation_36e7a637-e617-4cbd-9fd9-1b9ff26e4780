<!DOCTYPE html>
<html class=" ">

<style>
    @page {
        size: auto;
        margin-top: 3cm;
	    margin-bottom: 2cm;
        header: myheader;
}
</style>
    <body class=" ">

    <htmlpageheader name="myheader" style="margin: 20%; padding:20%">
    <img src="{{$image}}" style="width: 50px; height: 50px"  class="img-thumbnail" alt="">
    <strong>{{$header}}</strong>
        <br/>
    </htmlpageheader>

    <div class="col-xl-12">
        <section class="box">
            <div class="content-body">
                <div class="row">
                    <div class="col-lg-12 col-md-12 col-12">
                        @if ($accounttypes->count() > 0)
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>#</th>
                                        <th>ID</th>
                                        <th>Name</th>
                                        <th>Notes</th>
                                        <th>Sort</th>
                                        <th>Shift</th>
                                        <th>Parent</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach ($accounttypes as $index=>$account_type)
                                        <tr>
                                            <td>{{ $index + 1 }}</td>
                                            <td>{{ $account_type->id }}</td>
                                            <td>{{ $account_type->name }}</td>
                                            <td>{{ $account_type->notes }}</td>
                                            <td>{{ $account_type->sort }}</td>
                                            <td>{{ $account_type->shift->name}}</td>
                                            <td>{{ $account_type->parent}}</td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                            @else
                            <h2>No Data Found</h2>
                        @endif
                    </div>
                </div>
            </div>
        </section>
    </div>

    <br/>
    <div style="text-align: right">
        <strong>User ID: {{$user_id}} </strong>
        <br/>
        <strong>User Name: {{$user_name}} </strong>
        <br/>
        <strong>Date: {{$exported_date}} </strong>
        <br/>
    </div>

    <br/>
    <strong>{{$footer}}</strong>

    </body>
</html>
