<!DOCTYPE html>
<html class=" ">

<style>
    @page {
        size: auto;
        margin-top: 4cm;
        margin-bottom: 2cm;
        header: myheader;
    }
    img {
        width: 200px;
        height: 100px;
    }
</style>

<body class=" ">
    <htmlpageheader name="myheader">
        <img src="{{ $image }}" class="img-thumbnail" alt="">
    </htmlpageheader>
    <div style="text-align: center; margin:0">
        <strong style="color: blue;">{{ $header }}</strong>
    </div>

    <div class="col-xl-12">
        <section class="box">
            <div class="content-body">
                <div class="row">
                    <div class="col-lg-12 col-md-12 col-12">
                        @if ($owactualvisits->count() > 0)
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        {{-- <th>#</th> --}}
                                        <th>ID</th>
                                        <th>Date</th>
                                        <th>Shift</th>
                                        <th>Notes</th>
                                        <th>Ow Type</th>
                                        <th>User</th>
                                        <th>Ow Plan Id</th>
                                    </tr>
                              
                                </thead>
                                @foreach ($owactualvisits as $index=>$owactualvisit)

                                <tbody>
                                    <tr>
                                        {{-- <td>{{ $index + 1 }}</td> --}}
                                        <td>{{ $owactualvisit->id }}</td>
                                        <td>{{ $owactualvisit->date }}</td>
                                        <td>{{ $owactualvisit->shift->name }}</td>
                                        <td>{{ $owactualvisit->notes }}</td>
                                        <td>{{ $owactualvisit->owType->name }}</td>
                                        <td>{{ $owactualvisit->user->fullname }}</td>
                                        <td>{{ $owactualvisit->ow_plan_id }}</td>
                                    </tr>
                                </tbody>

                            @endforeach
                            </table>
                            @else
                            <h2>No Data Found</h2>
                        @endif
                    </div>
                </div>
            </div>
        </section>
    </div>


    <br/>
    <div style="text-align: right">
        <strong>User ID: {{$user_id}} </strong>
        <br/>
        <strong>User Name: {{$user_name}} </strong>
        <br/>
        <strong>Date: {{$exported_date}} </strong>
        <br/>
    </div>

    <br/>
    <strong>{{$footer}}</strong>

    </body>
</html>
