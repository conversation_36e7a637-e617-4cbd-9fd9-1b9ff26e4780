<!DOCTYPE html>
<html class=" ">

<style>
    @page {
        size: auto;
        margin-top: 3cm;
	    margin-bottom: 2cm;
        header: myheader;
}
</style>
    <body class=" ">

    <htmlpageheader name="myheader" style="margin: 20%; padding:20%">
    <img src="{{$image}}" style="width: 50px; height: 50px"  class="img-thumbnail" alt="">
    <strong>{{$header}}</strong>
        <br/>
    </htmlpageheader>

    <div class="col-xl-12">
        <section class="box">
            <div class="content-body">
                <div class="row">
                    <div class="col-lg-12 col-md-12 col-12">
                        @if ($actualvisits->count() > 0)
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        {{-- <th>#</th> --}}
                                        <th>ID</th>
                                        <th>user</th>
                                        <th>line</th>
                                        <th>division</th>
                                        <th>brick</th>
                                        <th>account type</th>
                                        <th>speciality</th>
                                        <th>account</th>
                                        <th>doctor</th>
                                        <th>visit type</th>
                                        <th>visit date</th>
                                    </tr>
                              
                                </thead>
                                @foreach ($actualvisits as $index=>$actualvisit)

                                <tbody>
                                    <tr>
                                        {{-- <td>{{ $index + 1 }}</td> --}}
                                        <td>{{ $actualvisit->id }}</td>
                                        <td>{{ $actualvisit->user->fullname }}</td>
                                        <td>{{ $actualvisit->line->name }}</td>
                                        <td>{{ $actualvisit->division->name }}</td>
                                        <td>{{ $actualvisit->brick?->name ?? 'All' }}</td>
                                        <td>{{ $actualvisit->accountType->name }}</td>
                                        <td>{{ $actualvisit->account_dr_id ? $actualVisit->doctor->speciality->name : '' }}</td>
                                        <td>{{ $actualvisit->account->name }}</td>
                                        <td>{{ $actualvisit->account_dr_id ? $actualVisit->doctor->name : '' }}</td>
                                        <td>{{ $actualvisit->visitType->name }}</td>   
                                        <td>{{ $actualvisit->visit_date }}</td>   
                                    </tr>
                                </tbody>

                            @endforeach
                            </table>
                            @else
                            <h2>No Data Found</h2>
                        @endif
                    </div>
                </div>
            </div>
        </section>
    </div>


    <br/>
    <div style="text-align: right">
        <strong>User ID: {{$user_id}} </strong>
        <br/>
        <strong>User Name: {{$user_name}} </strong>
        <br/>
        <strong>Date: {{$exported_date}} </strong>
        <br/>
    </div>

    <br/>
    <strong>{{$footer}}</strong>

    </body>
</html>
