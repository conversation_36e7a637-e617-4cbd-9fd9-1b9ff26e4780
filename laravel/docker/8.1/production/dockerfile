FROM php:8.1-fpm-alpine

WORKDIR /var/www/html

RUN apk update \
    && apk add nginx \
    && apk add --no-cache supervisor \
    && apk add --no-cache libzip-dev \
    && apk add --no-cache libjpeg-turbo-dev \
    && apk add --no-cache libpng-dev \
    && apk add --no-cache freetype-dev \
    && apk add --no-cache libxml2-dev \
    && apk add --no-cache icu-dev \
    && apk add --no-cache libmemcached-dev \
    && apk add --virtual .build-deps \
        build-base \
        libc-dev \
        linux-headers \
        autoconf \
        libtool \
        make \
    && docker-php-ext-install pdo_mysql \
    && docker-php-ext-install zip \
    && docker-php-ext-install exif \
    && docker-php-ext-install pcntl \
    && docker-php-ext-configure gd \
        --with-freetype \
        --with-jpeg \
    && docker-php-ext-install gd \
    && pecl install memcached \
    && docker-php-ext-enable memcached \
    && apk del .build-deps

# Install composer
RUN curl -sS https://getcomposer.org/installer | php -- --install-dir=/usr/bin --filename=composer

# create the www-data user
RUN adduser -D -u 1000 itgates
RUN addgroup itgates www-data

# Copy code to /var/www
COPY --chown=itgates:www-data . /var/www/html


# set the owner and group of the storage directory to www-data
RUN chown -R itgates:www-data /var/www/html/storage

# set the permissions of the storage directory to 775
RUN chmod -R 775 /var/www/html/storage
RUN mkdir -p /etc/nginx/sites-enabled
# Copy nginx/php/supervisor configs
RUN cp /var/www/html/docker/8.1/production/supervisord.conf /etc/supervisord.conf
RUN cp /var/www/html/docker/8.1/production/php.ini /usr/local/etc/php/conf.d/app.ini
RUN cp /var/www/html/docker/8.1/production/nginx.conf /etc/nginx/sites-enabled/default
RUN cp /var/www/html/docker/8.1/production/start-container /usr/bin/start-container

# PHP Error Log Files
RUN mkdir /var/log/php
RUN mkdir /var/log/supervisor
RUN touch /var/log/php/errors.log && chmod 777 /var/log/php/errors.log

# Deployment steps
RUN composer install --optimize-autoloader --no-dev
RUN chmod +x /usr/bin/start-container

EXPOSE 80

ENTRYPOINT  [ "start-container" ]