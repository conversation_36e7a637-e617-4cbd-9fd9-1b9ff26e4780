server {
    listen 80;
    root /var/www/html/public;

    add_header X-Frame-Options "SAMEORIGIN";
    add_header X-Content-Type-Options "nosniff";

    index index.php index.html;
    charset utf-8;

    location = /favicon.ico { access_log off; log_not_found off; }
    location = /robots.txt  { access_log off; log_not_found off; }

    error_log /var/www/html/storage/logs/nginx-error.log error;
    access_log /var/www/html/storage/logs/nginx-access.log;
    error_page 404 /index.php;

    client_max_body_size 100M;

    location ~ \.php$ {
        try_files $uri =404;
        fastcgi_split_path_info ^(.+\.php)(/.+)$;
        fastcgi_pass 127.0.0.1:9000;
        fastcgi_index index.php;
        include fastcgi_params;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        fastcgi_param PATH_INFO $fastcgi_path_info;
        fastcgi_buffering off;
    }

    location / {
        try_files $uri $uri/ /index.php?$query_string;
        gzip_static on;
	}

    location ~ /\.(?!well-known).* {
        deny all;
    }
}
