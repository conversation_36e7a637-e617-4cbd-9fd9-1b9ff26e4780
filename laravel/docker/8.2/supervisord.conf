[supervisord]
nodaemon=true
user=root
logfile=/var/log/supervisor/supervisord.log
pidfile=/var/run/supervisord.pid

[program:php]
command=/usr/bin/php -d variables_order=EGPCS /var/www/html/artisan octane:start --host=0.0.0.0 --port=8001 --watch
# command=/usr/bin/php -d variables_order=EGPCS /var/www/html/artisan serve --host=0.0.0.0  --port=8001
user=sail
environment=LARAVEL_SAIL="1"
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
stderr_logfile=/dev/stderr
stderr_logfile_maxbytes=0


[program:horizon]
process_name=%(program_name)s
command=/usr/bin/php -d variables_order=EGPCS /var/www/html/artisan horizon
autostart=true
autorestart=true
user=sail
redirect_stderr=true
stdout_logfile=/var/www/html/storage/logs/horizon.log
stopwaitsecs=3600

[program:schedule]
process_name=%(program_name)s
command=/usr/bin/php -d variables_order=EGPCS /var/www/html/artisan schedule:work
autostart=true
autorestart=true
user=sail
redirect_stderr=true
stdout_logfile=/var/www/html/storage/logs/schedule.log
stopwaitsecs=3600
numprocs=1

