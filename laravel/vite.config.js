// vite.config.js
import {defineConfig} from 'vite';
import vue from '@vitejs/plugin-vue2';
import laravel from 'laravel-vite-plugin';
import {fileURLToPath, URL} from 'node:url';
import {VitePWA} from 'vite-plugin-pwa'
import replace from '@rollup/plugin-replace';

import path from 'path';
import fs from 'fs';

// Custom copy plugin
function copyFilesPlugin(options) {
    const { src, dest } = options;

    return {
        name: 'copy-files-plugin',
        apply: 'build',  // Only applies in build mode
        buildEnd() {
            const sourcePath = path.resolve(__dirname, src);
            const destPath = path.resolve(__dirname, dest);

            function copyFiles(srcDir, destDir) {
                fs.mkdirSync(destDir, { recursive: true });
                fs.readdirSync(srcDir).forEach((file) => {
                    const srcFile = path.join(srcDir, file);
                    const destFile = path.join(destDir, file);
                    if (fs.lstatSync(srcFile).isDirectory()) {
                        copyFiles(srcFile, destFile);  // Recursive copy for directories
                    } else {
                        fs.copyFileSync(srcFile, destFile);  // Copy individual files
                    }
                });
            }

            // Start copying
            copyFiles(sourcePath, destPath);
            console.log(`Files copied from ${src} to ${dest}`);
        }
    };
}


export default defineConfig({
    server: {
        hmr: {
            host: 'localhost',
        },
    },
    plugins: [
        VitePWA({
            // Enable PWA in development
            devOptions: {
                enabled: true,

            },

            // Register service worker
            registerType: 'autoUpdate',

            // Manifest configuration
            manifest: {
                name: 'GemStone',
                short_name: 'gemstone',
                theme_color: '#ffffff',
                start_url:'/',
                display:'standalone',
                icons: [
                    {
                        src: '/android-icon-192x192.png',
                        sizes: '192x192',
                        type: 'image/png'
                    },
                    {
                        src: '/android-icon-144x144.png',
                        sizes: '144x144',
                        type: 'image/png'
                    }
                ]
            },

            // Workbox configuration
            workbox: {
                // Development mode
                cleanupOutdatedCaches: true,
                sourcemap: true,

                maximumFileSizeToCacheInBytes: 12 * 1024 * 1024,

                globPatterns:['**/*.{js,css,html,ico,png,svg}'],
                navigateFallback: 'index.html',

                // Cache naming
                cacheId: 'my-app-cache',

                // Cache strategies
                runtimeCaching: [
                    {
                        // Cache static assets
                        urlPattern: /\.(js|css|ico|png|jpg|jpeg|svg|gif|woff2?)$/i,
                        handler: 'CacheFirst',
                        options: {
                            cacheName: 'static-assets',
                            expiration: {
                                maxEntries: 100,
                                maxAgeSeconds: 24 * 60 * 60 // 24 hours
                            },
                            cacheableResponse: {
                                statuses: [0, 200]
                            }
                        }
                    },
                    {
                        // Cache API requests
                        urlPattern: /^https?:\/\/api\./i,
                        handler: 'NetworkFirst',
                        options: {
                            cacheName: 'api-cache',
                            networkTimeoutSeconds: 10,
                            expiration: {
                                maxEntries: 50,
                                maxAgeSeconds: 60 * 60 // 1 hour
                            },
                            cacheableResponse: {
                                statuses: [0, 200]
                            }
                        }
                    },
                    {
                        // Cache pages for offline access
                        urlPattern: /^https?:\/\/.*/i,
                        handler: 'NetworkFirst',
                        options: {
                            cacheName: 'pages-cache',
                            expiration: {
                                maxEntries: 30,
                                maxAgeSeconds: 60 * 60 * 24 // 24 hours
                            },
                            cacheableResponse: {
                                statuses: [0, 200]
                            }
                        }
                    },
                    {
                        urlPattern: /index.html$/i,
                        handler: 'StaleWhileRevalidate',
                        options: {
                            cacheName: 'html-cache'
                        }
                    }
                ],

                // Skip waiting and clients claim
                skipWaiting: true,
                clientsClaim: true,
            }
        }),
        // Replace environment variables
        replace({
            preventAssignment: true,
            __DATE__: new Date().toISOString(),
            __RELOAD_SW__: process.env.RELOAD_SW === 'true'
        }),
        laravel({
            input: [
                'resources/js/app.js',
                'resources/sass/app.scss'
            ],
            refresh: true,
        }),
        vue(),
        copyFilesPlugin({
            src: '../coreui/public',
            dest: './public'
        })
    ],
    resolve: {
        alias: {
            '~ag-grid-community' : fileURLToPath(new URL('../coreui/node_modules/ag-grid-community', import.meta.url)),
            '@': fileURLToPath(new URL('./coreui/src', import.meta.url)),
            '~': fileURLToPath(new URL('./resources', import.meta.url)),
            'process/browser': fileURLToPath(new URL('./node_modules/process/browser.js', import.meta.url)),
            'vue': fileURLToPath(new URL('./node_modules/vue/dist/vue.esm.js', import.meta.url)) // use compiler-included Vue build
        },
        extensions: ['.wasm', '.mjs', '.js', '.jsx', '.json', '.vue']
    },
    build: {
        rollupOptions: {
            output: {
                manualChunks(id) {
                    // Split node_modules into a separate chunk
                    if (id.includes('node_modules')) {
                        return id.toString().split('node_modules/')[1].split('/')[0].toString(); // Creates chunk by package name
                    }
                    // Further custom chunk logic can go here
                },
            }
        },
        sourcemap: true,
    },
});
