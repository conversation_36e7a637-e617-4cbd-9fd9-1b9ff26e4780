{"private": true, "type": "module", "scripts": {"dev": "vite", "build": "node --max-old-space-size=8192 node_modules/.bin/vite build"}, "devDependencies": {"@babel/plugin-proposal-optional-chaining": "^7.16.7", "@vitejs/plugin-vue2": "^2.3.1", "autoprefixer": "^10.4.21", "chromedriver": "^78.0.1", "cross-env": "^5.1", "cssnano": "^7.0.6", "laravel-mix": "^6.0.43", "laravel-mix-bundle-analyzer": "^1.0.5", "laravel-vite-plugin": "^1.0.5", "lodash": "^4.17.21", "nightwatch": "^1.6.3", "resolve-url-loader": "^5.0.0", "sass": "^1.32.7", "sass-loader": "^8.0.2", "script-loader": "^0.7.2", "terser": "^5.39.0", "vite": "^5.4.10", "vite-plugin-compression": "^0.5.1", "vite-plugin-imagemin": "^0.6.1", "vue": "^2.7.0", "vue-loader": "^15.10.0", "vue-template-compiler": "^2.7.10"}, "engines": {"node": ">= 18.17.0", "npm": ">= 9.6.7"}, "dependencies": {"@rollup/plugin-replace": "^6.0.1", "vite-plugin-pwa": "^0.20.5"}}