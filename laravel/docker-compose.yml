# For more information: https://laravel.com/docs/sail
services:
  laravel.test:
    build:
      context: ./docker/8.2
      dockerfile: Dockerfile
      args:
        WWWGROUP: '${WWWGROUP}'
        NODE_VERSION: 18
        TZ: 'Africa/Cairo'
    image: sail-8.2/app
    extra_hosts:
      - 'host.docker.internal:host-gateway'
    ports:
      - '${SERVER_PORT:-80}:${SERVER_PORT:-80}'
      - '${VITE_PORT:-5173}:${VITE_PORT:-5173}'
    environment:
      WWWUSER: '${WWWUSER}'
      LARAVEL_SAIL: 1
      XDEBUG_MODE: '${SAIL_XDEBUG_MODE:-off}'
      XDEBUG_CONFIG: '${SAIL_XDEBUG_CONFIG:-client_host=host.docker.internal}'
    volumes:
      - '.:/var/www/html'
      - '../coreui:/var/www/coreui'
    networks:
      - sail
    depends_on:
      - phpmyadmin
      - mysql
      - redis
      - nodejs
      # - export-service
  nodejs:
    build:
      context: ../websocket-server
      dockerfile: Dockerfile
    restart: unless-stopped
    ports:
      - '${NODE_PORT:-3000}:3000'
    environment:
      REDIS_HOST: redis
    volumes:
      - '../websocket-server:/var/www/html'
    networks:
      - sail
    depends_on:
      - redis
  # export-service:
  #   build:
  #     context: ../export-service
  #     dockerfile: Dockerfile
  #   restart: unless-stopped
  #   ports:
  #     - '${NODE_PORT:-7000}:7000'
  #   volumes:
  #     - '../export-service:/var/www/html'
  #   networks:
  #     - sail
  mysql:
    image: 'mysql/mysql-server:8.0'
    ports:
      - '${FORWARD_DB_PORT:-3306}:3306'

    environment:
      MYSQL_ROOT_PASSWORD: '${DB_PASSWORD}'
      MYSQL_ROOT_HOST: "%"
      MYSQL_DATABASE: '${DB_DATABASE}'
      MYSQL_USER: '${DB_USERNAME}'
      MYSQL_PASSWORD: '${DB_PASSWORD}'
      MYSQL_ALLOW_EMPTY_PASSWORD: 1
    command:
      - --local_infile=1
      - --bind-address=0.0.0.0
    volumes:
      - 'sail-mysql:/var/lib/mysql'
      - './vendor/laravel/sail/database/mysql/create-testing-database.sh:/docker-entrypoint-initdb.d/10-create-testing-database.sh'
    networks:
      - sail
    healthcheck:
      test: [ "CMD", "mysqladmin", "ping", "-p${DB_PASSWORD}" ]
      retries: 3
      timeout: 5s
  redis:
    image: 'redis:alpine'
    ports:
      - '${FORWARD_REDIS_PORT:-6379}:6379'
    volumes:
      - 'sail-redis:/data'
    networks:
      - sail
    healthcheck:
      test: [ "CMD", "redis-cli", "ping" ]
      retries: 3
      timeout: 5s
  phpmyadmin:
    image: 'phpmyadmin:latest'
    restart: always
    environment:
      - PMA_HOST=mysql
      - UPLOAD_LIMIT=4096M
    ports:
      - 8070:80
    depends_on:
      - mysql
    volumes:
      - sail-phpmyadmin:/var/lib/phpmyadmin/data
    networks:
      - sail
networks:
  sail:
    driver: bridge
volumes:
  sail-mysql:
    driver: local
  sail-redis:
    driver: local
  sail-phpmyadmin:
    driver: local
