<?php

use App\Http\Controllers\ApprovalFlowController;
use App\Http\Controllers\RequestController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Approval System API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for the hierarchical approval system.
| These routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group.
|
*/

// Approval Flow Endpoints
Route::middleware(['auth:api'])->group(function () {
    // Approval Flow Management
    Route::get('/approval-flows', [ApprovalFlowController::class, 'index']);
    Route::post('/approval-flows', [ApprovalFlowController::class, 'store']);
    Route::get('/approval-flows/{id}', [ApprovalFlowController::class, 'show']);
    Route::put('/approval-flows/{id}', [ApprovalFlowController::class, 'update']);
    Route::delete('/approval-flows/{id}', [ApprovalFlowController::class, 'destroy']);

    // Approval Level Management
    Route::get('/approval-flows/{id}/levels', [ApprovalFlowController::class, 'getLevels']);
    Route::post('/approval-flows/{id}/levels', [ApprovalFlowController::class, 'addLevel']);
    Route::put('/approval-flows/{id}/levels/{levelId}', [ApprovalFlowController::class, 'updateLevel']);
    Route::delete('/approval-flows/{id}/levels/{levelId}', [ApprovalFlowController::class, 'removeLevel']);

    // Sample Request Approval Routes
    Route::get('/sample-requests/approval-flows', [\App\Http\Controllers\SampleRequestController::class, 'getApprovalFlows']);
    Route::get('/sample-requests/pending-approvals', [\App\Http\Controllers\SampleRequestController::class, 'getPendingApprovals']);
    Route::post('/sample-requests/{id}/approve', [\App\Http\Controllers\SampleRequestController::class, 'approve']);
    Route::post('/sample-requests/{id}/reject', [\App\Http\Controllers\SampleRequestController::class, 'reject']);

    // Request Management
    Route::get('/requests', [RequestController::class, 'index']);
    Route::post('/requests', [RequestController::class, 'store']);
    Route::get('/requests/{id}', [RequestController::class, 'show']);

    // Approval Actions
    Route::post('/requests/{id}/approve', [RequestController::class, 'approve']);
    Route::post('/requests/{id}/reject', [RequestController::class, 'reject']);
    Route::post('/requests/{id}/undo-approve', [RequestController::class, 'undoApprove']);
    Route::post('/requests/{id}/undo-reject', [RequestController::class, 'undoReject']);

    // Timeline and Pending Approvals
    Route::get('/requests/{id}/timeline', [RequestController::class, 'timeline']);
    Route::get('/approvals/pending', [RequestController::class, 'pending']);
});