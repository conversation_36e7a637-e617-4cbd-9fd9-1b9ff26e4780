<?php

use App\Http\Controllers\LineController;
use App\Http\Controllers\LineSpecialitiesController;
use Illuminate\Support\Facades\Route;

Route::get('/line-specialities', [LineSpecialitiesController::class, 'index'])->name('show_all_line_specialities');
Route::get('/get_line_specialities/{line}', [LineSpecialitiesController::class, 'getLineSpecialities'])->name('show_all_line_specialities');
Route::post('/lines/{line}/specialities', [LineSpecialitiesController::class, 'store'])->name('create_line_specialities');
Route::get('/line-specialities/{id}', [LineSpecialitiesController::class, 'show'])->name('show_single_line_specialities');
Route::put('/lines/{line}/speciality/{id}', [LineSpecialitiesController::class, 'update'])->name('edit_line_specialities');
Route::delete('/line-specialities/{id}', [LineSpecialitiesController::class, 'destroy'])->name('delete_line_specialities');

