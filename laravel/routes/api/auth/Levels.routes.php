<?php

use App\Http\Controllers\LevelController;
use Illuminate\Support\Facades\Route;


//resource
Route::get('/levels',[LevelController::class,'index'])->name('show_all_levels');
Route::post('/levels',[LevelController::class,'store'])->name('create_levels');
Route::get('/levels/create',[LevelController::class,'create'])->name('create_levels');
Route::get('/levels/{id}/edit',[LevelController::class,'edit'])->name('edit_levels');
Route::get('/levels/{id}',[LevelController::class,'show'])->name('show_single_levels');
Route::put('/levels/{id}',[LevelController::class,'update'])->name('edit_levels');
Route::delete('/levels/{id}',[LevelController::class,'destroy'])->name('delete_levels');

Route::post('/importlevels', [LevelController::class,'import'])->name('import_levels');
Route::post('/importupdatelevels', [LevelController::class, 'updateByImport'])->name('import_bulk_edit');
Route::get('/downloadlevel/{filename}', [LevelController::class,'export'])->name('download_template_levels');
Route::get('/exportlevels', [LevelController::class,'exportlevels'])->name('export_xlsx_levels');
Route::get('/exportlevelpdf', [LevelController::class,'exportpdf'])->name('export_pdf_levels');
Route::post('/sendmaillevels', [LevelController::class,'sendmail'])->name('export_email_levels');
Route::get('/exportlevelscsv', [LevelController::class,'exportcsv'])->name('export_csv_levels');
