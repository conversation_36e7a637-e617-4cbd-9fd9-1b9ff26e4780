<?php

use App\Http\Controllers\Product\MessageController;
use Illuminate\Support\Facades\Route;


//resource
Route::get('/messages', [MessageController::class, 'index'])->name('show_all_messages');
Route::post('/messages', [MessageController::class, 'store'])->name('create_messages');
Route::get('/messages/{id}', [MessageController::class, 'show'])->name('show_single_messages');
Route::put('/messages/{id}', [MessageController::class, 'update'])->name('edit_messages');
Route::delete('/messages/{id}', [MessageController::class, 'destroy'])->name('delete_messages');

Route::post('/importmessages', [MessageController::class, 'import'])->name('import_messages');
Route::post('/importproductmessages', [MessageController::class, 'importProductMessage'])->name('import_product_messages');
Route::post('/importupdatemessages', [MessageController::class, 'updateByImport'])->name('import_bulk_edit');
Route::get('/downloadmessage/{filename}', [MessageController::class, 'export'])->name('download_template_messages');
Route::get('/exportmessages', [MessageController::class, 'export'])->name('export_xlsx_messages');
Route::get('/exportmessagescsv', [MessageController::class, 'exportcsv'])->name('export_csv_messages');
Route::get('/exportmessagepdf', [MessageController::class, 'exportpdf'])->name('export_pdf_messages');
Route::get('/restoremessage', [MessageController::class, 'restore'])->name('restore_messages');

Route::post('sendmailmessages', [MessageController::class, 'sendmail'])->name('export_email_messages');
