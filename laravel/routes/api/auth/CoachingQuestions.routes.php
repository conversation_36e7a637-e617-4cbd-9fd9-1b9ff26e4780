<?php

//resource

use App\Http\Controllers\Coaching\QuestionController;
use Illuminate\Support\Facades\Route;

Route::get('/coaching/questions',[QuestionController::class,'index'])->name('show_all_questions');
Route::post('/coaching/questions',[QuestionController::class,'store'])->name('create_questions');
Route::get('/coaching/questions/{question}',[QuestionController::class,'show'])->name('show_single_questions');
Route::put('/coaching/questions/{question}',[QuestionController::class,'update'])->name('edit_questions');
Route::delete('/coaching/questions/{question}',[QuestionController::class,'destroy'])->name('delete_questions');

// Tools 
Route::post('/coaching/importquestions', [QuestionController::class,'import'])->name('import_questions');
Route::post('/coaching/importupdatequestions', [QuestionController::class, 'updateByImport'])->name('import_bulk_edit');

Route::get('/coaching/downloadquestion/{filename}', [QuestionController::class,'export'])->name('download_template_questions');
Route::get('/coaching/exportquestions', [QuestionController::class,'exportquestions'])->name('export_xlsx_questions');
Route::get('/coaching/exportquestionscsv', [QuestionController::class,'exportcsv'])->name('export_csv_questions');
Route::get('/coaching/exportquestionpdf', [QuestionController::class,'exportpdf'])->name('export_pdf_questions');

Route::get('/coaching/restorequestion', [QuestionController::class,'restore'])->name('restore_questions');

Route::post('/coaching/sendmailquestions', [QuestionController::class,'sendmail'])->name('export_email_questions');
