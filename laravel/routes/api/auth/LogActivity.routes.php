<?php

use App\Http\Controllers\LogActivityController;
use Illuminate\Support\Facades\Route;

Route::get('/logActivities', [LogActivityController::class, 'logActivity'])->name('show_all_log_activities');
Route::post('/restore/{id}', [LogActivityController::class, 'restore'])->name('restore_log_activities');
Route::post('/force-delete/{id}', [LogActivityController::class, 'forceDelete'])->name('delete_log_activities');
Route::get('/upload-download/{id}', [LogActivityController::class, 'downloadLog'])->name('export_xlsx_log_activities');
