<?php

use App\Http\Controllers\DoctorController;
use App\Http\Controllers\DoctorSocialController;
use Illuminate\Support\Facades\Route;


//resource
Route::post('/doctors/index', [DoctorController::class, 'index'])->name('show_all_doctors');
Route::post('/doctors', [DoctorController::class, 'store'])->name('create_doctors');
Route::get('/doctors/create', [DoctorController::class, 'create'])->name('create_doctors');
Route::get('/doctors/{id}/edit', [DoctorController::class, 'edit'])->name('edit_doctors');
Route::get('/doctors/{id}', [DoctorController::class, 'show'])->name('show_single_doctors');
Route::put('/doctors/{id}', [DoctorController::class, 'update'])->name('edit_doctors');
Route::delete('/doctors/{id}', [DoctorController::class, 'destroy'])->name('delete_doctors');
// import & bulkEdit doctors with doctroSocials
Route::post('/importdoctors', [DoctorController::class, 'import'])->name('import_doctors');
Route::post('/importupdatedoctors', [DoctorController::class, 'updateByImport'])->name('import_bulk_edit');

Route::get('/exportdoctors', [DoctorController::class, 'exportdoctors'])->name('export_xlsx_doctors');
Route::get('/exportdoctorscsv', [DoctorController::class, 'exportcsv'])->name('export_csv_doctors');
Route::get('/exportdoctorpdf', [DoctorController::class, 'exportpdf'])->name('export_pdf_doctors');
Route::post('/sendmaildoctors', [DoctorController::class, 'sendmail'])->name('export_email_doctors');
// Route::get('/restoredoctor', [DoctorController::class, 'restore'])->name('restore_doctors');

Route::get('/doctor-socials', [DoctorSocialController::class, 'index'])->name('show_all_doctor_socials');
Route::get('/get_doctor_socials/{id}', [DoctorSocialController::class, 'getDoctorSocials'])->name('show_all_doctor_socials');
Route::post('/doctor-socials', [DoctorSocialController::class, 'store'])->name('create_doctor_socials');
Route::get('/doctor-socials/{id}', [DoctorSocialController::class, 'show'])->name('show_single_doctor_socials');
Route::put('/doctor-socials/{id}', [DoctorSocialController::class, 'update'])->name('edit_doctor_socials');
Route::delete('/doctor-socials/{id}', [DoctorSocialController::class, 'destroy'])->name('delete_doctor_socials');

Route::post('/doctors/{doctor}/visits/{role}', [DoctorController::class, 'getVisits'])->name('show_single_doctors');
