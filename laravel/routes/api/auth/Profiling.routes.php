<?php

use App\Http\Controllers\ProfilingController;
use App\Http\Controllers\DoctorProfilingController;
use Illuminate\Support\Facades\Route;

//resource
Route::get('/profiling',[ProfilingController::class,'index'])->name('show_all_profiling');
Route::post('/profiling',[ProfilingController::class,'store'])->name('create_profiling');
Route::get('/profiling/create',[ProfilingController::class,'create'])->name('create_profiling');
Route::get('/edit-profiling/{id}',[ProfilingController::class,'edit'])->name('edit_profiling');
Route::get('/profiling/{id}',[ProfilingController::class,'show'])->name('show_single_profiling');
Route::put('/profiling/{id}',[ProfilingController::class,'update'])->name('edit_profiling');
Route::delete('/profiling/{id}',[ProfilingController::class,'destroy'])->name('delete_profiling');
Route::get('/sum-profiling',[ProfilingController::class,'sum'])->name('');


/* Doctor Profiling Routes */
Route::post('/doctor-profiling-list',[DoctorProfilingController::class,'filter'])->name('');
Route::get('/doctor-profiling-list/{line}',[DoctorProfilingController::class,'lineDivisions'])->name('');
Route::post('/doctor-profiling-bricks',[DoctorProfilingController::class,'divisionBricks'])->name('');
Route::post('/doctor-profiling-store',[DoctorProfilingController::class,'store'])->name('');

