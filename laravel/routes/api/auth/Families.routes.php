<?php

use App\Http\Controllers\FamilyController;
use Illuminate\Support\Facades\Route;

//resource
Route::get('/families',[FamilyController::class,'index'])->name('show_all_families');
Route::post('/families',[FamilyController::class,'store'])->name('create_families');
Route::get('/families/create',[FamilyController::class,'create'])->name('create_families');
Route::get('/families/{id}/edit',[FamilyController::class,'edit'])->name('edit_families');
Route::get('/families/{id}',[FamilyController::class,'show'])->name('show_single_families');
Route::put('/families/{id}',[FamilyController::class,'update'])->name('edit_families');
Route::delete('/families/{id}',[FamilyController::class,'destroy'])->name('delete_families');

Route::post('/importfamilies', [FamilyController::class,'import'])->name('import_families');
Route::post('/importupdatefamilies', [FamilyController::class, 'updateByImport'])->name('import_bulk_edit');
Route::get('/downloadfamily/{filename}', [FamilyController::class,'export'])->name('download_template_families');
Route::get('/exportfamilies', [FamilyController::class,'exportfamilies'])->name('export_xlsx_families');
Route::get('/exportfamilypdf', [FamilyController::class,'exportpdf'])->name('export_pdf_families');
Route::post('/sendmailfamilies', [FamilyController::class,'sendmail'])->name('export_email_families');
Route::get('/exportfamiliescsv', [FamilyController::class,'exportcsv'])->name('export_csv_families');
