<?php

use App\Http\Controllers\SocialController;
use Illuminate\Support\Facades\Route;


//resource socials
Route::get('/socials',[SocialController::class,'index'])->name('show_all_socials');
Route::post('/socials',[SocialController::class,'store'])->name('create_socials');
Route::get('/socials/create',[SocialController::class,'create'])->name('create_socials');
Route::get('/socials/{id}/edit',[SocialController::class,'edit'])->name('edit_socials');
Route::get('/socials/{id}',[SocialController::class,'show'])->name('show_single_socials');
Route::put('/socials/{id}',[SocialController::class,'update'])->name('edit_socials');
Route::delete('/socials/{id}',[SocialController::class,'destroy'])->name('delete_socials');

// Route::post('/importsocials', [SocialController::class,'import'])->name('import_socials');
// Route::get('/downloadsocial/{filename}', [SocialController::class,'export'])->name('download_template_socials');
// Route::get('/exportsocials', [SocialController::class,'exportsocials'])->name('export_xlsx_socials');
// Route::get('/exportsocialpdf', [SocialController::class,'exportpdf'])->name('export_pdf_socials');
// Route::post('/sendmailsocials', [SocialController::class,'sendmail'])->name('export_email_socials');
// Route::get('/exportsocialscsv', [SocialController::class,'exportcsv'])->name('export_csv_socials');
