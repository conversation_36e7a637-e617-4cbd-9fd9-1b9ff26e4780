<?php

use App\Http\Controllers\LineController;
use App\Http\Controllers\PositionController;
use App\Http\Controllers\PositionManagersController;
use App\Http\Controllers\UnLinkedUserPositionController;
use App\Http\Controllers\UserPositionController;
use App\Http\Controllers\UserPositionSettingController;
use Illuminate\Support\Facades\Route;


//resource positions
Route::get('/positions',[PositionController::class,'index'])->name('show_all_positions');
Route::post('/positions',[PositionController::class,'store'])->name('create_positions');
Route::get('/positions/{id}',[PositionController::class,'show'])->name('show_single_positions');
Route::put('/positions/{id}',[PositionController::class,'update'])->name('edit_positions');
Route::delete('/positions/{id}',[PositionController::class,'destroy'])->name('delete_positions');


//resource user positions
Route::get('/user-positions',[UserPositionController::class,'index'])->name('show_all_employee_positions');
Route::post('/user-positions',[UserPositionController::class,'store'])->name('create_employee_positions');
Route::get('/user-positions/{id}',[UserPositionController::class,'show'])->name('show_single_employee_positions');
Route::put('/user-positions/{id}',[UserPositionController::class,'update'])->name('edit_employee_positions');
Route::delete('/user-positions/{id}',[UserPositionController::class,'destroy'])->name('delete_employee_positions');

Route::get('unlinked-positions', [UnLinkedUserPositionController::class,'getUnlinkedPositions'])->name(''); //Todo: permissions not implemented yet
Route::get('unlinked-users', [UnLinkedUserPositionController::class,'getUnLinkedUsers'])->name(''); //Todo: permissions not implemented yet

//resource position manager
Route::get('/position-managers',[PositionManagersController::class,'index'])->name('show_all_position_managers');
Route::post('/position-managers',[PositionManagersController::class,'store'])->name('create_position_managers');
Route::get('/position-managers/{id}',[PositionManagersController::class,'show'])->name('show_single_position_managers');
Route::put('/position-managers/{id}',[PositionManagersController::class,'update'])->name('edit_position_managers');
Route::delete('/position-managers/{id}',[PositionManagersController::class,'destroy'])->name('delete_position_managers');

Route::get('line-positions', [LineController::class,'LineDetailsPositions'])->name(''); //Todo: permissions not implemented yet

//resource position settings
Route::get('/position-settings',[UserPositionSettingController::class,'index'])->name('show_all_position_settings');
Route::post('/position-settings',[UserPositionSettingController::class,'store'])->name('create_position_settings');
Route::get('/position-settings/{id}',[UserPositionSettingController::class,'show'])->name('show_single_position_settings');
Route::put('/position-settings/{id}',[UserPositionSettingController::class,'update'])->name('edit_position_settings');
Route::delete('/position-settings/{user_position}/lines/{line}',[UserPositionSettingController::class,'destroy'])->name('delete_position_settings');
