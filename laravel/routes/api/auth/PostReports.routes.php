<?php

use App\Http\Controllers\CallRateReportController;
use App\Http\Controllers\EmployeePerformanceReportController;
use App\Http\Controllers\FrequencyReportController;
use App\Http\Controllers\ManagersReportController;
use App\Http\Controllers\PostKpisReportController;
use App\Http\Controllers\PostVisitKpisController;
use App\Http\Controllers\VisitCoverageReportController;
use App\Http\Controllers\VisitStatisticsController;
use Illuminate\Support\Facades\Route;

Route::post('/post-kpis', [PostKpisReportController::class, 'post'])->name('');
Route::post('/post-coverage', [VisitCoverageReportController::class, 'post'])->name('');
Route::post('/post-visit-statistics', [VisitStatisticsController::class, 'post'])->name('');
Route::post('/post-employee-performance', [EmployeePerformanceReportController::class, 'post'])->name('');
Route::post('/post-call-rate', [CallRateReportController::class, 'post'])->name('');
Route::post('/fast-post-employee-performance', [EmployeePerformanceReportController::class, 'fastPost'])->name('');
Route::post('/post-frequency', [FrequencyReportController::class, 'post'])->name('');
Route::post('/post-sop', [ManagersReportController::class, 'post'])->name('');
Route::get('/post-data', [PostVisitKpisController::class, 'index'])->name('');
Route::post('/import/post', [PostVisitKpisController::class, 'import'])->name('');









              