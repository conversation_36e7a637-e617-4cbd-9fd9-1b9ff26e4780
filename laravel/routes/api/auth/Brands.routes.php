<?php

use App\Http\Controllers\BrandController;
use Illuminate\Support\Facades\Route;


//resource
Route::get('/brands', [BrandController::class, 'index'])->name('show_all_brands');
Route::post('/brands', [BrandController::class, 'store'])->name('create_brands');
Route::get('/brands/create', [BrandController::class, 'create'])->name('create_brands');
Route::get('/brands/{id}/edit', [BrandController::class, 'edit'])->name('edit_brands');
Route::get('/brands/{id}', [BrandController::class, 'show'])->name('show_single_brands');
Route::put('/brands/{id}', [BrandController::class, 'update'])->name('edit_brands');
Route::delete('/brands/{id}', [BrandController::class, 'destroy'])->name('delete_brands');

Route::post('/importbrands', [BrandController::class, 'import'])->name('import_brands');
Route::post('/importupdatebrands', [BrandController::class, 'updateByImport'])->name('import_bulk_edit');
Route::get('/downloadbrand/{filename}', [BrandController::class, 'export'])->name('download_template_brands');
Route::get('/exportbrands', [BrandController::class, 'exportbrands'])->name('export_xlsx_brands');
Route::get('/exportbrandscsv', [BrandController::class, 'exportcsv'])->name('export_csv_brands');
Route::get('/exportbrandpdf', [BrandController::class, 'exportpdf'])->name('export_pdf_brands');
// Route::post('/sendmailbrands', [BrandController::class,'sendmail'])->name('export_email_brands');
Route::get('/restorebrand', [BrandController::class, 'restore'])->name('restore_brands');

Route::post('sendmailbrands', [BrandController::class, 'sendmail'])->name('export_email_brands');
