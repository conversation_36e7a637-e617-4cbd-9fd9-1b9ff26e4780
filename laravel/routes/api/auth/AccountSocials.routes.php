<?php

use App\Http\Controllers\AccountController;
use App\Http\Controllers\AccountSocialController;
use Illuminate\Support\Facades\Route;

Route::get('/account-socials', [AccountSocialController::class, 'index'])->name('show_all_account_socials');
Route::get('/get_account_socials/{account}', [AccountSocialController::class, 'getAccountSocials'])->name('show_all_account_socials');
Route::post('/account-socials', [AccountSocialController::class, 'store'])->name('create_account_socials');
Route::get('/account-socials/{id}', [AccountSocialController::class, 'show'])->name('show_single_account_socials');
Route::put('/account-socials/{id}', [AccountSocialController::class, 'update'])->name('edit_account_socials');
Route::delete('/account-socials/{id}', [AccountSocialController::class, 'destroy'])->name('delete_account_socials');