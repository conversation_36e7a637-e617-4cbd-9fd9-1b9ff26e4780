<?php

use App\Http\Controllers\AccountController;
use App\Http\Controllers\NewAccountDoctorController;
use App\Http\Controllers\WhatsAppController;
use Illuminate\Support\Facades\Route;

// Route::get('/get_account_doctors/{id}', [AccountController::class,'getAccountDoctors'])->name('show_all_account_doctors');
// Route::post('/add_account_doctor', [AccountController::class,'storeAccountDoctor'])->name('create_account_doctors');
// Route::delete('/delete_account_doctor/{id}', [AccountController::class,'deleteAccountDoctor'])->name('delete_account_doctors');
// Route::put('/update_account_doctor/{id}', [AccountController::class,'updateAccountDoctor'])->name('edit_account_doctors');
// Route::get('/showEditAccountDoctors/{id}', [AccountController::class,'showEditAccountDoctors'])->name('edit_account_doctors');


//test

Route::get('/account_doctors', [NewAccountDoctorController::class,'index'])->name('show_all_account_doctors');
Route::get('/getaccountdoctors/{account}', [NewAccountDoctorController::class,'getAccountDoctors'])->name('show_all_account_doctors');
Route::post('/account_doctors', [NewAccountDoctorController::class,'store'])->name('create_account_doctors');
Route::get('/account_doctors/{id}', [NewAccountDoctorController::class,'show'])->name('show_single_account_doctors');
Route::put('/account_doctors/{id}', [NewAccountDoctorController::class,'update'])->name('edit_account_doctors');
Route::delete('/account_doctors/{id}', [NewAccountDoctorController::class,'destroy'])->name('delete_account_doctors');


Route::get('/whatsapp-send', [WhatsAppController::class, 'send']);
Route::get('/sms-send', [WhatsAppController::class, 'sendSms']);