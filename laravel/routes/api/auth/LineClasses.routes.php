<?php

use App\Http\Controllers\LineClassesController;
use Illuminate\Support\Facades\Route;

Route::get('/line-classes', [LineClassesController::class,'index'])->name('show_all_line_classes');
Route::get('/get_line_classes/{line}', [LineClassesController::class,'getLineClasses'])->name('show_all_line_classes');
Route::post('/lines/{line}/classes', [LineClassesController::class,'store'])->name('create_line_classes');
Route::get('/line-classes/{id}', [LineClassesController::class,'show'])->name('show_single_line_classes');
Route::put('/lines/{line}/class/{id}', [LineClassesController::class,'update'])->name('edit_line_classes');
Route::delete('/line-classes/{id}', [LineClassesController::class,'destroy'])->name('delete_line_classes');
