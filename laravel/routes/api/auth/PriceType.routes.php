<?php

use App\Http\Controllers\PricetypeController;
use Illuminate\Support\Facades\Route;


//resource price type
Route::get('/price_types',[PricetypeController::class,'index'])->name('show_all_pricetypes');
Route::post('/price_types',[PricetypeController::class,'store'])->name('create_pricetypes');
Route::get('/price_types/create',[PricetypeController::class,'create'])->name('create_pricetypes');
Route::get('/price_types/{id}/edit',[PricetypeController::class,'edit'])->name('edit_pricetypes');
Route::get('/price_types/{id}',[PricetypeController::class,'show'])->name('show_single_pricetypes');
Route::put('/price_types/{id}',[PricetypeController::class,'update'])->name('edit_pricetypes');
Route::delete('/price_types/{id}',[PricetypeController::class,'destroy'])->name('delete_pricetypes');

Route::post('/importprice_types', [PricetypeController::class,'import'])->name('import_pricetypes');
Route::post('/importupdatepricetypes', [PricetypeController::class, 'updateByImport'])->name('import_bulk_edit');
Route::get('/downloadprice_type/{filename}', [PricetypeController::class,'export'])->name('download_template_pricetypes');
Route::get('/exportprice_types', [PricetypeController::class,'exportprice_types'])->name('export_xlsx_pricetypes');
Route::get('/exportprice_typepdf', [PricetypeController::class,'exportpdf'])->name('export_pdf_pricetypes');
Route::post('/sendmailprice_types', [PricetypeController::class,'sendmail'])->name('export_email_pricetypes');
Route::get('/exportprice_typescsv', [PricetypeController::class,'exportcsv'])->name('export_csv_pricetypes');
