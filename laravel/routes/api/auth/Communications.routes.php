<?php

use App\Announcement;
use App\Http\Controllers\AnnouncementController;
use App\Http\Controllers\TasksController;
use Illuminate\Support\Facades\Route;

//resource tasks
Route::get('/tasks',[TasksController::class,'index'])->name('show_all_tasks');
Route::post('/tasks',[TasksController::class,'store'])->name('create_tasks');
Route::get('/tasks/{id}',[TasksController::class,'show'])->name('show_single_tasks');
Route::put('/tasks/{id}',[TasksController::class,'update'])->name('edit_tasks');
Route::delete('/tasks/{id}',[TasksController::class,'destroy'])->name('delete_tasks');

Route::get('/tasks-lines',[TasksController::class,'taskLines'])->name('');
Route::get('/tasks-users/{line_id}',[TasksController::class,'taskUsers'])->name('');

//resource announcements
Route::get('/announcements',[AnnouncementController::class,'index'])->name('show_all_announcements');
Route::post('/announcements',[AnnouncementController::class,'store'])->name('create_announcements');
Route::get('/announcements/{id}',[AnnouncementController::class,'show'])->name('show_single_announcements');
Route::put('/announcements/{id}',[AnnouncementController::class,'update'])->name('edit_announcements');
Route::delete('/announcements/{id}',[AnnouncementController::class,'destroy'])->name('delete_announcements');

Route::get('/announcements-lines',[AnnouncementController::class,'announcementLines'])->name('');
Route::post('/announcements-users',[AnnouncementController::class,'announcementUsers'])->name('');
