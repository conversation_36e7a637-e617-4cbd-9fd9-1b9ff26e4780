<?php


use App\Http\Controllers\Coaching\CategoryQuestionController;
use Illuminate\Support\Facades\Route;

Route::get('/coaching/categoryquestions',[CategoryQuestionController::class,'index'])->name('show_all_categoryquestions');
Route::post('/coaching/categoryquestions',[CategoryQuestionController::class,'store'])->name('create_categoryquestions');
Route::get('/coaching/categoryquestions/{categoryQuestion}',[CategoryQuestionController::class,'show'])->name('show_single_categoryquestions');
Route::put('/coaching/categoryquestions/{categoryQuestion}',[CategoryQuestionController::class,'update'])->name('edit_categoryquestions');
Route::delete('/coaching/categoryquestions/{categoryQuestion}',[CategoryQuestionController::class,'destroy'])->name('delete_categoryquestions');


// Tools 
Route::post('/coaching/importcategoryquestions', [CategoryQuestionController::class,'import'])->name('import_categoryquestions');
Route::get('/coaching/downloadcategoryquestion/{filename}', [CategoryQuestionController::class,'export'])->name('download_template_categoryquestions');
Route::get('/coaching/exportcategoryquestions', [CategoryQuestionController::class,'exportcategoryquestions'])->name('export_xlsx_categoryquestions');
Route::get('/coaching/exportcategoryquestionscsv', [CategoryQuestionController::class,'exportcsv'])->name('export_csv_categoryquestions');
Route::get('/coaching/exportcategoryquestionpdf', [CategoryQuestionController::class,'exportpdf'])->name('export_pdf_categoryquestions');

Route::get('/coaching/restorecategoryquestion', [CategoryQuestionController::class,'restore'])->name('restore_categoryquestions');

Route::post('/coaching/sendmailcategoryquestions', [CategoryQuestionController::class,'sendmail'])->name('export_email_categoryquestions');
