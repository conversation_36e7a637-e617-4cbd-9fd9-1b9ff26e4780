<?php

use App\Http\Controllers\AccountRequestController;
use App\Http\Controllers\ActiveInActiveApprovalController;
use App\Http\Controllers\BudgetSetupController;
use App\Http\Controllers\BudgetStatisticsController;
use App\Http\Controllers\CategoryController;
use App\Http\Controllers\CategoryTypeController;
use App\Http\Controllers\CoachingSettingController;
use App\Http\Controllers\CommercialCostController;
use App\Http\Controllers\CommercialDataController;
use App\Http\Controllers\CommercialPharmacySettingController;
use App\Http\Controllers\CommercialRequestController;
use App\Http\Controllers\CommercialSettingController;
use App\Http\Controllers\CustodyController;
use App\Http\Controllers\ExpenseApprovalController;
use App\Http\Controllers\ExpenseController;
use App\Http\Controllers\ExpenseDataController;
use App\Http\Controllers\ExpenseDoubleLocationController;
use App\Http\Controllers\ExpensePerLocationController;
use App\Http\Controllers\ExpensePerLocationPriceController;
use App\Http\Controllers\ExpensePerLocationPriceFactorController;
use App\Http\Controllers\ExpensePerLocationSettingController;
use App\Http\Controllers\ExpenseTypesController;
use App\Http\Controllers\ExpenseMealsController;
use App\Http\Controllers\ExpenseSettingController;
use App\Http\Controllers\InActiveApprovalController;
use App\Http\Controllers\KilometersAverageController;
use App\Http\Controllers\PersonalRequestsController;
use App\Http\Controllers\PersonalRequestTypesController;
use App\Http\Controllers\PromotionalMaterialTypeController;
use App\Http\Controllers\PublicHolidayController;
use App\Http\Controllers\LineController;
use App\Http\Controllers\MaterialController;
use App\Http\Controllers\MaterialDetailController;
use App\Http\Controllers\MaterialStockController;
use App\Http\Controllers\MaterialVendorController;
use App\Http\Controllers\PaidRequestController;
use App\Http\Controllers\PartialPaymentController;
use App\Http\Controllers\PaymentMethodController;
use App\Http\Controllers\RequestFeedbackController;
use App\Http\Controllers\RequestTypesController;
use App\Http\Controllers\ScientificOfficesController;
use App\Http\Controllers\ServiceCompleteReportController;
use App\Http\Controllers\VacationBalanceController;
use App\Http\Controllers\VacationController;
use App\Http\Controllers\VacationSettingController;
use App\Http\Controllers\VacationTypeController;
use App\Http\Requests\CommercialRequestRequest;
use App\Models\CommercialPharmacySetting;
use App\Models\CommercialRequest\ServiceComplete;
use App\Models\Expenses\Types\ExpenseType;
use App\Models\MaterialVendor;
use Illuminate\Support\Facades\Route;

//resource holidays
Route::get('/publicholidays', [PublicHolidayController::class, 'index'])->name('show_all_public_holidays');
Route::post('/publicholidays', [PublicHolidayController::class, 'store'])->name('create_public_holidays');
Route::get('/publicholidays/create', [PublicHolidayController::class, 'create'])->name('create_public_holidays');
Route::get('/publicholidays/{id}/edit', [PublicHolidayController::class, 'edit'])->name('edit_public_holidays');
Route::get('/publicholidays/{id}', [PublicHolidayController::class, 'show'])->name('show_single_public_holidays');
Route::put('/publicholidays/{id}', [PublicHolidayController::class, 'update'])->name('edit_public_holidays');
Route::delete('/publicholidays/{id}', [PublicHolidayController::class, 'destroy'])->name('delete_public_holidays');


//resource plan settings
Route::get('/vacationsettings', [VacationSettingController::class, 'index'])->name('show_all_plan_visit_settings');
Route::post('/vacationsettings', [VacationSettingController::class, 'store'])->name('create_plan_visit_settings');
Route::get('/vacationsettings/create', [VacationSettingController::class, 'create'])->name('create_plan_visit_settings');
Route::get('/vacationsettings/{id}/edit', [VacationSettingController::class, 'edit'])->name('edit_plan_visit_settings');
Route::get('/vacationsettings/key/{key}', [VacationSettingController::class, 'ByKey'])->name(''); //Todo permission is not implemented
Route::get('/vacationsettings/{id}', [VacationSettingController::class, 'show'])->name('show_single_plan_visit_settings');
Route::put('/vacationsettings/{id}', [VacationSettingController::class, 'update'])->name('edit_plan_visit_settings');





//resource vacation types
Route::get('/vacationtypes', [VacationTypeController::class, 'index'])->name('show_all_vacation_types');
Route::post('/vacationtypes', [VacationTypeController::class, 'store'])->name('create_vacation_types');
Route::get('/vacationtypes/create', [VacationTypeController::class, 'create'])->name('create_vacation_types');
Route::get('/vacationtypes/{id}/edit', [VacationTypeController::class, 'edit'])->name('edit_vacation_types');
Route::get('/vacationtypes/{id}', [VacationTypeController::class, 'show'])->name('show_single_vacation_types');
Route::put('/vacationtypes/{id}', [VacationTypeController::class, 'update'])->name('edit_vacation_types');
Route::delete('/vacationtypes/{id}', [VacationTypeController::class, 'destroy'])->name('delete_vacation_types');


//resource vacation Balance
Route::get('/vacation-balance', [VacationBalanceController::class, 'index'])->name('show_all_vacation_balances');
Route::post('/vacation-balance', [VacationBalanceController::class, 'store'])->name('create_vacation_balances');
Route::get('/vacation-balance/{id}', [VacationBalanceController::class, 'show'])->name('show_single_vacation_balances');
Route::put('/vacation-balance/{id}', [VacationBalanceController::class, 'update'])->name('edit_vacation_balances');
Route::delete('/vacation-balance/{id}', [VacationBalanceController::class, 'destroy'])->name('delete_vacation_balances');
Route::post('/import-vacation-balance', [VacationBalanceController::class, 'import'])->name('import_vacation_balances');

Route::get('/vacation-setting', [VacationController::class, 'setting'])->name('');
//resource Vacations
Route::post('/vacations/index', [VacationController::class, 'index'])->name('show_all_vacations');
Route::post('/vacations', [VacationController::class, 'store'])->name('create_vacations');
Route::get('/vacations/create', [VacationController::class, 'create'])->name('create_vacations');
Route::get('/vacations/{id}/edit', [VacationController::class, 'edit'])->name('edit_vacations');
Route::get('/vacations/{id}', [VacationController::class, 'show'])->name('show_single_vacations');
Route::put('/vacations/{id}', [VacationController::class, 'update'])->name('edit_vacations');
Route::delete('/vacations/{id}', [VacationController::class, 'destroy'])->name('delete_vacations');

Route::get('/start-vacation/{id}', [VacationController::class, 'vacationDates'])->name('');
Route::get('/get-vacation-dates', [VacationController::class, 'minDate'])->name('');


Route::post('/vacation-feedback/{id}', [VacationController::class, 'feedback'])->name('vacation_request_feedback');

Route::post('/vacation-approval-data', [VacationController::class, 'vacationFlow'])->name(''); //TODO:permission is not implemented yet
Route::post('/vacation-approval-reset', [VacationController::class, 'resetApprovals'])->name(''); //TODO:permission is not implemented yet
Route::get('/vacation-policies', [VacationController::class, 'policies'])->name('');



Route::get('/exportvacations', [VacationController::class, 'exportvacations'])->name('export_xlsx_vacations');
Route::get('/exportvacations-csv', [VacationController::class, 'exportvacationsCsv'])->name('export_csv_vacations');
Route::get('/exportvacations-pdf', [VacationController::class, 'exportvacationspdf'])->name('export_pdf_vacations');
Route::post('/send-mailvacations', [VacationController::class, 'sendvacationsmail'])->name('export_email_vacations');

//resource Personal Request
Route::get('/personal-requests', [PersonalRequestsController::class, 'index'])->name('show_all_personal_requests');
Route::post('/personal-requests', [PersonalRequestsController::class, 'store'])->name('create_personal_requests');
Route::get('/personal-requests/{id}', [PersonalRequestsController::class, 'show'])->name('show_single_personal_requests');
Route::put('/personal-requests/{id}', [PersonalRequestsController::class, 'update'])->name('edit_personal_requests');
Route::delete('/personal-requests/{id}', [PersonalRequestsController::class, 'destroy'])->name('delete_personal_requests');

Route::get('/personal-lines', [PersonalRequestsController::class, 'personalLines'])->name(''); //TODO:permission is not implemented yet


//resource Personal Request Types
Route::get('/personal-request-types', [PersonalRequestTypesController::class, 'index'])->name('show_all_personal_request_types');
Route::post('/personal-request-types', [PersonalRequestTypesController::class, 'store'])->name('create_personal_request_types');
Route::get('/personal-request-types/{id}', [PersonalRequestTypesController::class, 'show'])->name('show_single_personal_request_types');
Route::put('/personal-request-types/{id}', [PersonalRequestTypesController::class, 'update'])->name('edit_personal_request_types');
Route::delete('/personal-request-types/{id}', [PersonalRequestTypesController::class, 'destroy'])->name('delete_personal_request_types');

//resource Commercial Costs Types
Route::get('/costs', [CommercialCostController::class, 'index'])->name('show_all_cost_types');
Route::post('/costs', [CommercialCostController::class, 'store'])->name('create_cost_types');
Route::get('/costs/{id}', [CommercialCostController::class, 'show'])->name('show_single_cost_types');
Route::put('/costs/{id}', [CommercialCostController::class, 'update'])->name('edit_cost_types');
Route::delete('/costs/{id}', [CommercialCostController::class, 'destroy'])->name('delete_cost_types');

// categories

Route::get('/categories', [CategoryController::class, 'index'])->name('show_all_commercial_categories');
Route::post('/categories', [CategoryController::class, 'store'])->name('create_commercial_categories');
Route::get('/categories/{id}', [CategoryController::class, 'show'])->name('show_single_commercial_categories');
Route::put('/categories/{id}', [CategoryController::class, 'update'])->name('edit_commercial_categories');
Route::delete('/categories/{id}', [CategoryController::class, 'destroy'])->name('delete_commercial_categories');
Route::get('/categories/{id}', [CategoryController::class, 'getCategoryWithPayment'])->name('');
Route::get('/get-categories-payments', [CategoryController::class, 'checkCategoriesWithPayments'])->name('');
// payment Methods

Route::get('/payment-methods', [PaymentMethodController::class, 'index'])->name('show_all_commercial_payment_methods');
Route::post('/payment-methods', [PaymentMethodController::class, 'store'])->name('create_commercial_payment_methods');
Route::get('/payment-methods/{id}', [PaymentMethodController::class, 'show'])->name('show_single_commercial_payment_methods');
Route::put('/payment-methods/{id}', [PaymentMethodController::class, 'update'])->name('edit_commercial_payment_methods');
Route::delete('/payment-methods/{id}', [PaymentMethodController::class, 'destroy'])->name('delete_commercial_payment_methods');

// categories_types

Route::get('/categories_types', [CategoryTypeController::class, 'index'])->name('show_all_commercial_categories_types');
Route::post('/categories_types', [CategoryTypeController::class, 'store'])->name('create_commercial_categories_types');
Route::get('/categories_types/{id}', [CategoryTypeController::class, 'show'])->name('show_single_commercial_categories_types');
Route::put('/categories_types/{id}', [CategoryTypeController::class, 'update'])->name('edit_commercial_categories_types');
Route::delete('/categories_types/{id}', [CategoryTypeController::class, 'destroy'])->name('delete_commercial_categories_types');


//resource Request Types
Route::get('/request-types', [RequestTypesController::class, 'index'])->name('show_all_request_types');
Route::post('/request-types', [RequestTypesController::class, 'store'])->name('create_request_types');
Route::get('/request-types/{id}', [RequestTypesController::class, 'show'])->name('show_single_request_types');
Route::put('/request-types/{id}', [RequestTypesController::class, 'update'])->name('edit_request_types');
Route::delete('/request-types/{id}', [RequestTypesController::class, 'destroy'])->name('delete_request_types');

// Commercial Data
Route::get('/commercial-tabs', [CommercialDataController::class, 'commercialTabs'])->name(''); //TODO:permission is not implemented yet
Route::get('/chosen-commercial-tabs', [CommercialDataController::class, 'chosenTabs'])->name(''); //TODO:permission is not implemented yet
Route::post('/save-commercial-tabs', [CommercialDataController::class, 'saveCommercialTabs'])->name(''); //TODO:permission is not implemented yet
Route::get('/min-date', [CommercialDataController::class, 'minDate'])->name(''); //TODO:permission is not implemented yet
Route::get('/commercial-lines', [CommercialDataController::class, 'lines'])->name(''); //TODO:permission is not implemented yet
Route::get('/commercial-lines-products', [CommercialDataController::class, 'linesWithBrands'])->name(''); //TODO:permission is not implemented yet
Route::get('/commercial-request-types', [CommercialDataController::class, 'requestTypes'])->name(''); //TODO:permission is not implemented yet
Route::get('/commercial-speaker-types', [CommercialDataController::class, 'speakerTypes'])->name(''); //TODO:permission is not implemented yet
Route::get('/commercial-speakers', [CommercialDataController::class, 'speakers'])->name(''); //TODO:permission is not implemented yet
Route::post('/category/{id}/types', [CommercialDataController::class, 'categoriesCosts'])->name(''); //TODO:permission is not implemented yet
Route::get('/sub-type/{id}', [CommercialDataController::class, 'categoriesCostPrice'])->name(''); //TODO:permission is not implemented yet
Route::post('/commercial-divisions', [CommercialDataController::class, 'divisions'])->name(''); //TODO:permission is not implemented yet
Route::post('/commercial-products', [CommercialDataController::class, 'products'])->name(''); //TODO:permission is not implemented yet
Route::post('/get-commercial-pharmacies', [CommercialDataController::class, 'getPharmacies'])->name(''); //TODO:permission is not implemented yet
Route::get('/get-commercial-pharmacies-settings', [CommercialDataController::class, 'settingCommercialPharmacies'])->name(''); //TODO:permission is not implemented yet
Route::post('/commercial-users', [CommercialDataController::class, 'users'])->name(''); //TODO:permission is not implemented yet
Route::post('/commercial-doctors/index', [CommercialDataController::class, 'doctors'])->name(''); //TODO:permission is not implemented yet
Route::post('/single-doctors', [CommercialDataController::class, 'singleDoctor'])->name(''); //TODO:permission is not implemented yet
Route::get('/commercial-costs', [CommercialDataController::class, 'costTypes'])->name(''); //TODO:permission is not implemented yet
Route::post('/commercial-request/index', [CommercialRequestController::class, 'index'])->name('show_all_commercial_requests');
Route::post('/commercial-request/index2', [CommercialRequestController::class, 'index2'])->name('show_all_commercial_requests');
Route::post('/commercial-request', [CommercialRequestController::class, 'store'])->name('create_commercial_requests');
Route::get('/commercial-request/{id}', [CommercialRequestController::class, 'show'])->name('show_single_commercial_requests');
Route::get('/edit-cost-elements/{commercial}', [CommercialRequestController::class, 'editCostElements'])->name('edit_commercial_requests');
Route::get('/edit-out_of_list/{commercial}', [CommercialRequestController::class, 'editOutOfList'])->name('edit_commercial_requests');
Route::delete('/commercial-request/{id}', [CommercialRequestController::class, 'destroy'])->name('delete_commercial_requests');
Route::post('/commercial-save-attachments', [CommercialRequestController::class, 'attachments'])->name(''); //TODO:permission is not implemented yet
Route::post('/doctor-costs', [CommercialRequestController::class, 'doctorTypes'])->name(''); //TODO:permission is not implemented yet
Route::post('/load-doctor-data', [CommercialRequestController::class, 'getDoctors'])->name(''); //TODO:permission is not implemented yet
Route::post('/load-doctor-pharmacies', [CommercialRequestController::class, 'getDoctorPharmacies'])->name(''); //TODO:permission is not implemented yet
Route::post('/doctor-products', [CommercialRequestController::class, 'doctorProducts'])->name(''); //TODO:permission is not implemented yet
Route::get('/doctor-months/{commercial}', [CommercialRequestController::class, 'doctorMonth'])->name(''); //TODO:permission is not implemented yet
Route::get('/edit-commercial-request/{id}', [CommercialRequestController::class, 'getEditedData'])->name('edit_commercial_requests');
Route::post('/get-commercial-products/{id}', [CommercialRequestController::class, 'getEditedProducts'])->name('edit_commercial_requests');
Route::post('/get-commercial-doctors/index', [CommercialRequestController::class, 'getEditedDoctors'])->name('edit_commercial_requests');
Route::post('/get-commercial-users/{id}', [CommercialRequestController::class, 'getUsers'])->name('edit_commercial_requests');
Route::put('/update-commercial-request', [CommercialRequestController::class, 'update'])->name('edit_commercial_requests'); //TODO:permission is not implemented yet
Route::delete('/commercial-attach/{id}', [CommercialRequestController::class, 'removeCommercialAttach'])->name('edit_commercial_requests'); //TODO:permission is not implemented yet
Route::post('/get-users-data', [CommercialRequestController::class, 'getCommercialEmployee'])->name(''); //TODO:permission is not implemented yet
Route::post('/get-doctors-data', [CommercialRequestController::class, 'getCommercialDoctor'])->name(''); //TODO:permission is not implemented yet
Route::post('/get-products-data', [CommercialRequestController::class, 'getCommercialProducts'])->name(''); //TODO:permission is not implemented yet
Route::post('/commercial-approval-data', [CommercialRequestController::class, 'commercialFlow'])->name(''); //TODO:permission is not implemented yet
Route::post('/commercial-feedback/{id}', [CommercialRequestController::class, 'feedback'])->name('commercial_request_feedback');
Route::get('/commercial-policies', [CommercialRequestController::class, 'policies'])->name(''); //TODO:permission is not implemented yet
Route::post('/show-roi', [CommercialDataController::class, 'showROI'])->name(''); //TODO:permission is not implemented yet
Route::get('/get-budget-commercial', [BudgetStatisticsController::class, 'getBudgets'])->name(''); //TODO:permission is not implemented yet
Route::post('/import/commercial/doctors', [CommercialRequestController::class, 'import'])->name(''); //TODO:permission is not implemented yet
Route::post('/get-commercial-approvals', [CommercialRequestController::class, 'getApprovalUser'])->name(''); //TODO:permission is not implemented yet
Route::delete('/commercial/approval/detail/{id}', [CommercialRequestController::class, 'deleteApprovalUser'])->name(''); //TODO:permission is not implemented yet
Route::post('/update-commercial-flow', [CommercialRequestController::class, 'updateApprovalUser'])->name(''); //TODO:permission is not implemented yet
// Commercial Settings
Route::get('/commercial-settings', [CommercialSettingController::class, 'index'])->name('show_all_commercial_settings');
Route::get('/commercial-settings/{id}', [CommercialSettingController::class, 'show'])->name('show_single_commercial_settings');
Route::put('/commercial-settings/{id}', [CommercialSettingController::class, 'update'])->name('edit_commercial_settings');
// Custody
Route::get('/custody', [CustodyController::class, 'index'])->name('show_all_commercial_custodies');
Route::get('/custody-users', [CustodyController::class, 'users'])->name('');
Route::post('/custody', [CustodyController::class, 'store'])->name('create_commercial_custodies');
Route::get('/custody/{id}', [CustodyController::class, 'show'])->name('show_single_commercial_custodies');
Route::put('/custody/{id}', [CustodyController::class, 'update'])->name('edit_commercial_custodies');
Route::delete('/custody/{id}', [CustodyController::class, 'destroy'])->name('delete_commercial_custodies');

Route::post('/get-custody-user-balance', [CustodyController::class, 'getUserBalance'])->name('');
Route::get('/get-custody-lines', [CustodyController::class, 'getlines'])->name('');
Route::get('/get-custody-lines-data/{line}', [CustodyController::class, 'getlineUsers'])->name('');

// Expense Types
Route::get('/expense-type', [ExpenseTypesController::class, 'index'])->name('show_all_expense_types');
Route::post('/expense-type', [ExpenseTypesController::class, 'store'])->name('create_expense_types');
Route::get('/expense-type/{id}', [ExpenseTypesController::class, 'show'])->name('show_single_expense_types');
Route::put('/expense-type/{id}', [ExpenseTypesController::class, 'update'])->name('edit_expense_types');
Route::delete('/expense-type/{id}', [ExpenseTypesController::class, 'destroy'])->name('delete_expense_types');

// Expense Meals
Route::get('/expense-meal', [ExpenseMealsController::class, 'index'])->name('show_all_expense_types');
Route::get('/expense-meal/{id}', [ExpenseMealsController::class, 'show'])->name('show_single_expense_types');
Route::put('/expense-meal/{id}', [ExpenseMealsController::class, 'update'])->name('edit_expense_types');
// Route::delete('/expense-meal/{id}', [ExpenseMealsController::class, 'destroy'])->name('delete_expense_types');
Route::get('/get-roles', [ExpenseMealsController::class, 'getRoles'])->name('');


// Expenses
Route::get('/expense-line', [ExpenseDataController::class, 'lines'])->name(''); //TODO:permission is not implemented yet
Route::get('/expense-line-data/{line_id}', [ExpenseDataController::class, 'getLineData'])->name(''); //TODO:permission is not implemented yet
Route::post('/get-expense-products', [ExpenseDataController::class, 'getProducts'])->name(''); //TODO:permission is not implemented yet
Route::get('/expense-types', [ExpenseDataController::class, 'types'])->name(''); //TODO:permission is not implemented yet
Route::get('/expense-meals', [ExpenseDataController::class, 'meals'])->name(''); //TODO:permission is not implemented yet
Route::get('/expense-distances', [ExpenseDataController::class, 'distances'])->name(''); //TODO:permission is not implemented yet
Route::get('/expense-overnights', [ExpenseDataController::class, 'overNights'])->name(''); //TODO:permission is not implemented yet
Route::post('/expenses/index', [ExpenseController::class, 'index'])->name('show_all_expenses');
Route::post('/expenses', [ExpenseController::class, 'store'])->name('create_expenses');
Route::put('/expenses/{id}', [ExpenseController::class, 'update'])->name('edit_expenses');
Route::put('/update-details-date/{id}', [ExpenseController::class, 'updateDetailsDate'])->name('edit_expenses');
Route::get('/expenses/{id}', [ExpenseController::class, 'show'])->name('show_single_expenses');
Route::delete('/expenses/{id}', [ExpenseController::class, 'destroy'])->name('delete_expenses');
Route::delete('/reset-expense-approval/{id}', [ExpenseController::class, 'resetApprovals'])->name('delete_expenses');
Route::post('/expense-save-attachments', [ExpenseController::class, 'attachments'])->name(''); //TODO:permission is not implemented yet
Route::get('/min-date-expense-header', [ExpenseController::class, 'minDate'])->name(''); //TODO:permission is not implemented yet
Route::get('/change-expense-dates', [ExpenseController::class, 'changeExpenseDates'])->name(''); //TODO:permission is not implemented yet
Route::post('/show-user-functions', [ExpenseController::class, 'getUsersData'])->name(''); //TODO:permission is not implemented yet

Route::post('/expense-approvals', [ExpenseController::class, 'expenseFlow'])->name(''); //TODO:permission is not implemented yet
// expense Double Location

Route::get('/expenses-second-version', [ExpenseDoubleLocationController::class, 'index'])->name('show_all_expenses');
Route::post('/expenses-second-version', [ExpenseDoubleLocationController::class, 'store'])->name('create_expenses');
Route::get('/expenses-second-version/{id}', [ExpenseDoubleLocationController::class, 'show'])->name('show_single_expenses');
Route::put('/expenses-second-version/{id}', [ExpenseDoubleLocationController::class, 'update'])->name('edit_expenses');
Route::delete('/expenses-second-version/{id}', [ExpenseDoubleLocationController::class, 'destroy'])->name('delete_expenses');
Route::post('/get-expense-details-second-version', [ExpenseDoubleLocationController::class, 'details'])->name(''); //TODO:permission is not implemented yet
Route::get('/edit-expense-details-second-version/{detail}', [ExpenseDoubleLocationController::class, 'getExpenseDetails'])->name('edit_expenses');

Route::post('/expense-feedback/{id}', [ExpenseController::class, 'feedback'])->name('expense_request_feedback');

Route::post('/get-expense-details', [ExpenseController::class, 'details'])->name(''); //TODO:permission is not implemented yet
Route::post('/get-expense-details-approvals', [ExpenseApprovalController::class, 'details'])->name(''); //TODO:permission is not implemented yet
Route::post('/get-expense-location-details', [ExpensePerLocationController::class, 'details'])->name(''); //TODO:permission is not implemented yet
Route::get('/expense-type-data/{type}', [ExpenseDataController::class, 'expenseTypePerLocation'])->name(''); //TODO:permission is not implemented yet


Route::get('/expense-policies', [ExpenseController::class, 'policies'])->name(''); //TODO:permission is not implemented yet

// Expense Settings
Route::get('/expense-settings', [ExpenseSettingController::class, 'index'])->name('show_all_expense_settings');
Route::get('/expense-settings/{id}', [ExpenseSettingController::class, 'show'])->name('show_single_expense_settings');
Route::put('/expense-settings/{id}', [ExpenseSettingController::class, 'update'])->name('edit_expense_settings');

// Coaching Settings
Route::get('/coaching-settings', [CoachingSettingController::class, 'index'])->name('show_all_coaching_settings');
Route::get('/coaching-settings/{id}', [CoachingSettingController::class, 'show'])->name('show_single_coaching_settings');
Route::put('/coaching-settings/{id}', [CoachingSettingController::class, 'update'])->name('edit_coaching_settings');

// Expense Location Settings
Route::get('/expense-location-settings', [ExpensePerLocationSettingController::class, 'index'])->name('show_all_expense_location_settings');
Route::post('/expense-location-settings', [ExpensePerLocationSettingController::class, 'store'])->name('create_expense_location_settings');
Route::get('/expense-location-settings/{id}', [ExpensePerLocationSettingController::class, 'show'])->name('show_single_expense_location_settings');
Route::put('/expense-location-settings/{id}', [ExpensePerLocationSettingController::class, 'update'])->name('edit_expense_location_settings');
Route::delete('/expense-location-settings/{id}', [ExpensePerLocationSettingController::class, 'destroy'])->name('delete_expense_location_settings');

Route::post('/import-expense-location-settings', [ExpensePerLocationSettingController::class, 'import'])->name('import_expense_location_settings');
Route::post('/import-update-expense-location-settings', [ExpensePerLocationSettingController::class, 'updateByImport'])->name('import_bulk_edit');
Route::get('/export-expense-location-settings', [ExpensePerLocationSettingController::class, 'export'])->name('export_xlsx_expense_location_settings');
Route::get('/export-expense-location-settings-csv', [ExpensePerLocationSettingController::class, 'exportcsv'])->name('export_csv_expense_location_settings');
Route::get('/export-expense-location-settings-pdf', [ExpensePerLocationSettingController::class, 'exportpdf'])->name('export_pdf_expense_location_settings');
Route::post('send-mail-expense-location-settings', [ExpensePerLocationSettingController::class, 'sendmail'])->name('export_email_expense_location_settings');

// Expense Location Prices
Route::get('/expense-location-prices', [ExpensePerLocationPriceController::class, 'index'])->name('show_all_expense_location_prices');
Route::post('/expense-location-prices', [ExpensePerLocationPriceController::class, 'store'])->name('create_expense_location_prices');
Route::get('/expense-location-prices/{id}', [ExpensePerLocationPriceController::class, 'show'])->name('show_single_expense_location_prices');
Route::put('/expense-location-prices/{id}', [ExpensePerLocationPriceController::class, 'update'])->name('edit_expense_location_prices');
Route::delete('/expense-location-prices/{id}', [ExpensePerLocationPriceController::class, 'destroy'])->name('delete_expense_location_prices');

Route::post('/import-expense-location-prices', [ExpensePerLocationPriceController::class, 'import'])->name('import_expense_location_prices');
Route::post('/import-update-expense-location-prices', [ExpensePerLocationPriceController::class, 'updateByImport'])->name('import_bulk_edit');
Route::get('/export-expense-location-prices', [ExpensePerLocationPriceController::class, 'export'])->name('export_xlsx_expense_location_prices');
Route::get('/export-expense-location-prices-csv', [ExpensePerLocationPriceController::class, 'exportcsv'])->name('export_csv_expense_location_prices');
Route::get('/export-expense-location-prices-pdf', [ExpensePerLocationPriceController::class, 'exportpdf'])->name('export_pdf_expense_location_prices');
Route::post('send-mail-expense-location-prices', [ExpensePerLocationPriceController::class, 'sendmail'])->name('export_email_expense_location_prices');


// Expense Location Prices
Route::get('/expense-locations', [ExpensePerLocationController::class, 'index'])->name('show_all_expense_locations');
Route::post('/expense-locations', [ExpensePerLocationController::class, 'store'])->name('create_expense_locations');
Route::get('/expense-locations/{id}', [ExpensePerLocationController::class, 'show'])->name('show_single_expense_locations');
// Route::put('/expense-locations/{id}',[ExpensePerLocationController::class,'update'])->name('edit_expense_locations');
Route::delete('/expense-locations/{id}', [ExpensePerLocationController::class, 'destroy'])->name('delete_expense_locations');

Route::post('/expense-location-kilometers', [ExpensePerLocationController::class, 'getKiloMeters'])->name('');
Route::post('/get-meals', [ExpenseDataController::class, 'getMeals'])->name('');
Route::get('/expense-download-attachment', [ExpensePerLocationController::class, 'downloadAttach'])->name('');


// Expense Price Factor
Route::get('/expense-price-factors', [ExpensePerLocationPriceFactorController::class, 'index'])->name('show_all_expense_price_factors');
Route::post('/expense-price-factors', [ExpensePerLocationPriceFactorController::class, 'store'])->name('create_expense_price_factors');
Route::get('/expense-price-factors/{id}', [ExpensePerLocationPriceFactorController::class, 'show'])->name('show_single_expense_price_factors');
Route::put('/expense-price-factors/{id}', [ExpensePerLocationPriceFactorController::class, 'update'])->name('edit_expense_price_factors');
Route::delete('/expense-price-factors/{id}', [ExpensePerLocationPriceFactorController::class, 'destroy'])->name('delete_expense_price_factors');
Route::get('/get-positions', [ExpensePerLocationPriceFactorController::class, 'getPositions'])->name('');

// Price Factor Average
Route::get('/kilometers-average', [KilometersAverageController::class, 'index'])->name('show_all_kilometers_average');
Route::post('/kilometers-average', [KilometersAverageController::class, 'store'])->name('create_kilometers_average');
Route::get('/kilometers-average/{id}', [KilometersAverageController::class, 'show'])->name('show_single_kilometers_average');
Route::put('/kilometers-average/{id}', [KilometersAverageController::class, 'update'])->name('edit_kilometers_average');
Route::delete('/kilometers-average/{id}', [KilometersAverageController::class, 'destroy'])->name('delete_kilometers_average');





//resource promotional material types
Route::get('/promotional-material-types', [PromotionalMaterialTypeController::class, 'index'])->name('show_all_promotional_material_types');
Route::post('/promotional-material-types', [PromotionalMaterialTypeController::class, 'store'])->name('create_promotional_material_types');
Route::get('/promotional-material-types/{id}', [PromotionalMaterialTypeController::class, 'show'])->name('show_single_promotional_material_types');
Route::put('/promotional-material-types/{id}', [PromotionalMaterialTypeController::class, 'update'])->name('edit_promotional_material_types');
Route::delete('/promotional-material-types/{id}', [PromotionalMaterialTypeController::class, 'destroy'])->name('delete_promotional_material_types');



// material vendors

Route::get('/material-vendors', [MaterialVendorController::class, 'index'])->name('show_all_material_vendors');
Route::post('/material-vendors', [MaterialVendorController::class, 'store'])->name('create_material_vendors');
Route::get('/material-vendors/{id}', [MaterialVendorController::class, 'show'])->name('show_single_material_vendors');
Route::put('/material-vendors/{id}', [MaterialVendorController::class, 'update'])->name('edit_material_vendors');
Route::delete('/material-vendors/{id}', [MaterialVendorController::class, 'destroy'])->name('delete_material_vendors');

// promotional material Settings
Route::post('/materials/index', [MaterialController::class, 'index'])->name('show_all_material');
Route::post('/materials', [MaterialController::class, 'store'])->name('create_material');
Route::get('/materials/{id}', [MaterialController::class, 'show'])->name('show_single_material');
Route::get('/materials/{id}/edit', [MaterialController::class, 'edit'])->name('edit_material');
Route::put('/materials/{id}', [MaterialController::class, 'update'])->name('edit_material');
Route::delete('/materials/{id}', [MaterialController::class, 'destroy'])->name('delete_material');
Route::get('/material-policies', [MaterialController::class, 'policies'])->name('');
Route::get('/material-lines', [MaterialController::class, 'lines'])->name('');
Route::post('/material-products', [MaterialController::class, 'products'])->name('');
Route::post('/material-edited-products', [MaterialController::class, 'getEditedProducts'])->name('');
Route::post('/material-products-data', [MaterialController::class, 'getMaterialProducts'])->name(''); //TODO:permission is not implemented yet
Route::post('/material-approval-data', [MaterialController::class, 'getMaterialApprovals'])->name(''); //TODO:permission is not implemented yet
Route::post('/material-stocks', [MaterialStockController::class, 'index'])->name('show_all_material_stocks'); //TODO:permission is not implemented yet
Route::post('/save-material-stocks', [MaterialStockController::class, 'save'])->name('create_material_stocks'); //TODO:permission is not implemented yet
Route::post('/material-distributions', [MaterialController::class, 'distributions'])->name('show_all_material_distributions');
Route::delete('/material-attach/{id}', [MaterialController::class, 'removeMaterialAttach'])->name('edit_commercial_requests'); //TODO:permission is not implemented yet


Route::get('/myline-products/{line_id}', [LineController::class, 'getLineProducts'])->name(''); //TODO:permission is not implemented yet


Route::get('/automatic-expense', [ExpenseTypesController::class, 'checkAutomaticExpense'])->name('');


// commercial pharmacy Settings
Route::get('/commercial-pharmacies', [CommercialPharmacySettingController::class, 'index'])->name('show_all_commercial_pharmacies');
Route::post('/commercial-pharmacies', [CommercialPharmacySettingController::class, 'store'])->name('create_commercial_pharmacies');
Route::get('/commercial-pharmacies/{id}', [CommercialPharmacySettingController::class, 'show'])->name('show_single_commercial_pharmacies');
Route::put('/commercial-pharmacies/{id}', [CommercialPharmacySettingController::class, 'update'])->name('edit_commercial_pharmacies');
Route::delete('/commercial-pharmacies/{id}', [CommercialPharmacySettingController::class, 'destroy'])->name('delete_commercial_pharmacies');


//resource Budget Setup
Route::get('/budget', [BudgetSetupController::class, 'index'])->name('show_all_budget_setups');
Route::post('/budget/subtypes', [BudgetSetupController::class, 'getSubTypes'])->name(''); // TODO:: display all subtypes
Route::post('/budget', [BudgetSetupController::class, 'store'])->name('create_budget_setups');
Route::post('/get-budgets', [BudgetSetupController::class, 'show'])->name('show_single_budget_setups');
Route::post('/import-budget', [BudgetSetupController::class, 'import'])->name('');

Route::delete('/budget/{id}', [BudgetSetupController::class, 'destroy'])->name('delete_budget_setups');
Route::post('/budget/{line}', [BudgetSetupController::class, 'getLineData'])->name(''); // TODO:: display all subtypes


// edit and delete request feedback

Route::get('/request-feedback/{requestFeedback}', [RequestFeedbackController::class, 'show'])->name('edit_request_feedback');
Route::put('/request-feedback/{requestFeedback}', [RequestFeedbackController::class, 'update'])->name('edit_request_feedback');
Route::delete('/request-feedback/{requestFeedback}', [RequestFeedbackController::class, 'destroy'])->name('delete_request_feedback');

//edit and delete request payment

Route::get('/request-payment/{paidRequest}', [PaidRequestController::class, 'show'])->name('edit_paid_request');
Route::put('/request-payment/{paidRequest}', [PaidRequestController::class, 'update'])->name('edit_paid_request');
Route::delete('/request-payment/{paidRequest}', [PaidRequestController::class, 'destroy'])->name('delete_paid_request');



// accounts active and inactive
Route::post('/active-inactive-accounts', [ActiveInActiveApprovalController::class, 'getData'])->name(''); //TODO:permission is not implemented yet
Route::post('/save-active-inactive-accounts', [ActiveInActiveApprovalController::class, 'save'])->name('show_settings_active_inactive_accounts'); //TODO:permission is not implemented yet
Route::post('/active-inactive-accounts/approvals', [ActiveInActiveApprovalController::class, 'getAccounts'])->name('show_settings_active_inactive_approvals'); //TODO:permission is not implemented yet
Route::post('/active-inactive/accept', [ActiveInActiveApprovalController::class, 'accept'])->name('create_approve_active_inactive'); //TODO:permission is not implemented yet
Route::post('/active-inactive/reject', [ActiveInActiveApprovalController::class, 'reject'])->name(''); //TODO:permission is not implemented yet
Route::post('/active-inactive-users', [ActiveInActiveApprovalController::class, 'filterOfEmployees'])->name(''); //TODO:permission is not implemented yet
Route::post('/active-inactive-approvals', [ActiveInActiveApprovalController::class, 'getApprovals'])->name(''); //TODO:permission is not implemented yet



// accounts active and inactive
Route::get('/account/request/setting', [AccountRequestController::class, 'getSettings'])->name(''); //TODO:permission is not implemented yet
Route::get('/account/request/bricks', [AccountRequestController::class, 'getBricks'])->name(''); //TODO:permission is not implemented yet
Route::get('/account/request/lines', [AccountRequestController::class, 'lines'])->name(''); //TODO:permission is not implemented yet
Route::get('/account/request/{line}/divisons', [AccountRequestController::class, 'divisions'])->name(''); //TODO:permission is not implemented yet
Route::get('/account/request/{line}/{division}/bricks', [AccountRequestController::class, 'bricks'])->name(''); //TODO:permission is not implemented yet
Route::post('/account/request/store', [AccountRequestController::class, 'store'])->name(''); //TODO:permission is not implemented yet
Route::post('/account/request/approvals/', [AccountRequestController::class, 'getAccounts'])->name(''); //TODO:permission is not implemented yet
Route::post('/account/request/accept', [AccountRequestController::class, 'accept'])->name(''); //TODO:permission is not implemented yet
Route::post('/account/request/reject', [AccountRequestController::class, 'reject'])->name(''); //TODO:permission is not implemented yet
Route::post('/account/request/users', [AccountRequestController::class, 'filterOfEmployees'])->name(''); //TODO:permission is not implemented yet
Route::post('/account/request/approval/users', [AccountRequestController::class, 'getApprovals'])->name(''); //TODO:permission is not implemented yet


// Scientific Offices
Route::get('/scientific-offices', [ScientificOfficesController::class, 'index'])->name('');
Route::post('/scientific-offices', [ScientificOfficesController::class, 'store'])->name('');
Route::get('/scientific-offices/{id}', [ScientificOfficesController::class, 'show'])->name('');
Route::put('/scientific-offices/{id}', [ScientificOfficesController::class, 'update'])->name('');
Route::delete('/scientific-offices/{id}', [ScientificOfficesController::class, 'destroy'])->name('');



// Service Done
Route::get('/service/complete/{id}', [ServiceCompleteReportController::class, 'getServices'])->name('');
Route::get('/get/service/complete/{id}', [ServiceCompleteReportController::class, 'getServiceData'])->name('');
Route::post('/save-service-complete', [ServiceCompleteReportController::class, 'save'])->name('edit_service_completes');


// Partial Payments
Route::post('/partial/payments', [PartialPaymentController::class, 'index'])->name('show_all_partial_payments');
Route::get('/partial/payments/{id}', [PartialPaymentController::class, 'show'])->name('show_single_partial_payments');
Route::put('/partial/payments/{id}', [PartialPaymentController::class, 'update'])->name('edit_partial_payments');
