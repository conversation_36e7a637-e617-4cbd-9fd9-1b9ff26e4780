<?php

//resource

use App\Http\Controllers\Coaching\CategoryController;
use App\Http\Controllers\Coaching\CategoryEvaluatorController;
use App\Models\Coaching\CategoryEvaluator;
use Illuminate\Support\Facades\Route;

Route::get('/coaching/evaluatoruser/{line}/categories', [CategoryEvaluatorController::class, 'getEvaluatorCategories'])->name(''); // Todo i think no need for permission here
Route::post('/coaching/{line}/categories', [CategoryEvaluatorController::class, 'getCheckedCategories'])->name(''); // Todo i think no need for permission here
// Route::post('/coaching/evaluator/categories',[CategoryEvaluatorController::class,'store'])->name('create_categories');
// Route::get('/coaching/evaluator/categories/{category}',[CategoryEvaluatorController::class,'show'])->name('show_single_categories');
// Route::put('/coaching/evaluator/categories/{category}',[CategoryEvaluatorController::class,'update'])->name('edit_categories');
// Route::delete('/coaching/evaluator/categories/{category}',[CategoryEvaluatorController::class,'destroy'])->name('delete_categories');


// // Tools 
// Route::post('/coaching/importcategories', [CategoryEvaluatorController::class,'import'])->name('import_categories');
// Route::post('/coaching/importupdatecategories', [CategoryEvaluatorController::class, 'updateByImport'])->name('import_bulk_edit');
// Route::get('/coaching/downloadcategory/{filename}', [CategoryEvaluatorController::class,'export'])->name('download_template_categories');
// Route::get('/coaching/exportcategories', [CategoryEvaluatorController::class,'exportcategories'])->name('export_xlsx_categories');
// Route::get('/coaching/exportcategoriescsv', [CategoryEvaluatorController::class,'exportcsv'])->name('export_csv_categories');
// Route::get('/coaching/exportcategorypdf', [CategoryEvaluatorController::class,'exportpdf'])->name('export_pdf_categories');

// Route::get('/coaching/restorecategory', [CategoryEvaluatorController::class,'restore'])->name('restore_categories');

// Route::post('/coaching/sendmailcategories', [CategoryEvaluatorController::class,'sendmail'])->name('export_email_categories');
