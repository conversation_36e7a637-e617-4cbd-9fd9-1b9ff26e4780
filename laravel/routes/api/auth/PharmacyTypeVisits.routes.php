<?php

use App\Http\Controllers\PharmacyTypeVisitController;
use Illuminate\Support\Facades\Route;


//resource
Route::get('/pharmacy-type-visits', [PharmacyTypeVisitController::class, 'index'])->name('');
Route::post('/pharmacy-type-visits', [PharmacyTypeVisitController::class, 'store'])->name('');
Route::get('/pharmacy-type-visits/{id}', [PharmacyTypeVisitController::class, 'show'])->name('');
Route::put('/pharmacy-type-visits/{id}', [PharmacyTypeVisitController::class, 'update'])->name('');
Route::delete('/pharmacy-type-visits/{id}', [PharmacyTypeVisitController::class, 'destroy'])->name('');
