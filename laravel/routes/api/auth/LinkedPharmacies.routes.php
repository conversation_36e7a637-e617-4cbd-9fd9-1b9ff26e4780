<?php

use App\Http\Controllers\LevelController;
use App\Http\Controllers\LinkedPharmaciesController;
use App\Http\Controllers\LinkedPharmacyPerProductRatioController;
use Illuminate\Support\Facades\Route;


//resource
Route::post('/linked-pharmacy/index', [LinkedPharmaciesController::class, 'index'])->name('show_all_linked_pharmacies');
Route::post('/linked-pharmacy', [LinkedPharmaciesController::class, 'store'])->name('create_linked_pharmacies');
Route::get('/linked-pharmacy/{id}', [LinkedPharmaciesController::class, 'show'])->name('show_single_linked_pharmacies');
Route::put('/linked-pharmacy/{id}', [LinkedPharmaciesController::class, 'update'])->name('edit_linked_pharmacies');
Route::delete('/linked-pharmacy/{id}', [LinkedPharmaciesController::class, 'destroy'])->name('delete_linked_pharmacies');
Route::get('/linked-divisions/{line}', [LinkedPharmaciesController::class, 'divisions'])->name('');

Route::post('/import-linked-pharmacy', [LinkedPharmaciesController::class, 'import'])->name('import_linked_pharmacies');
Route::get('/export-linked-pharmacy', [LinkedPharmaciesController::class, 'exportLinked'])->name('export_xlsx_linked_pharmacies');
Route::get('/export-linked-pharmacy-pdf', [LinkedPharmaciesController::class, 'exportpdf'])->name('export_pdf_linked_pharmacies');
Route::post('/send-mail-linked-pharmacy', [LinkedPharmaciesController::class, 'sendmail'])->name('export_email_linked_pharmacies');
Route::get('/export-linked-pharmacy-csv', [LinkedPharmaciesController::class, 'exportcsv'])->name('export_csv_linked_pharmacies');


/* Linked Pharmacies to accounts Routes */
Route::post('/linked-pharmacies-list',[LinkedPharmaciesController::class,'filter'])->name('');
Route::get('/linked-pharmacies-list/{line}',[LinkedPharmaciesController::class,'lineDivisions'])->name('');
Route::post('/linked-pharmacies-bricks',[LinkedPharmaciesController::class,'divisionBricks'])->name('');
Route::post('/linked-pharmacies-store',[LinkedPharmaciesController::class,'linked'])->name('');




/* Linked Per Brand */
Route::post('/linked-pharmacies-per-brand',[LinkedPharmacyPerProductRatioController::class,'linkedPerBrand'])->name('');
Route::post('/linked-pharmacies-per-brand-store',[LinkedPharmacyPerProductRatioController::class,'storeLinkedPerBrand'])->name('');
