<?php

use App\Http\Controllers\BrandController;
use App\Http\Controllers\UnplannedVisitNumberController;
use Illuminate\Support\Facades\Route;


//resource
Route::get('/unplanned', [UnplannedVisitNumberController::class, 'index'])->name('show_all_unplanned_visits');
Route::post('/unplanned', [UnplannedVisitNumberController::class, 'store'])->name('create_unplanned_visits');
Route::get('/unplanned/{id}', [UnplannedVisitNumberController::class, 'show'])->name('show_single_unplanned_visits');
Route::put('/unplanned/{id}', [UnplannedVisitNumberController::class, 'update'])->name('edit_unplanned_visits');
Route::delete('/unplanned/{id}', [UnplannedVisitNumberController::class, 'destroy'])->name('delete_unplanned_visits');

