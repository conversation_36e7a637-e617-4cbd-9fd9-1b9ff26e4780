<?php

use App\Http\Controllers\DistributorsController;
use App\Models\Distributors\ProductMapping;
use Illuminate\Support\Facades\Route;


//resource
Route::get('/distributors',[DistributorsController::class,'index'])->name('show_all_distributors');
Route::post('/distributors',[DistributorsController::class,'store'])->name('create_distributors');
Route::get('/distributors/create',[DistributorsController::class,'create'])->name('create_distributors');
Route::get('/distributors/{id}/edit',[DistributorsController::class,'edit'])->name('edit_distributors');
Route::get('/distributors/{id}',[DistributorsController::class,'show'])->name('show_single_distributors');
Route::put('/distributors/{id}',[DistributorsController::class,'update'])->name('edit_distributors');
Route::delete('/distributors/{id}',[DistributorsController::class,'destroy'])->name('delete_distributors');

Route::post('/importdistributors', [DistributorsController::class,'import'])->name('import_distributors');
Route::post('/importupdatedistributors', [DistributorsController::class, 'updateByImport'])->name('import_bulk_edit');
Route::get('/downloaddistributor/{filename}', [DistributorsController::class,'export'])->name('download_template_distributors');
Route::get('/exportdistributors', [DistributorsController::class,'exportdistributors'])->name('export_xlsx_distributors');
Route::get('/exportdistributorpdf', [DistributorsController::class,'exportpdf'])->name('export_pdf_distributors');
Route::post('/sendmaildistributors', [DistributorsController::class,'sendmail'])->name('export_email_distributors');
Route::get('/exportdistributorscsv', [DistributorsController::class,'exportcsv'])->name('export_csv_distributors');
