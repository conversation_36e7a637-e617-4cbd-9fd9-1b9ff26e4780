<?php

use App\Http\Controllers\ProductSampleController;
use Illuminate\Support\Facades\Route;

//resource product
Route::post('/samples/index', [ProductSampleController::class, 'index'])->name('show_all_samples');
Route::post('/samples', [ProductSampleController::class, 'store'])->name('create_samples');
Route::get('/samples/{id}', [ProductSampleController::class, 'show'])->name('show_single_samples');
Route::put('/samples/{id}', [ProductSampleController::class, 'update'])->name('edit_samples');
Route::delete('/samples/{id}', [ProductSampleController::class, 'destroy'])->name('delete_samples');

Route::post('/samples/import', [ProductSampleController::class, 'import'])->name('import_samples');
Route::post('/samples/import/update', [ProductSampleController::class, 'updateByImport'])->name('import_bulk_edit');

Route::get('/export-samples', [ProductSampleController::class, 'export'])->name('export_xlsx_samples');
Route::get('/export-samples-pdf', [ProductSampleController::class, 'exportpdf'])->name('export_pdf_samples');
Route::post('/send-mail-samples', [ProductSampleController::class, 'sendmail'])->name('export_email_samples');
Route::get('/export-samples-csv', [ProductSampleController::class, 'exportcsv'])->name('export_csv_samples');


Route::post('/change-samples-approval', [ProductSampleController::class, 'approveSamples'])->name('');
Route::post('/edit-samples', [ProductSampleController::class, 'editSamples'])->name('');
