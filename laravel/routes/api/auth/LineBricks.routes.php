<?php

use App\Http\Controllers\LineBricksController;
use Illuminate\Support\Facades\Route;

Route::get('/line-bricks/{line}', [LineBricksController::class,'index'])->name('show_all_line_bricks');
Route::get('/get_line_bricks/{line}', [LineBricksController::class,'getLineBricks'])->name('show_all_line_bricks');
Route::post('/lines/{line}/bricks', [LineBricksController::class,'store'])->name('create_line_bricks');
Route::post('/line-brick/move', [LineBricksController::class,'moveLineBrick'])->name('create_line_bricks');
Route::post('/line-brick/reshape', [LineBricksController::class,'recontribute'])->name('create_line_bricks');
Route::get('/line-brick/{id}', [LineBricksController::class,'show'])->name('show_single_line_bricks');
Route::put('/lines/{line}/brick/{id}', [LineBricksController::class,'update'])->name('edit_line_bricks');
Route::get('/lines/{line_id}/brick/{brick_id}', [LineBricksController::class,'getLineBrick'])->name('');// TODO: get line bricks for line and brick
Route::delete('/line-bricks/{id}', [LineBricksController::class,'destroy'])->name('delete_line_bricks');
