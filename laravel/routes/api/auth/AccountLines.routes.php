<?php

use App\Http\Controllers\AccountController;
use App\Http\Controllers\AccountLineController;
use Illuminate\Support\Facades\Route;

// Route::get('/get_account_lines/{id}', [AccountController::class,'getAccountLines'])->name('show_all_account_lines');
// Route::post('/add_account_line', [AccountController::class,'storeAccountLine'])->name('create_account_lines');
// Route::delete('/delete_account_line/{id}', [AccountController::class,'deleteAccountLine'])->name('delete_account_lines');
// Route::put('/update_account_line/{id}', [AccountController::class,'updateAccountLine'])->name('edit_account_lines');
// Route::get('/showEditAccountLines/{id}', [AccountController::class,'showEditAccountLines'])->name('edit_account_lines');


Route::get('/account-lines', [AccountLineController::class,'index'])->name('show_all_account_lines');
Route::get('/get-account-lines/{account}', [AccountLineController::class,'getAccountLines'])->name('show_all_account_lines');
Route::post('/account-lines', [AccountLineController::class,'store'])->name('create_account_lines');
Route::get('/account-lines/{id}', [AccountLineController::class,'show'])->name('show_single_account_lines');
Route::put('/account-lines/{id}', [AccountLineController::class,'update'])->name('edit_account_lines');
Route::delete('/account-lines/{id}', [AccountLineController::class,'destroy'])->name('delete_account_lines');
Route::get('/get-line-divisions/{line}', [AccountLineController::class,'getLineDivisions'])->name('show_single_account_lines');
Route::get('/get-line-division-bricks/{division}', [AccountLineController::class,'getLineDivisionBricks'])->name('show_single_account_lines');
