<?php

use App\Http\Controllers\CurrencyController;
use Illuminate\Support\Facades\Route;


//resource
Route::get('/currencies',[CurrencyController::class,'index'])->name('show_all_currencies');
Route::post('/currencies',[CurrencyController::class,'store'])->name('create_currencies');
Route::get('/currencies/create',[CurrencyController::class,'create'])->name('create_currencies');
Route::get('/currencies/{id}/edit',[CurrencyController::class,'edit'])->name('edit_currencies');
Route::get('/currencies/{id}',[CurrencyController::class,'show'])->name('show_single_currencies');
Route::put('/currencies/{id}',[CurrencyController::class,'update'])->name('edit_currencies');
Route::delete('/currencies/{id}',[CurrencyController::class,'destroy'])->name('delete_currencies');

Route::post('/importcurrencies', [CurrencyController::class,'import'])->name('import_currencies');
Route::post('/importupdatecurrencies', [CurrencyController::class, 'updateByImport'])->name('import_bulk_edit');
Route::get('/downloadcurrency/{filename}', [CurrencyController::class,'export'])->name('download_template_currencies');
Route::get('/exportcurrencies', [CurrencyController::class,'exportcurrencies'])->name('export_xlsx_currencies');
Route::get('/exportcurrencypdf', [CurrencyController::class,'exportpdf'])->name('export_pdf_currencies');
Route::post('/sendmailcurrencies', [CurrencyController::class,'sendmail'])->name('export_email_currencies');
Route::get('/exportcurrenciescsv', [CurrencyController::class,'exportcsv'])->name('export_csv_currencies');
