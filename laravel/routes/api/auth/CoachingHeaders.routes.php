<?php

//resource

use App\Http\Controllers\Coaching\CategoryController;
use App\Http\Controllers\Coaching\CategoryEvaluatorController;
use App\Http\Controllers\CoachingHeaderController;
use App\Models\Coaching\CategoryEvaluator;
use Illuminate\Support\Facades\Route;

// Route::get('/coaching/evaluatoruser/categories',[CategoryEvaluatorController::class,'getEvaluatorCategories'])->name(''); // Todo i think no need for permission here
Route::post('/coaching/headers', [CoachingHeaderController::class, 'store'])->name('');
Route::post('/change-coaching-approval', [CoachingHeaderController::class, 'change_approval'])->name('');
// Route::get('/coaching/evaluator/categories/{category}',[CategoryEvaluatorController::class,'show'])->name('show_single_categories');
// Route::put('/coaching/evaluator/categories/{category}',[CategoryEvaluatorController::class,'update'])->name('edit_categories');
Route::delete('/coaching/headers/{coachingHeader}',[CoachingHeaderController::class,'destroy'])->name('delete_categories');


// // Tools 
// Route::post('/coaching/importcategories', [CategoryEvaluatorController::class,'import'])->name('import_categories');
// Route::post('/coaching/importupdatecategories', [CategoryEvaluatorController::class, 'updateByImport'])->name('import_bulk_edit');
// Route::get('/coaching/downloadcategory/{filename}', [CategoryEvaluatorController::class,'export'])->name('download_template_categories');
// Route::get('/coaching/exportcategories', [CategoryEvaluatorController::class,'exportcategories'])->name('export_xlsx_categories');
// Route::get('/coaching/exportcategoriescsv', [CategoryEvaluatorController::class,'exportcsv'])->name('export_csv_categories');
// Route::get('/coaching/exportcategorypdf', [CategoryEvaluatorController::class,'exportpdf'])->name('export_pdf_categories');

// Route::get('/coaching/restorecategory', [CategoryEvaluatorController::class,'restore'])->name('restore_categories');

// Route::post('/coaching/sendmailcategories', [CategoryEvaluatorController::class,'sendmail'])->name('export_email_categories');

// Coaching Reports

// coaching & performance summary report

Route::post('/coaching/performance-summary-report', [CoachingHeaderController::class, 'performanceSummaryReport'])->name('show_reports_coaching_performance_summary');
Route::get('below-users/{user}/lines/{line}', [CoachingHeaderController::class, 'getBelowUsers'])->name('');
Route::get('coaching-lines', [CoachingHeaderController::class, 'getLines'])->name('');
Route::get('coaching-dates', [CoachingHeaderController::class, 'getDates'])->name('');

// Route::post('/coaching/statistics-report', [CoachingHeaderController::class, 'statisticsReport'])->name('show_reports_coaching_statistics'); 