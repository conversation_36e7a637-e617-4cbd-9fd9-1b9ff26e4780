<?php

use App\Http\Controllers\Product\MessageController;
use App\Http\Controllers\Product\ProductMessageController;
use App\Http\Controllers\ProductBrandController;
use App\Http\Controllers\ProductCeilingController;
use App\Http\Controllers\ProductController;
use App\Http\Controllers\ProductLinesController;
use App\Http\Controllers\ProductManufacturerController;
use App\Http\Controllers\ProductOfferController;
use App\Http\Controllers\ProductPresentationController;
use App\Http\Controllers\ProductPriceController;
use App\Http\Controllers\ProductSpecialityController;
use Illuminate\Support\Facades\Route;

//resource product
Route::get('/products',[ProductController::class,'index'])->name('show_all_products');
Route::get('/productslist',[ProductController::class,'listproducts'])->name('');
Route::post('/products',[ProductController::class,'store'])->name('create_products');
Route::get('/products/create',[ProductController::class,'create'])->name('create_products');
Route::get('/products/{id}/edit',[ProductController::class,'edit'])->name('edit_products');
Route::get('/products/{id}',[ProductController::class,'show'])->name('show_single_products');
Route::put('/products/{id}',[ProductController::class,'update'])->name('edit_products');
Route::delete('/products/{id}',[ProductController::class,'destroy'])->name('delete_products');

Route::post('/importproducts', [ProductController::class, 'import'])->name('import_products');
Route::post('/importupdateproducts', [ProductController::class, 'updateByImport'])->name('import_bulk_edit');

Route::get('/downloadproduct/{filename}', [ProductController::class, 'export'])->name('download_template_products');
Route::get('/exportproducts', [ProductController::class, 'exportproducts'])->name('export_xlsx_products');
Route::get('/exportproductpdf', [ProductController::class, 'exportpdf'])->name('export_pdf_products');
Route::post('/sendmailproducts', [ProductController::class, 'sendmail'])->name('export_email_products');
Route::get('/exportproductscsv', [ProductController::class, 'exportcsv'])->name('export_csv_products');


//product offers
Route::post('/products/{product}/offers', [ProductOfferController::class, 'store'])->name('create_product_offers');
Route::get('/products/{product}/offers', [ProductOfferController::class, 'index'])->name('show_all_product_offers');
Route::delete('/products/{product}/offers/{offer}', [ProductOfferController::class, 'destroy'])->name('delete_product_offers');


// test product brand
Route::get('/get-brands', [ProductBrandController::class, 'index'])->name('show_all_brands');
Route::get('/products/{product}/brands', [ProductBrandController::class, 'getProductBrands'])->name('show_all_product_brands');
Route::post('/products/{product}/brands', [ProductBrandController::class, 'store'])->name('create_product_brands');
Route::get('/product-brands/{id}', [ProductBrandController::class, 'show'])->name('edit_product_brands');
Route::put('/product-brands/{productBrand}', [ProductBrandController::class, 'update'])->name('edit_product_brands');
Route::delete('/product-brands/{productBrand}', [ProductBrandController::class, 'destroy'])->name('delete_product_brands');

// test product Presentations
Route::get('/get-presentations/{id}', [ProductPresentationController::class, 'index'])->name('show_all_presentations');
Route::get('/products/{product}/presentations', [ProductPresentationController::class, 'getProductpresentations'])->name('show_all_product_presentations');
Route::post('/products/{product}/presentations', [ProductPresentationController::class, 'store'])->name('create_product_presentations');
Route::get('/product-presentations/{id}', [ProductPresentationController::class, 'show'])->name('edit_product_presentations');
Route::put('/product-presentations/{productPresentation}', [ProductPresentationController::class, 'update'])->name('edit_product_presentations');
Route::delete('/product-presentations/{productPresentation}', [ProductPresentationController::class, 'destroy'])->name('delete_product_presentations');

// test product Messages
Route::get('/get-messages', [ProductMessageController::class, 'index'])->name('show_all_messages');
Route::get('/products/{product}/messages', [ProductMessageController::class, 'getProductmessages'])->name('show_all_product_messages');
Route::post('/products/{product}/messages', [ProductMessageController::class, 'store'])->name('create_product_messages');
Route::get('/product-messages/{id}', [ProductMessageController::class, 'show'])->name('edit_product_messages');
Route::put('/product-messages/{productMessage}', [ProductMessageController::class, 'update'])->name('edit_product_messages');
Route::delete('/product-messages/{productMessage}', [ProductMessageController::class, 'destroy'])->name('delete_product_messages');

// test product Ceilings
Route::get('/products/{product}/ceilings', [ProductCeilingController::class, 'getProductCeilings'])->name('show_all_product_ceilings');
Route::post('/products/{product}/ceilings', [ProductCeilingController::class, 'store'])->name('create_product_ceilings');
Route::get('/product-ceilings/{id}', [ProductCeilingController::class, 'show'])->name('edit_product_ceilings');
Route::put('/product-ceilings/{productCeiling}', [ProductCeilingController::class, 'update'])->name('edit_product_ceilings');
Route::delete('/product-ceilings/{productCeiling}', [ProductCeilingController::class, 'destroy'])->name('delete_product_ceilings');

// test product lines
// Route::get('/line-products', [ProductLinesController::class, 'index'])->name('show_all_line_products');
Route::get('/products/{product}/lines', [ProductLinesController::class, 'getLineProducts'])->name('show_all_line_products');
Route::post('/products/{product}/lines', [ProductLinesController::class, 'store'])->name('create_line_products');
Route::get('/product-lines/{id}', [ProductLinesController::class, 'show'])->name('show_single_line_products');
Route::put('/products/{product}/lines/{id}', [ProductLinesController::class, 'update'])->name('edit_line_products');
Route::delete('/product-line/{id}', [ProductLinesController::class, 'destroy'])->name('delete_line_products');


// test product manufactrer
Route::get('/get_manufacturers', [ProductManufacturerController::class, 'index'])->name('show_all_manufacturers');
Route::get('/products/{product}/manufacturers', [ProductManufacturerController::class, 'getProductManufacturers'])->name('show_all_product_manufacturers');
Route::post('/products/{product}/manufacturers', [ProductManufacturerController::class, 'store'])->name('create_product_manufacturers');
Route::get('/product-manufacturers/{id}', [ProductManufacturerController::class, 'show'])->name('show_single_product_manufacturers');
Route::put('/product-manufacturers/{productManufacturer}', [ProductManufacturerController::class, 'update'])->name('edit_product_manufacturers');
Route::delete('/product-manufacturers/{productManufacturer}', [ProductManufacturerController::class, 'destroy'])->name('delete_product_manufacturers');

//test product prices
Route::get('/get-distributors', [ProductPriceController::class, 'index'])->name('show_all_distributors');
Route::get('/products/{product}/prices', [ProductPriceController::class, 'getProductPrices'])->name('show_all_product_prices');
Route::post('/products/{product}/prices', [ProductPriceController::class, 'store'])->name('create_product_prices');
Route::get('/product-prices/{id}', [ProductPriceController::class, 'show'])->name('show_single_product_prices');
Route::put('/product-prices/{productPrice}', [ProductPriceController::class, 'update'])->name('edit_product_prices');
Route::delete('/product-prices/{productPrice}', [ProductPriceController::class, 'destroy'])->name('delete_product_prices');


// test product specialities
Route::get('/get-specialities', [ProductSpecialityController::class, 'index'])->name('show_all_specialities');
Route::get('/products/{product}/specialities', [ProductSpecialityController::class, 'getProductSpecialities'])->name('show_all_product_specialities');
Route::post('/products/{product}/specialities', [ProductSpecialityController::class, 'store'])->name('create_product_specialities');
Route::get('/product-specialities/{id}', [ProductSpecialityController::class, 'show'])->name('edit_product_specialities');
Route::put('/product-specialities/{productSpeciality}', [ProductSpecialityController::class, 'update'])->name('edit_product_specialities');
Route::delete('/product-specialities/{productSpeciality}', [ProductSpecialityController::class, 'destroy'])->name('delete_product_specialities');



// samples in filters
Route::post('/samples-list',[ProductController::class,'samples'])->name('show_all_products');
Route::put('/change-sample-qty',[ProductController::class,'update_sample_qty'])->name('');

