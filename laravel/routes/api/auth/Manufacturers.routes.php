<?php

use App\Http\Controllers\ManufacturerController;
use Illuminate\Support\Facades\Route;


//resource
Route::get('/manufacturers',[ManufacturerController::class,'index'])->name('show_all_manufacturers');
Route::post('/manufacturers',[ManufacturerController::class,'store'])->name('create_manufacturers');
Route::get('/manufacturers/create',[ManufacturerController::class,'create'])->name('create_manufacturers');
Route::get('/manufacturers/{id}/edit',[ManufacturerController::class,'edit'])->name('edit_manufacturers');
Route::get('/manufacturers/{id}',[ManufacturerController::class,'show'])->name('show_single_manufacturers');
Route::put('/manufacturers/{id}',[ManufacturerController::class,'update'])->name('edit_manufacturers');
Route::delete('/manufacturers/{id}',[ManufacturerController::class,'destroy'])->name('delete_manufacturers');

Route::post('/importmanufacturers', [ManufacturerController::class,'import'])->name('import_manufacturers');
Route::post('/importupdatemanufacturers', [ManufacturerController::class, 'updateByImport'])->name('import_bulk_edit');
Route::get('/downloadmanufacturer/{filename}', [ManufacturerController::class,'export'])->name('download_template_manufacturers');
Route::get('/exportmanufacturers', [ManufacturerController::class,'exportmanufacturers'])->name('export_xlsx_manufacturers');
Route::get('/exportmanufacturerpdf', [ManufacturerController::class,'exportpdf'])->name('export_pdf_manufacturers');
Route::post('/sendmailmanufacturers', [ManufacturerController::class,'sendmail'])->name('export_email_manufacturers');
Route::get('/exportmanufacturerscsv', [ManufacturerController::class,'exportcsv'])->name('export_csv_manufacturers');
