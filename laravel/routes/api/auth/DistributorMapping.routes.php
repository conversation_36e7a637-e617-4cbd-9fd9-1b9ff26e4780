<?php


//distributor product

use App\Http\Controllers\DistributorLineController;
use App\Http\Controllers\DistributorProductController;
use App\Models\Distributors\DistributorLine;
use Illuminate\Support\Facades\Route;

Route::get('/distributor/{distributor}/product/mapping', [DistributorProductController::class,'index'])->name('show_all_distributor_products');
Route::post('/distributor/{distributor}/product/mapping', [DistributorProductController::class,'store'])->name('create_distributor_products');
Route::get('/distributors/{distributor}/mapping/products', [DistributorProductController::class,'show'])->name('show_single_distributor_products');
Route::put('/distributors/{distributor}/mapping/products', [DistributorProductController::class,'update'])->name('edit_distributor_products');
Route::delete('/distributors/{distributor}/mapping/products', [DistributorProductController::class,'destroy'])->name('delete_distributor_products');

Route::post('/import-distributors-products', [DistributorProductController::class,'import'])->name('import_product_distributors');

//distributor line mapping
Route::get('/distributor/{distributor}', [DistributorLineController::class,'index'])->name('show_all_distributor_lines');
Route::post('/distributor/{distributor}', [DistributorLineController::class,'store'])->name('create_distributor_lines');
Route::get('/distributor/{id}/lines', [DistributorLineController::class,'show'])->name('show_single_distributor_lines');
Route::put('/distributor/{distributor}/lines', [DistributorLineController::class,'update'])->name('edit_distributor_lines');
Route::delete('/distributor/{distributor}', [DistributorLineController::class,'destroy'])->name('delete_distributor_lines');


Route::post('/import-distributors-lines', [DistributorLineController::class,'import'])->name('import_distributor_lines');
