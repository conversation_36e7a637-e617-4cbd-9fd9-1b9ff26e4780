<?php

//resource

use App\Http\Controllers\Coaching\AnswerController;
use Illuminate\Support\Facades\Route;

Route::get('/coaching/answers',[AnswerController::class,'index'])->name('show_all_answers');
Route::post('/coaching/answers',[AnswerController::class,'store'])->name('create_answers');
Route::get('/coaching/answers/{answer}',[AnswerController::class,'show'])->name('show_single_answers');
Route::put('/coaching/answers/{answer}',[AnswerController::class,'update'])->name('edit_answers');
Route::delete('/coaching/answers/{answer}',[AnswerController::class,'destroy'])->name('delete_answers');

// Tools 
Route::post('/coaching/importanswers', [AnswerController::class,'import'])->name('import_answers');
Route::get('/coaching/downloadanswer/{filename}', [AnswerController::class,'export'])->name('download_template_answers');
Route::get('/coaching/exportanswers', [AnswerController::class,'exportanswers'])->name('export_xlsx_answers');
Route::get('/coaching/exportanswerscsv', [AnswerController::class,'exportcsv'])->name('export_csv_answers');
Route::get('/coaching/exportanswerpdf', [AnswerController::class,'exportpdf'])->name('export_pdf_answers');

Route::get('/coaching/restoreanswer', [AnswerController::class,'restore'])->name('restore_answers');

Route::post('/coaching/sendmailanswers', [AnswerController::class,'sendmail'])->name('export_email_answers');

