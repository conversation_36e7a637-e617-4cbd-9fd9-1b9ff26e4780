<?php

use App\Http\Controllers\ListManagementController;
use App\Http\Controllers\ListSettingController;
use Illuminate\Support\Facades\Route;


Route::get('/list-management', [
    ListManagementController::class,
    'index'
])->name('');

Route::post('/list-management/accounts', [
    ListManagementController::class,
    'getAccounts'
])->name('');
Route::post('/save-list-management', [
    ListManagementController::class,
    'save'
])->name('');



//resource sales
Route::get('/list/settings', [ListSettingController::class, 'index'])->name('show_all_list_settings');
Route::get('/list/settings/{id}', [ListSettingController::class, 'show'])->name('show_single_list_settings');
Route::put('/list/settings/{id}', [ListSettingController::class, 'update'])->name('edit_list_settings');
Route::delete('/list/settings/{id}', [ListSettingController::class, 'destroy'])->name('delete_sites_settings');
