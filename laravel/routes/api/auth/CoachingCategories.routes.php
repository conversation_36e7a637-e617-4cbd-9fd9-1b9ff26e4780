<?php

//resource

use App\Http\Controllers\Coaching\CategoryController;
use Illuminate\Support\Facades\Route;

Route::get('/coaching/categories',[CategoryController::class,'index'])->name('show_all_categories');
Route::post('/coaching/categories',[CategoryController::class,'store'])->name('create_categories');
Route::get('/coaching/categories/{category}',[CategoryController::class,'show'])->name('show_single_categories');
Route::put('/coaching/categories/{category}',[CategoryController::class,'update'])->name('edit_categories');
Route::delete('/coaching/categories/{category}',[CategoryController::class,'destroy'])->name('delete_categories');


// Tools 
Route::post('/coaching/importcategories', [CategoryController::class,'import'])->name('import_categories');
Route::post('/coaching/importupdatecategories', [CategoryController::class, 'updateByImport'])->name('import_bulk_edit');
Route::get('/coaching/downloadcategory/{filename}', [CategoryController::class,'export'])->name('download_template_categories');
Route::get('/coaching/exportcategories', [CategoryController::class,'exportcategories'])->name('export_xlsx_categories');
Route::get('/coaching/exportcategoriescsv', [CategoryController::class,'exportcsv'])->name('export_csv_categories');
Route::get('/coaching/exportcategorypdf', [CategoryController::class,'exportpdf'])->name('export_pdf_categories');

Route::get('/coaching/restorecategory', [CategoryController::class,'restore'])->name('restore_categories');

Route::post('/coaching/sendmailcategories', [CategoryController::class,'sendmail'])->name('export_email_categories');

