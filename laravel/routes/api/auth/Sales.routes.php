<?php


//resource sales
// Route::get('/sales',[SaleController::class,'index'])->name('show_all_sales');

use App\Http\Controllers\BranchMappingController;
use App\Http\Controllers\BranchSalesController;
use App\Http\Controllers\ContributionController;
use App\Http\Controllers\HigherOrderIncentiveSchemaController;
use App\Http\Controllers\IncentiveMappingController;
use App\Http\Controllers\KpiIncentiveSchemaController;
use App\Http\Controllers\LineDistributorSalesTypeController;
use App\Http\Controllers\MappingUnifiedCodeController;
use App\Http\Controllers\ProductWeightController;
use App\Http\Controllers\RoleSaleController;
use App\Http\Controllers\SaleController;
use App\Http\Controllers\SalesMappingController;
use App\Http\Controllers\SalesSettingsController;
use App\Http\Controllers\TargetController;
use App\Http\Controllers\TargetDetailsController;
use App\Http\Controllers\UploadSalesController;
use Illuminate\Support\Facades\Route;

Route::post('/sales/index', [SaleController::class, 'index'])->name('show_all_sales_mappings');
Route::post('/sales', [SaleController::class, 'store'])->name('create_sales');
Route::get('/sales/{id}', [SaleController::class, 'show'])->name('show_single_sales');
Route::put('/sales/{id}', [SaleController::class, 'update'])->name('edit_sales');
Route::delete('/sales/{id}', [SaleController::class, 'destroy'])->name('delete_sales');
Route::get('/sales/{ids}/details', [SaleController::class, 'getDistributionDetails'])->name('details-distribution-get');
Route::get('/get-sales-data', [SaleController::class, 'getSalesData'])->name('');

Route::get('/sales/template/{filename}', [SaleController::class, 'exportTemplate'])->name('download_template_sales');
Route::post('/sales/read', [SaleController::class, 'readFromFile'])->name('import_sales_mappings'); //Todo permissions not implemented yet
Route::post('/sales/save', [SaleController::class, 'saveFromFile'])->name('import_sales_mappings'); //Todo permissions not implemented yet
Route::get('/upload-sales', [UploadSalesController::class, 'index'])->name('import_sales');
Route::post('/get-sales-to-delete', [SaleController::class, 'getSalesDetailsToDelete'])->name('delete_sales_details');


//resource sales
Route::get('/sales-settings', [SalesSettingsController::class, 'index'])->name('show_all_sales_settings');
Route::post('/sales-settings', [SalesSettingsController::class, 'store'])->name('create_sales_settings');
Route::get('/sales-settings/{id}', [SalesSettingsController::class, 'show'])->name('show_single_sales_settings');
Route::put('/sales-settings/{id}', [SalesSettingsController::class, 'update'])->name('edit_sales_settings');
Route::delete('/sales-settings/{id}', [SalesSettingsController::class, 'destroy'])->name('delete_sites_settings');


// Branch Sales
Route::post('/branch/sales/index', [BranchSalesController::class, 'index'])->name('show_all_branch_sales');
Route::post('/branch/sales', [BranchSalesController::class, 'store'])->name('create_branch_sales');
Route::get('/branch/sales/{id}', [BranchSalesController::class, 'show'])->name('show_single_branch_sales');
Route::put('/branch/sales/{id}', [BranchSalesController::class, 'update'])->name('edit_branch_sales');
Route::delete('/branch/sales/{id}', [BranchSalesController::class, 'destroy'])->name('delete_branch_sales');
Route::post('/branch-sales/import', [BranchSalesController::class, 'import'])->name('import_branch_sales');
Route::get('/export/branch/sales', [BranchSalesController::class, 'exportBranchSales'])->name('import_branch_sales');

//resource sales mapping
// Route::get('/sales-mappings',[SalesMappingController::class,'index'])->name('show_all_sales_mappings');
Route::post('/sales-mappings', [SalesMappingController::class, 'store'])->name('create_sales_mappings');
Route::post('/sales-mappings/index', [SalesMappingController::class, 'index'])->name('show_all_sales_mappings');
Route::get('/sales-mappings/create', [SalesMappingController::class, 'create'])->name('create_sales_mappings');
Route::get('/sales-mappings/{id}/edit', [SalesMappingController::class, 'edit'])->name('edit_sales_mappings');
Route::get('/sales-mappings/{id}', [SalesMappingController::class, 'show'])->name('show_single_sales_mappings');
Route::put('/sales-mappings/{id}', [SalesMappingController::class, 'update'])->name('edit_sales_mappings');
Route::delete('/sales-mappings/{id}', [SalesMappingController::class, 'destroy'])->name('delete_sales_mappings');
Route::post('/replicate-mapping', [SalesMappingController::class, 'replicateMapping'])->name('show_all_sales_mappings');

Route::get('/get-mapping-data', [LineDistributorSalesTypeController::class, 'index'])->name(''); //Todo permissions not implemented yet
Route::get('/get-line-divisions-bricks/{line_id}', [LineDistributorSalesTypeController::class, 'getLineDivisions'])->name(''); //Todo permissions not implemented yet
Route::post('/sales-mappings/import', [SalesMappingController::class, 'import'])->name('import_sales_mappings');
Route::post('/sales-mappings/import/update', [SalesMappingController::class, 'updateByImport'])->name('import_bulk_edit');
Route::get('/mapping-details', [SalesMappingController::class, 'getFullMapping'])->name(''); //Todo permissions not implemented yet


// Branch Mapping
Route::post('/branch-mappings', [BranchMappingController::class, 'store'])->name('create_branch_mappings');
Route::post('/branch-mappings/index', [BranchMappingController::class, 'index'])->name('show_all_branch_mappings');
Route::get('/branch-mappings/create', [BranchMappingController::class, 'create'])->name('create_branch_mappings');
Route::get('/branch-mappings/{id}/edit', [BranchMappingController::class, 'edit'])->name('edit_branch_mappings');
Route::get('/branch-mappings/{id}', [BranchMappingController::class, 'show'])->name('show_single_branch_mappings');
Route::put('/branch-mappings/{id}', [BranchMappingController::class, 'update'])->name('edit_branch_mappings');
Route::delete('/branch-mappings/{id}', [BranchMappingController::class, 'destroy'])->name('delete_branch_mappings');
Route::post('/branch-mappings/import', [BranchMappingController::class, 'import'])->name('import_branch_mappings');
//resource contribution
Route::get('/contribution', [ContributionController::class, 'index'])->name('show_all_contributions');
Route::post('/contribution', [ContributionController::class, 'store'])->name('create_contributions');
Route::post('/get-contribution', [ContributionController::class, 'show'])->name('show_single_contributions');
Route::put('/contribution/{id}', [ContributionController::class, 'update'])->name('edit_contributions');
Route::delete('/contribution/{id}', [ContributionController::class, 'destroy'])->name('delete_contributions');

Route::get('/getLineData/{line}', [ContributionController::class, 'getLineData'])->name(''); //Todo permissions not implemented yet

Route::post('/import-contribution', [ContributionController::class, 'import'])->name('import_contributions');

//target Details
Route::post('/target-details/all', [TargetDetailsController::class, 'index'])->name('show_all_target_details');
Route::get('/target-details/data', [TargetDetailsController::class, 'linesAndTypes'])->name('');
Route::post('/target-details', [TargetDetailsController::class, 'store'])->name('create_target_details');
Route::post('/get-target-details', [TargetDetailsController::class, 'show'])->name('show_single_target_details');
Route::post('/get-targets-to-delete', [TargetDetailsController::class, 'getTargetDetailsToDelete'])->name('show_single_target_details');

Route::get('/getTargetDetailsData/{line}', [TargetDetailsController::class, 'getLineData'])->name(''); //Todo permissions not implemented yet

//resource target
Route::get('/target', [TargetController::class, 'index'])->name('show_all_targets');
Route::post('/target', [TargetController::class, 'store'])->name('create_targets');
// Route::get('/target/{id}',[TargetController::class,'show'])->name('show_single_targets');

Route::put('/target/{id}', [TargetController::class, 'update'])->name('edit_targets');
Route::delete('/target/{id}', [TargetController::class, 'destroy'])->name('delete_targets');
Route::post('/get-target-data', [TargetController::class, 'show'])->name('show_single_targets');

Route::post('/import-target', [TargetController::class, 'import'])->name('import_targets');
Route::post('/import-target-details', [TargetDetailsController::class, 'import'])->name('import_targets');


Route::post('/getTargetData', [TargetController::class, 'getLineData'])->name(''); //Todo permissions not implemented yet
Route::get('/get-line-products/{line}', [TargetController::class, 'getLineProducts'])->name(''); //Todo permissions not implemented yet

//resource Mapping Unified Code
Route::get('/unified-codes', [MappingUnifiedCodeController::class, 'index'])->name('show_all_unified_codes');
Route::post('/unified-codes', [MappingUnifiedCodeController::class, 'store'])->name('create_unified_codes');
Route::get('/unified-codes/{id}', [MappingUnifiedCodeController::class, 'show'])->name('show_single_unified_codes');
Route::put('/unified-codes/{id}', [MappingUnifiedCodeController::class, 'update'])->name('edit_unified_codes');
Route::delete('/unified-codes/{id}', [MappingUnifiedCodeController::class, 'destroy'])->name('delete_unified_codes');

Route::get('get-division-bricks/{div_id}', [MappingUnifiedCodeController::class, 'getDivisionBricks'])->name(''); //Todo permissions not implemented yet

Route::post('/importmappingunifiedcode', [MappingUnifiedCodeController::class, 'import'])->name('import_mapping_unified_codes');
Route::post('/importupdatemappingunifiedcode', [MappingUnifiedCodeController::class, 'updateByImport'])->name('import_bulk_edit');
Route::get('/export-unified', [MappingUnifiedCodeController::class, 'exportUnified'])->name('');
Route::get('/export_unified-csv', [MappingUnifiedCodeController::class, 'exportUnifiedCsv'])->name('');
// Route::get('/downloadmappingunifiedcode/{filename}', [MappingUnifiedCodeController::class, 'export'])->name('download_template_mapping_unified_codes');

Route::get('/sales/settings/roles', [RoleSaleController::class, 'index'])->name(''); // TODO: permissions not implemented yet
Route::post('/sales/settings/roles', [RoleSaleController::class, 'store'])->name(''); // TODO: permissions not implemented yet
Route::delete('/sales/settings/roles/{role}', [RoleSaleController::class, 'destroy'])->name(''); // TODO: permissions not implemented yet
Route::put('/sales/settings/roles/{role}', [RoleSaleController::class, 'update'])->name(''); // TODO: permissions not implemented yet


Route::get('/sales/settings/product-weights', [ProductWeightController::class, 'index'])->name('show_sales_product_weights_settings'); // TODO: permissions not implemented yet
Route::post('/sales/settings/product-weights', [ProductWeightController::class, 'store'])->name(''); // TODO: permissions not implemented yet


Route::get('/sales/settings/incentive-mappings', [IncentiveMappingController::class, 'index'])->name(''); // TODO:
Route::post('/sales/settings/incentive-mappings', [IncentiveMappingController::class, 'store'])->name(''); // TODO:
Route::put('/sales/settings/incentive-mappings/{incentiveMapping}', [IncentiveMappingController::class, 'update'])->name(''); // TODO:
Route::delete('/sales/settings/incentive-mappings/{incentiveMapping}', [IncentiveMappingController::class, 'destroy'])->name(''); // TODO:

Route::get('/sales/settings/kpi-incentives', [KpiIncentiveSchemaController::class, 'index'])->name(''); // TODO:
Route::post('/sales/settings/kpi-incentives', [KpiIncentiveSchemaController::class, 'store'])->name(''); // TODO:
Route::put('/sales/settings/kpi-incentives/{kpiIncentiveSchema}', [KpiIncentiveSchemaController::class, 'update'])->name(''); // TODO:
Route::delete('/sales/settings/kpi-incentives/{kpiIncentiveSchema}', [KpiIncentiveSchemaController::class, 'destroy'])->name(''); // TODO:

Route::get('/sales/settings/higher-order-incentives', [HigherOrderIncentiveSchemaController::class, 'index'])->name(''); // TODO:
Route::post('/sales/settings/higher-order-incentives', [HigherOrderIncentiveSchemaController::class, 'store'])->name(''); // TODO:
Route::put('/sales/settings/higher-order-incentives/{higherOrderIncentiveSchema}', [HigherOrderIncentiveSchemaController::class, 'update'])->name(''); // TODO:
Route::delete('/sales/settings/higher-order-incentives/{higherOrderIncentiveSchema}', [HigherOrderIncentiveSchemaController::class, 'destroy'])->name(''); // TODO:
