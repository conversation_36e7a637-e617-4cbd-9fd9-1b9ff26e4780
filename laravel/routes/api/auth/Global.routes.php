<?php

use App\Company;
use App\Helpers\Template;
use App\Http\Controllers\AttachmentController;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\CalendarController;
use App\Http\Controllers\CheckLocationController;
use App\Http\Controllers\CompanyController;
use App\Http\Controllers\LeaderboardController;
use App\Http\Controllers\AlertController;
use App\Http\Controllers\AutomaticExpenseController;
use App\Http\Controllers\CustomLinkController;
use App\Http\Controllers\ErrorMessageController;
use App\Http\Controllers\FieldDaysController;
use App\Http\Controllers\GoogleMapsController;
use App\Http\Controllers\Help\ImageController;
use App\Http\Controllers\ItGatesLogosController;
use App\Http\Controllers\KPISController;
use App\Http\Controllers\LineController;
use App\Http\Controllers\LocaleController;
use App\Http\Controllers\LockScreenController;
use App\Http\Controllers\MailController;
use App\Http\Controllers\NotesController;
use App\Http\Controllers\NotificationCenterController;
use App\Http\Controllers\NotificationController;
use App\Http\Controllers\OffDaysController;
use App\Http\Controllers\PolicyController;
use App\Http\Controllers\PowerBiSettingController;
use App\Http\Controllers\RoleSaleController;
use App\Http\Controllers\SettingController;
use App\Http\Controllers\SupportController;
use App\Http\Controllers\TemplateController;
use App\Http\Controllers\TestMailController;
use App\Http\Controllers\LinkedParmaciesSettingController;
use App\Http\Controllers\RolesController;
use App\Http\Controllers\ScheduledSettingController;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\TemporaryUploadController;
use App\LinkedParmaciesSetting;
use App\Models\CustomLink;
use App\Services\Kpis\FieldDays;

Route::post('logout', [AuthController::class, 'logout'])->name('logout');
Route::post('refresh', [AuthController::class, 'refresh'])->name('');
Route::get('show-check-location', [CheckLocationController::class, 'show'])->name('');
Route::post('check-location', [CheckLocationController::class, 'store'])->name('');
Route::post('add-automatic-expense', [AutomaticExpenseController::class, 'store'])->name('');


//lang
Route::get('langlist', [LocaleController::class, 'getLangList'])->name('');

//lock
Route::get('/lockscreen', [LockScreenController::class, 'get'])->name('lock_account');

// logos of itgates

Route::get('/logo/{logo}', [ItGatesLogosController::class, 'show'])->name('show_all_logos');

// import permission to role

Route::post('roles/import/permissions', [RolesController::class, 'import'])->name('');


//resource advanced in general settings
Route::get('/settings', [SettingController::class, 'index'])->name('show_all_advanced');
Route::post('/settings', [SettingController::class, 'store'])->name('create_advanced');
Route::get('/settings/create', [SettingController::class, 'create'])->name('create_advanced');
Route::get('/settings/{id}/edit', [SettingController::class, 'edit'])->name('edit_advanced');
Route::get('/settings/{id}', [SettingController::class, 'show'])->name('show_single_advanced');
Route::put('/settings/{id}', [SettingController::class, 'update'])->name('edit_advanced');
Route::delete('/settings/{id}', [SettingController::class, 'destroy'])->name('delete_advanced');


//resource Scheduled in general settings
Route::get('/scheduled', [ScheduledSettingController::class, 'index'])->name('show_all_scheduled');
Route::post('/scheduled', [ScheduledSettingController::class, 'store'])->name('create_scheduled');
Route::get('/scheduled/{id}', [ScheduledSettingController::class, 'show'])->name('show_single_scheduled');
Route::put('/scheduled/{id}', [ScheduledSettingController::class, 'update'])->name('edit_scheduled');
Route::delete('/scheduled/{id}', [ScheduledSettingController::class, 'destroy'])->name('delete_scheduled');

// resource company setting
Route::get('/companies', [CompanyController::class, 'index'])->name('');
Route::get('/companies/{company}', [CompanyController::class, 'show'])->name('show_all_company_logos');
Route::put('/companies/{id}', [CompanyController::class, 'update'])->name('edit_companies');
Route::delete('/companies/{id}', [CompanyController::class, 'destroy'])->name('delete_companies');

// resource leaderboards setting
Route::get('/leaderboards', [LeaderboardController::class, 'index'])->name('');
Route::post('/leaderboards', [LeaderboardController::class, 'store'])->name('create_leaderboard');
// Route::get('/leaderboards/{company}', [LeaderboardController::class, 'show'])->name('show_all_leaderboards');
Route::put('/leaderboards/{id}', [LeaderboardController::class, 'update'])->name('edit_leaderboard');
Route::delete('/leaderboards/{id}', [LeaderboardController::class, 'destroy'])->name('delete_leaderboard');

// resource alerts setting
Route::get('/alerts', [AlertController::class, 'index'])->name('');
Route::post('/alerts', [AlertController::class, 'store'])->name('create_alert');
Route::get('/alerts/users', [AlertController::class, 'getUsers'])->name('users_alert');
Route::get('/alerts/methods', [AlertController::class, 'getSendingMethods'])->name('methods_alert');
Route::get('/alerts/types', [AlertController::class, 'getAlertTypes'])->name('types_alert');
Route::get('/alerts/{id}', [AlertController::class, 'edit'])->name('edit_alert');
Route::put('/alerts/{id}', [AlertController::class, 'update'])->name('update_alert');
Route::delete('/alerts/{id}', [AlertController::class, 'destroy'])->name('delete_alert');
Route::post('sendalert', [AlertController::class, 'sendalert'])->name('send_data_alerts');
Route::post('/sendmail', [AlertController::class, 'sendmail'])->name('send_emaildata_alerts');
Route::post('/sendwidget', [AlertController::class, 'sendwidget'])->name('send_emaildata_alerts');
Route::post('/sendInternalMessage', [AlertController::class, 'sendInternalMessage'])->name('send_messagedata_alerts');
Route::post('/searchalert/index', [AlertController::class, 'search'])->name('send_messagedata_alerts');


//resource error messages
Route::get('/errormessages', [ErrorMessageController::class, 'index'])->name('show_all_error_messages');
Route::post('/errormessages', [ErrorMessageController::class, 'store'])->name('create_error_messages');
Route::get('/errormessages/create', [ErrorMessageController::class, 'create'])->name('create_error_messages');
Route::get('/errormessages/{id}/edit', [ErrorMessageController::class, 'edit'])->name('edit_error_messages');
Route::get('/errormessages/{id}', [ErrorMessageController::class, 'show'])->name('show_single_error_messages');
Route::put('/errormessages/{id}', [ErrorMessageController::class, 'update'])->name('edit_error_messages');
Route::delete('/errormessages/{id}', [ErrorMessageController::class, 'destroy'])->name('delete_error_messages');

// kpis
Route::post('kpis/{kpi}', [KPISController::class, 'store'])->name('create_kpis');
Route::put('kpis/{kpiRatio}', [KPISController::class, 'update'])->name(''); // TODO: Need to add permission
Route::delete('kpis/{kpiRatio}', [KPISController::class, 'destroy'])->name(''); // TODO: Need to add permission
Route::get('/get-kpis', [KPISController::class, 'getKPIs'])->name(''); // TODO: Need to add permission
Route::get('kpis/{kpi}', [KPISController::class, 'show'])->name(''); // TODO: Need to add permission
Route::post('save-kpi-percent', [KPISController::class, 'saveKpiRatioPercents'])->name(''); // TODO: Need to add permission

Route::post('show-percents',[KPISController::class,'getPercents'])->name('');

// field Days
Route::get('get-field-days', [FieldDaysController::class, 'index'])->name(''); // TODO: Need to add permission
Route::post('get-field-days-details', [FieldDaysController::class, 'getDetails'])->name(''); // TODO: Need to add permission
Route::get('field-days', [FieldDaysController::class, 'show'])->name(''); // TODO: Need to add permission
Route::post('field-days', [FieldDaysController::class, 'store'])->name(''); // TODO: Need to add permission
Route::delete('field-days/{fieldDay}', [FieldDaysController::class, 'destroy'])->name(''); // TODO: Need to add permission

//resource Off Days
Route::get('/off-days', [OffDaysController::class, 'index'])->name('show_all_off_days');
Route::post('/off-days', [OffDaysController::class, 'store'])->name('create_off_days');
Route::get('/off-days/{id}', [OffDaysController::class, 'show'])->name('show_single_off_days');
Route::put('/off-days/{id}', [OffDaysController::class, 'update'])->name('edit_off_days');
Route::delete('/off-days/{id}', [OffDaysController::class, 'destroy'])->name('delete_off_days');


//resource notes
Route::get('/notes', [NotesController::class, 'index'])->name('show_all_notes');
Route::post('/notes', [NotesController::class, 'store'])->name('create_notes');
Route::get('/notes/create', [NotesController::class, 'create'])->name('create_notes');
Route::get('/notes/{id}/edit', [NotesController::class, 'edit'])->name('edit_notes');
Route::get('/notes/{id}', [NotesController::class, 'show'])->name('show_single_notes');
Route::put('/notes/{id}', [NotesController::class, 'update'])->name('edit_notes');
Route::delete('/notes/{id}', [NotesController::class, 'destroy'])->name('delete_notes');

//calendar
Route::get('calendar/{user}', [CalendarController::class, 'index'])->name('show_menu_calendar');
Route::get('calendar-lines', [CalendarController::class, 'getLines'])->name('');
Route::post('calendar-positions', [CalendarController::class, 'getPositions'])->name('');
Route::post('calendar-users', [CalendarController::class, 'getUsers'])->name('');


//resource support
Route::get('/supports', [SupportController::class, 'index'])->name('show_all_support');
Route::post('/supports', [SupportController::class, 'store'])->name('create_supports');
Route::get('/supports/{id}', [SupportController::class, 'show'])->name('show_single_supports');
Route::put('/supports/{id}', [SupportController::class, 'update'])->name('edit_supports');
Route::delete('/supports/{id}', [SupportController::class, 'destroy'])->name('delete_supports');
Route::get('/send/{id}', [TestMailController::class, 'sendEmail']);


//resource mail
Route::get('/mail', [MailController::class, 'index'])->name('show_all_mails');
Route::post('/mail', [MailController::class, 'store'])->name('create_mails');
Route::get('/mail/create', [MailController::class, 'create'])->name('create_mails');
Route::get('/mail/{id}/edit', [MailController::class, 'edit'])->name('edit_mails');
Route::get('/mail/{id}', [MailController::class, 'show'])->name('show_single_mails');
Route::put('/mail/{id}', [MailController::class, 'update'])->name('edit_mails');
Route::delete('/mail/{id}', [MailController::class, 'destroy'])->name('delete_mails');

Route::get('prepareSend/{id}', [MailController::class, 'prepareSend'])->name('show_all_mails');
Route::post('mailSend/{id}', [MailController::class, 'send'])->name('show_all_mails');

// upload image with folder name
Route::post('/upload/images', [ImageController::class, 'store'])->name('');
Route::post("/upload/attachment", [AttachmentController::class, 'saveFile'])->name(""); //TODO:permission is not implemented yet
Route::delete("/attachments/{id}", [AttachmentController::class, 'destroy'])->name(""); //TODO:permission is not implemented yet
Route::post("/upload/attachments", [AttachmentController::class, 'saveFiles'])->name(""); //TODO:permission is not implemented yet
Route::post("/upload/mobile/attachments", [AttachmentController::class, 'saveMobileFiles'])->name(""); //TODO:permission is not implemented yet

Route::get('/download/template/{filename}', [TemplateController::class, 'show'])->name('download_all_templates');
Route::Post('/export', [TemplateController::class, 'show'])->name('export_files');


Route::get("/notifications", [NotificationController::class, 'index'])->name(""); //TODO:permission is not implemented yet

Route::get("/notification-center", [NotificationCenterController::class, 'index'])->name("show_all_notification_center");
Route::get("/notification-center/{id}", [NotificationCenterController::class, 'show'])->name("show_single_notification_center");
Route::put("/notification-center/{id}", [NotificationCenterController::class, 'update'])->name("edit_notification_center");
Route::get("/notifications/read/{id}", [NotificationController::class, 'markAsRead'])->name(""); //TODO:permission is not implemented yet
Route::delete("/notifications/delete/{id}", [NotificationController::class, 'deleteNotify'])->name(""); //TODO:permission is not implemented yet
Route::post("/save-notification-center", [NotificationCenterController::class, 'save'])->name(""); //TODO:permission is not implemented yet

Route::put("/temp-upload", [TemporaryUploadController::class, 'update'])->name(""); //TODO:permission is not implemented yet

Route::get("/power-bi", [PowerBiSettingController::class, 'index'])->name(""); //TODO:permission is not implemented yet
Route::post("/power-bi", [PowerBiSettingController::class, 'store'])->name(""); //TODO:permission is not implemented yet
Route::get("/power-bi/{id}", [PowerBiSettingController::class, 'show'])->name(""); //TODO:permission is not implemented yet
Route::put("/power-bi/{id}", [PowerBiSettingController::class, 'update'])->name(""); //TODO:permission is not implemented yet
Route::delete("/power-bi/{id}", [PowerBiSettingController::class, 'delete'])->name(""); //TODO:permission is not implemented yet


// resource policies setting
Route::get('/policies', [PolicyController::class, 'index'])->name('show_all_policies');
Route::post('/policies', [PolicyController::class, 'store'])->name('');
Route::get('/policies/{policy}', [PolicyController::class, 'show'])->name('show_single_policies');
Route::put('/policies/{id}', [PolicyController::class, 'update'])->name('edit_policies');
Route::delete('/policies/{id}', [PolicyController::class, 'destroy'])->name('delete_policies');
Route::get('/policy-models', [PolicyController::class, 'models'])->name('');


// resource linked pharmacies setting
Route::get('/linked-pharmacies-setting', [LinkedParmaciesSettingController::class, 'index'])->name('show_all_linked_pharmacies_settings');
Route::post('/linked-pharmacies-setting', [LinkedParmaciesSettingController::class, 'store'])->name('');
Route::get('/linked-pharmacies-setting/{linked}', [LinkedParmaciesSettingController::class, 'show'])->name('show_single_linked_pharmacies_setting');
Route::put('/linked-pharmacies-setting/{id}', [LinkedParmaciesSettingController::class, 'update'])->name('edit_linked_pharmacies_setting');
Route::delete('/linked-pharmacies-setting/{id}', [LinkedParmaciesSettingController::class, 'destroy'])->name('delete_linked_pharmacies_setting');


// get google maps
Route::get("maps/credentials", GoogleMapsController::class)->name("");

Route::get("sales/date/limit", [RoleSaleController::class, "show"])->name("");


// Custom Links

// resource policies setting
Route::get('/customized/links', [CustomLinkController::class, 'index'])->name('');
Route::post('/customized/links', [CustomLinkController::class, 'store'])->name('create_customized_links');
Route::get('/customized/links/{id}', [CustomLinkController::class, 'show'])->name('show_single_customized_links');
Route::put('/customized/links/{id}', [CustomLinkController::class, 'update'])->name('edit_customized_links');
Route::delete('/customized/links/{id}', [CustomLinkController::class, 'destroy'])->name('delete_customized_links');
