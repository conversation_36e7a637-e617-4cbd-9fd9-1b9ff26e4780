<?php

use App\Http\Controllers\FavouriteListController;
use App\Http\Controllers\KolListController;
use App\Http\Controllers\KolListPerAccountController;
use Illuminate\Support\Facades\Route;

Route::post('/kol-list', [KolListController::class, 'index'])->name('show_all_kol_lists');
Route::post('/kol-lists', [KolListController::class, 'store'])->name('create_kol_lists');
Route::post('/reset-kol-lists', [KolListController::class, 'resetKolList'])->name('reset_kol_lists');



Route::post('/kol-list-account', [KolListPerAccountController::class, 'index'])->name('show_all_kol_lists');
Route::post('/kol-lists-account', [KolListPerAccountController::class, 'store'])->name('create_kol_lists');
