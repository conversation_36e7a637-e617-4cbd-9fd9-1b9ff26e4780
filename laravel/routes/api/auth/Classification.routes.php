<?php

use App\Http\Controllers\ClassificationController;
use Illuminate\Support\Facades\Route;


//resource
Route::get('/classifications',[ClassificationController::class,'index'])->name('show_all_classifications');
Route::post('/classifications',[ClassificationController::class,'store'])->name('create_classifications');
Route::get('/classifications/create',[ClassificationController::class,'create'])->name('create_classifications');
Route::get('/classifications/{id}/edit',[ClassificationController::class,'edit'])->name('edit_classifications');
Route::get('/classifications/{id}',[ClassificationController::class,'show'])->name('show_single_classifications');
Route::put('/classifications/{id}',[ClassificationController::class,'update'])->name('edit_classifications');
Route::delete('/classifications/{id}',[ClassificationController::class,'destroy'])->name('delete_classifications');

Route::post('/importclassifications', [ClassificationController::class,'import'])->name('import_classifications');
Route::post('/importupdateclassifications', [ClassificationController::class, 'updateByImport'])->name('import_bulk_edit');
Route::get('/downloadclassification/{filename}', [ClassificationController::class,'export'])->name('download_template_classifications');
Route::get('/exportclassifications', [ClassificationController::class,'exportclassifications'])->name('export_xlsx_classifications');
Route::get('/exportclassificationscsv', [ClassificationController::class,'exportcsv'])->name('export_csv_classifications');
Route::get('/exportclassificationpdf', [ClassificationController::class,'exportpdf'])->name('export_pdf_classifications');
Route::post('/sendmailclassifications', [ClassificationController::class,'sendmail'])->name('export_email_classifications');
