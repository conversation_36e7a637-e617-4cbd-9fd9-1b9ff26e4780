<?php

use App\Http\Controllers\MessageController;
use Illuminate\Support\Facades\Route;

Route::post('/message', [MessageController::class, 'store'])->name(""); //TODO:permission is not implemented yet
Route::get("/message", [MessageController::class, 'inbox'])->name(""); //TODO:permission is not implemented yet
Route::get("/sent-message", [MessageController::class, 'sentMessages'])->name(""); //TODO:permission is not implemented yet
Route::get("/trashed-message", [MessageController::class, 'trashedMessages'])->name(""); //TODO:permission is not implemented yet
// Route::get("/message", [MessageController::class, 'receivedMessages'])->name(""); //TODO:permission is not implemented yet
Route::get("/message/{message}", [MessageController::class, 'show'])->name(""); //TODO:permission is not implemented yet
Route::get("/previous-message/{message}", [MessageController::class, 'previousMessage'])->name(""); //TODO:permission is not implemented yet
Route::put("/message/{message}", [MessageController::class, 'update'])->name("");//TODO:permission is not implemented yet
Route::put("/update-message/{message}", [MessageController::class, 'update'])->name("");//TODO:permission is not implemented yet
Route::put("/undo-message/{message}", [MessageController::class, 'undoMessages'])->name("");//TODO:permission is not implemented yet
Route::delete("/delete-message/{message}", [MessageController::class, 'delete'])->name("");//TODO:permission is not implemented yet
Route::get('/message-to-users', [MessageController::class, 'users'])->name('');

