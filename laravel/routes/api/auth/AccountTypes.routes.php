<?php

use App\Http\Controllers\AccountTypeController;
use Illuminate\Support\Facades\Route;

//resource
Route::get('/accounttypes',[AccountTypeController::class,'index'])->name('show_all_account_types');
Route::post('/accounttypes',[AccountTypeController::class,'store'])->name('create_account_types');
Route::get('/accounttypes/create',[AccountTypeController::class,'create'])->name('create_account_types');
Route::get('/accounttypes/{id}/edit',[AccountTypeController::class,'edit'])->name('edit_account_types');
Route::get('/accounttypes/{id}',[AccountTypeController::class,'show'])->name('show_single_account_types');
Route::put('/accounttypes/{id}',[AccountTypeController::class,'update'])->name('edit_account_types');
Route::delete('/accounttypes/{id}',[AccountTypeController::class,'destroy'])->name('delete_account_types');

Route::post('/importaccounttypes', [AccountTypeController::class, 'import'])->name('import_account_types');
Route::post('/importupdateaccounttypes', [AccountTypeController::class, 'updateByImport'])->name('import_bulk_edit');
Route::get('/downloadaccounttype/{filename}', [AccountTypeController::class, 'export'])->name('download_template_account_types');
Route::get('/exportaccounttypes', [AccountTypeController::class, 'exportaccounttypes'])->name('export_xlsx_account_types');
Route::get('/exportaccounttypescsv', [AccountTypeController::class, 'exportcsv'])->name('export_csv_account_types');
Route::get('/exportaccounttypepdf', [AccountTypeController::class, 'exportpdf'])->name('export_pdf_account_types');
Route::post('/sendmailaccounttypes', [AccountTypeController::class, 'sendmail'])->name('export_email_account_types');
Route::get('/restoreaccounttype', [AccountTypeController::class, 'restore'])->name('restore_account_types');
Route::get('/get_accounttype_subtypes/{id}', [AccountTypeController::class, 'getSubTypes'])->name('show_all_sub_types');
