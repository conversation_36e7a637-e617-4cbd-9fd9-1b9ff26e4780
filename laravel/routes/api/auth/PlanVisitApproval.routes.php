<?php

use App\Http\Controllers\ActualVisitApprovalController;
use App\Http\Controllers\ChangePlanApprovalController;
use App\Http\Controllers\CommercialApprovalController;
use App\Http\Controllers\CommercialBillApprovalController;
use App\Http\Controllers\ExpenseApprovalController;
use App\Http\Controllers\MaterialApprovalController;
use App\Http\Controllers\PlanVisitApprovalController;
use App\Http\Controllers\PvApprovalController;
use App\Http\Controllers\VacationApprovalController;
use Illuminate\Support\Facades\Route;

Route::get('/planapprovals', [PlanVisitApprovalController::class, 'index'])->name('show_all_plan_approvals');
Route::post('/filter-of-employees', [PlanVisitApprovalController::class, 'filterOfEmployees'])->name(''); //TODO:permission is not implemented yet
Route::get('/userswithplans', [PlanVisitApprovalController::class, 'usersWithPlans'])->name(''); //TODO:permission is not implemented yet
Route::post('/getPlanVisitsForApproval', [PlanVisitApprovalController::class, 'getPlanVisitsForApproval'])->name(''); //TODO:permission is not implemented yet
Route::post('/plan/accept', [PlanVisitApprovalController::class, 'accept_plan_visit'])->name('create_approve_plan');
Route::post('/plan/reject', [PlanVisitApprovalController::class, 'reject_plan_visit'])->name('create_disapprove_plan');


Route::post('/filterofemployees', [VacationApprovalController::class, 'filterOfEmployees'])->name(''); //TODO:permission is not implemented yet
Route::post('/get-vacations', [VacationApprovalController::class, 'getVacations'])->name(''); //TODO:permission is not implemented yet
Route::post('/vacation/accept', [VacationApprovalController::class, 'accept'])->name('');
Route::post('/vacation/reject', [VacationApprovalController::class, 'reject'])->name('');

Route::post('/actual-approval-employees', [ActualVisitApprovalController::class, 'filterOfEmployees'])->name(''); //TODO:permission is not implemented yet
Route::post('/getActualVisitsForApproval', [ActualVisitApprovalController::class, 'getActualVisits'])->name(''); //TODO:permission is not implemented yet
Route::post('/actual/accept', [ActualVisitApprovalController::class, 'accept'])->name('');
Route::post('/actual/reject', [ActualVisitApprovalController::class, 'reject'])->name('');


Route::post('/commercial-approval-employees', [CommercialApprovalController::class, 'filterOfEmployees'])->name(''); //TODO:permission is not implemented yet
Route::post('/get-commercials', [CommercialApprovalController::class, 'getCommercials'])->name(''); //TODO:permission is not implemented yet
Route::post('/commercial/accept', [CommercialApprovalController::class, 'accept'])->name('');
Route::post('/commercial/reject', [CommercialApprovalController::class, 'reject'])->name('');
Route::post('/commercial/accept/one', [CommercialApprovalController::class, 'acceptOne'])->name('');
Route::post('/commercial/reject/one', [CommercialApprovalController::class, 'rejectOne'])->name('');



Route::post('/material-approval-employees', [MaterialApprovalController::class, 'filterOfEmployees'])->name(''); //TODO:permission is not implemented yet
Route::post('/get-materials', [MaterialApprovalController::class, 'getMaterials'])->name(''); //TODO:permission is not implemented yet
Route::post('/material/accept', [MaterialApprovalController::class, 'accept'])->name('create_approve_material');
Route::post('/material/reject', [MaterialApprovalController::class, 'reject'])->name('create_disapprove_material');
Route::post('/material/accept/one', [MaterialApprovalController::class, 'acceptOne'])->name('create_approve_material');
Route::post('/material/reject/one', [MaterialApprovalController::class, 'rejectOne'])->name('create_disapprove_material');


Route::post('/commercial-bills-approval-employees', [CommercialBillApprovalController::class, 'filterOfEmployees'])->name(''); //TODO:permission is not implemented yet
Route::post('/get-commercial-bills', [CommercialBillApprovalController::class, 'getCommercials'])->name(''); //TODO:permission is not implemented yet
Route::post('/commercial/bills/accept', [CommercialBillApprovalController::class, 'accept'])->name('');
Route::post('/commercial/bills/reject', [CommercialBillApprovalController::class, 'reject'])->name('');
Route::post('/commercial/bills/accept/one', [CommercialBillApprovalController::class, 'acceptOne'])->name('');
Route::post('/commercial/bills/reject/one', [CommercialBillApprovalController::class, 'rejectOne'])->name('');
Route::post('/commercial-bills-approval-data', [CommercialBillApprovalController::class, 'billFlow'])->name(''); //TODO:permission is not implemented yet

Route::post('/expense-approval-employees', [ExpenseApprovalController::class, 'filterOfEmployees'])->name(''); //TODO:permission is not implemented yet
Route::post('/get-expenses', [ExpenseApprovalController::class, 'getExpenses'])->name(''); //TODO:permission is not implemented yet
Route::post('/expense/accept', [ExpenseApprovalController::class, 'accept'])->name('');
Route::post('/expense/reject', [ExpenseApprovalController::class, 'reject'])->name('');

Route::post('/change-plan-approval-employees', [ChangePlanApprovalController::class, 'filterOfEmployees'])->name(''); //TODO:permission is not implemented yet
Route::post('/get-change-plan', [ChangePlanApprovalController::class, 'getChangePlans'])->name(''); //TODO:permission is not implemented yet
Route::post('/change-plan/accept', [ChangePlanApprovalController::class, 'accept'])->name('');
Route::post('/change-plan/reject', [ChangePlanApprovalController::class, 'reject'])->name('');


Route::post('/pv-approval-employees', [PvApprovalController::class, 'filterOfEmployees'])->name(''); //TODO:permission is not implemented yet
Route::post('/get-pvs', [PvApprovalController::class, 'getChangePlans'])->name(''); //TODO:permission is not implemented yet
Route::post('/pv/accept', [PvApprovalController::class, 'accept'])->name('');
Route::post('/pv/reject', [PvApprovalController::class, 'reject'])->name('');

