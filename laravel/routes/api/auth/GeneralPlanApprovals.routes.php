<?php

use App\Http\Controllers\ApprovableController;
use App\Http\Controllers\ApprovalSettingController;
use App\Http\Controllers\GeneralApprovalSettingController;
use App\Http\Controllers\PlanableController;
use Illuminate\Support\Facades\Route;

//resource
Route::get('/approval-settings',[ApprovalSettingController::class,'index'])->name('show_all_general_approval_settings');
Route::get('/approval-settings/{id}',[ApprovalSettingController::class,'show'])->name('show_single_general_approval_settings');
Route::put('/approval-settings/{id}',[ApprovalSettingController::class,'update'])->name('edit_general_approval_settings');

//resource
Route::get('/approval-setting',[GeneralApprovalSettingController::class,'index'])->name('show_all_approval_settings');
Route::post('/approval-setting',[GeneralApprovalSettingController::class,'store'])->name('create_approval_settings');
Route::get('/approval-setting/{id}',[GeneralApprovalSettingController::class,'show'])->name('show_single_approval_settings');
Route::put('/approval-setting/{id}',[GeneralApprovalSettingController::class,'update'])->name('edit_error_messages');
Route::post('/approval-setting-delete',[GeneralApprovalSettingController::class,'destroy'])->name('delete_approval_settings');

Route::get('get-approval-settings/{id}/{type}',[GeneralApprovalSettingController::class,'getPlanableWithSettings'])->name('show_all_approval_settings');
Route::get('planables/{line_id}', [PlanableController::class,'index'])->name('show_all_approval_settings');
Route::post('approvables/{line_id}',[ApprovableController::class,'index'])->name('show_all_approval_settings');
Route::get('requesttypes',[GeneralApprovalSettingController::class,'requestTypes'])->name('');
