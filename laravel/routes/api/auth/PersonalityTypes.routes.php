<?php

use App\Http\Controllers\PersonalityTypeController;
use Illuminate\Support\Facades\Route;


//resource
Route::get('/personalitytypes',[PersonalityTypeController::class,'index'])->name('show_all_personalitytypes');
Route::post('/personalitytypes',[PersonalityTypeController::class,'store'])->name('create_personalitytypes');
Route::get('/personalitytypes/create',[PersonalityTypeController::class,'create'])->name('create_personalitytypes');
Route::get('/personalitytypes/{id}/edit',[PersonalityTypeController::class,'edit'])->name('edit_personalitytypes');
Route::get('/personalitytypes/{id}',[PersonalityTypeController::class,'show'])->name('show_single_personalitytypes');
Route::put('/personalitytypes/{id}',[PersonalityTypeController::class,'update'])->name('edit_personalitytypes');
Route::delete('/personalitytypes/{id}',[PersonalityTypeController::class,'destroy'])->name('delete_personalitytypes');

Route::post('/importpersonalitytypes', [PersonalityTypeController::class, 'import'])->name('import_personalitytypes');
Route::post('/importupdatepersonalitytypes', [PersonalityTypeController::class, 'updateByImport'])->name('import_bulk_edit');
Route::get('/downloadpersonalitytype/{filename}', [PersonalityTypeController::class, 'export'])->name('download_template_personalitytypes');
Route::get('/exportpersonalitytypes', [PersonalityTypeController::class, 'exportpersonalitytypes'])->name('export_xlsx_personalitytypes');
Route::get('/exportpersonalitytypepdf', [PersonalityTypeController::class, 'exportpdf'])->name('export_pdf_personalitytypes');
Route::post('/sendmailpersonalitytypes', [PersonalityTypeController::class, 'sendmail'])->name('export_email_personalitytypes');
Route::get('/exportpersonalitytypescsv', [PersonalityTypeController::class, 'exportcsv'])->name('export_csv_personalitytypes');
