<?php

use App\Http\Controllers\SpecialityController;
use Illuminate\Support\Facades\Route;


//resource specialities
Route::get('/specialities',[SpecialityController::class,'index'])->name('show_all_specialities');
Route::post('/specialities',[SpecialityController::class,'store'])->name('create_specialities');
Route::get('/specialities/create',[SpecialityController::class,'create'])->name('create_specialities');
Route::get('/specialities/{id}/edit',[SpecialityController::class,'edit'])->name('edit_specialities');
Route::get('/specialities/{id}',[SpecialityController::class,'show'])->name('show_single_specialities');
Route::put('/specialities/{id}',[SpecialityController::class,'update'])->name('edit_specialities');
Route::delete('/specialities/{id}',[SpecialityController::class,'destroy'])->name('delete_specialities');

Route::post('/importspecialities', [SpecialityController::class, 'import'])->name('import_specialities');
// Route::post('/importupdatespecialities', [SpecialityController::class, 'updateByImport'])->name('import_bulk_edit');
Route::get('/downloadspeciality/{filename}', [SpecialityController::class, 'export'])->name('download_template_specialities');
Route::get('/exportspecialities', [SpecialityController::class, 'exportspecialities'])->name('export_xlsx_specialities');
Route::get('/exportspecialitiescsv', [SpecialityController::class, 'exportcsv'])->name('export_csv_specialities');
Route::get('/exportspecialitypdf', [SpecialityController::class, 'exportpdf'])->name('export_pdf_specialities');
Route::post('/sendmailspecialities', [SpecialityController::class, 'sendmail'])->name('export_email_specialities');
Route::get('/restorespeciality', [SpecialityController::class, 'restore'])->name('restore_specialities');
