<?php

use App\Http\Controllers\ProducttypeController;
use Illuminate\Support\Facades\Route;


//resource product types
Route::get('/product_types',[ProducttypeController::class,'index'])->name('show_all_producttypes');
Route::post('/product_types',[ProducttypeController::class,'store'])->name('create_producttypes');
Route::get('/product_types/{id}/edit',[ProducttypeController::class,'edit'])->name('edit_producttypes');
Route::get('/product_types/{id}',[ProducttypeController::class,'show'])->name('show_single_producttypes');
Route::put('/product_types/{id}',[ProducttypeController::class,'update'])->name('edit_producttypes');
Route::delete('/product_types/{id}',[ProducttypeController::class,'destroy'])->name('delete_producttypes');

Route::post('/importproduct_types', [ProducttypeController::class,'import'])->name('import_producttypes');
Route::post('/importupdateproducttypes', [ProducttypeController::class, 'updateByImport'])->name('import_bulk_edit');
Route::get('/downloadproduct_type/{filename}', [ProducttypeController::class,'export'])->name('download_template_producttypes');
Route::get('/exportproduct_types', [ProducttypeController::class,'exportproduct_types'])->name('export_xlsx_producttypes');
Route::get('/exportproduct_typepdf', [ProducttypeController::class,'exportpdf'])->name('export_pdf_producttypes');
Route::post('/sendmailproduct_types', [ProducttypeController::class,'sendmail'])->name('export_email_producttypes');
Route::get('/exportproduct_typescsv', [ProducttypeController::class,'exportcsv'])->name('export_csv_producttypes');
