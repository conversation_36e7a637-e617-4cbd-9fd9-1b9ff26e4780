<?php

use App\Http\Controllers\FavouriteListController;
use App\Http\Controllers\FavouriteListPerAccountController;
use Illuminate\Support\Facades\Route;

Route::post('/favourite-list', [FavouriteListController::class, 'index'])->name('show_all_favourite_lists');
Route::post('/get-dates', [FavouriteListController::class, 'getDate'])->name('');
Route::post('/favourite-list-account', [FavouriteListPerAccountController::class, 'index'])->name('show_all_favourite_lists');
Route::post('/favourite-list-accounts/approvals', [FavouriteListController::class, 'getAccounts'])->name('');
Route::post('/favourite-list/accept', [FavouriteListController::class, 'accept'])->name('create_approve_active_inactive'); //TODO:permission is not implemented yet
Route::post('/favourite-list/reject', [FavouriteListController::class, 'reject'])->name('create_disapprove_active_inactive'); //TODO:permission is not implemented yet
Route::post('/favourite-users', [FavouriteListController::class, 'filterOfEmployees'])->name('');
Route::post('/favourite-lists', [FavouriteListController::class, 'store'])->name('create_favourite_lists');
Route::post('/favourite-lists-account', [FavouriteListPerAccountController::class, 'store'])->name('create_favourite_lists');

// Route::get('/favourite-lists',[FavouriteListController::class,'show'])->name('show_single_favourite_lists');
// Route::put('/favourite-lists/{id}',[FavouriteListController::class,'update'])->name('edit_favourite_lists');
// Route::get('/favourite-list-lines', [FavouriteListController::class, 'lines'])->name('');
// Route::get('/favourite-list-data/{line}', [FavouriteListController::class, 'lineDivisions'])->name('');
Route::post('/favourite-list-bricks', [FavouriteListController::class, 'divisionBricks'])->name('');
