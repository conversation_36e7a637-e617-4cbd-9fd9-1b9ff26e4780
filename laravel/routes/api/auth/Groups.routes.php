<?php

use App\Http\Controllers\Groups\GroupingAccountDoctorsController;
use App\Http\Controllers\Groups\GroupingAccountLinesController;
use App\Http\Controllers\Groups\GroupingSettingController;
use Illuminate\Support\Facades\Route;

//resource Grouping Settings 
Route::get('/grouping/settings', [GroupingSettingController::class, 'index'])->name('show_settings_grouping_settings');
Route::post('/grouping/settings', [GroupingSettingController::class, 'store'])->name('show_settings_grouping_settings');
Route::get('/grouping/settings/{id}', [GroupingSettingController::class, 'show'])->name('show_settings_grouping_settings');
Route::put('/grouping/settings/{id}', [GroupingSettingController::class, 'update'])->name('show_settings_grouping_settings');
Route::delete('/grouping/settings/{id}', [GroupingSettingController::class, 'destroy'])->name('show_settings_grouping_settings');


Route::post('/grouping/account/lines', [GroupingAccountLinesController::class, 'index'])->name('show_settings_account_lines_grouping');
Route::post('/save/grouping/account/lines', [GroupingAccountLinesController::class, 'store'])->name('show_settings_account_lines_grouping');

Route::post('/grouping/account/doctors', [GroupingAccountDoctorsController::class, 'index'])->name('show_settings_account_doctors_grouping');
Route::post('/save/grouping/account/doctors', [GroupingAccountDoctorsController::class, 'store'])->name('show_settings_account_doctors_grouping');
