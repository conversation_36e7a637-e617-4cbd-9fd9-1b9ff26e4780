<?php

use App\Http\Controllers\QuizCategoryController;
use App\Http\Controllers\QuizQuestionController;
use App\Http\Controllers\QuizAnswerController;
use App\Http\Controllers\QuizController;

use Illuminate\Support\Facades\Route;











// Quiz Resource
Route::get('/quiz', [QuizController::class, 'index'])->name('show_all_quiz');
Route::post('/quiz', [QuizController::class, 'store'])->name('create_quiz');
Route::get('/quiz/{id}', [QuizController::class, 'show'])->name('show_single_quiz');
Route::put('/quiz/{id}', [QuizController::class, 'update'])->name('edit_quiz');
Route::delete('/quiz/{quiz}/{user}', [QuizController::class, 'destroy'])->name('delete_quiz');
Route::get('/get-line-users/{line}', [QuizController::class, 'getLineUsers'])->name('');
Route::post('/quiz-user-answers', [QuizController::class, 'user_answers'])->name('');
Route::get('/quiz/{quiz}/user/{user}', [QuizController::class, 'showAnswers'])->name('');
Route::delete('/delete-quiz/{quiz}', [QuizController::class, 'delete'])->name('delete_quiz');


// Quiz Category Resource
Route::get('/quiz-category', [QuizCategoryController::class, 'index'])->name('show_all_quiz_category');
Route::post('/quiz-category', [QuizCategoryController::class, 'store'])->name('create_quiz_category');
Route::get('/quiz-category/{id}', [QuizCategoryController::class, 'show'])->name('show_single_quiz_category');
Route::put('/quiz-category/{id}', [QuizCategoryController::class, 'update'])->name('edit_quiz_category');
Route::delete('/quiz-category/{id}', [QuizCategoryController::class, 'destroy'])->name('delete_quiz_category');

// Quiz Question Resource
Route::get('/quiz-question', [QuizQuestionController::class, 'index'])->name('show_all_quiz_question');
Route::get('/quiz-question-levels', [QuizQuestionController::class, 'question_levels'])->name('');
Route::post('/quiz-question', [QuizQuestionController::class, 'store'])->name('create_quiz_question');
Route::get('/quiz-question/{id}', [QuizQuestionController::class, 'show'])->name('show_single_quiz_question');
Route::get('/edit-quiz-question/{id}', [QuizQuestionController::class, 'edit'])->name('show_single_quiz_question');
Route::put('/quiz-question/{id}', [QuizQuestionController::class, 'update'])->name('edit_quiz_question');
Route::delete('/quiz-question/{id}', [QuizQuestionController::class, 'destroy'])->name('delete_quiz_question');

Route::post('/importquizquestions', [QuizQuestionController::class, 'import'])->name('import_quizquestions');
Route::post('/importupdatequizquestions', [QuizQuestionController::class, 'updateByImport'])->name('import_bulk_edit');
Route::get('/exportquizquestions', [QuizQuestionController::class, 'exportquizquestions'])->name('export_xlsx_quizquestions');
Route::get('/exportquizquestionpdf', [QuizQuestionController::class, 'exportpdf'])->name('export_pdf_quizquestions');
Route::post('/sendmailQuizQuestions', [QuizQuestionController::class, 'sendmail'])->name('export_email_quizquestions');
Route::get('/exportquizquestionscsv', [QuizQuestionController::class, 'exportcsv'])->name('export_csv_quizquestions');





// Quiz Answer Resource
Route::get('/quiz-answer', [QuizAnswerController::class, 'index'])->name('show_all_quiz_answer');
Route::post('/quiz-answer', [QuizAnswerController::class, 'store'])->name('create_quiz_answer');
Route::get('/quiz-answer/{id}', [QuizAnswerController::class, 'show'])->name('show_single_quiz_answer');
Route::put('/quiz-answer/{id}', [QuizAnswerController::class, 'update'])->name('edit_quiz_answer');
Route::delete('/quiz-answer/{id}', [QuizAnswerController::class, 'destroy'])->name('delete_quiz_answer');
