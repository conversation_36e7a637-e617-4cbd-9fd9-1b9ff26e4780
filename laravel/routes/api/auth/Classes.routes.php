<?php

use App\Http\Controllers\ClassesController;
use Illuminate\Support\Facades\Route;


//resource
Route::get('/classes',[ClassesController::class,'index'])->name('show_all_classes');
Route::post('/classes',[ClassesController::class,'store'])->name('create_classes');
Route::get('/classes/create',[ClassesController::class,'create'])->name('create_classes');
Route::get('/classes/{id}/edit',[ClassesController::class,'edit'])->name('edit_classes');
Route::get('/classes/{id}',[ClassesController::class,'show'])->name('show_single_classes');
Route::put('/classes/{id}',[ClassesController::class,'update'])->name('edit_classes');
Route::delete('/classes/{id}',[ClassesController::class,'destroy'])->name('delete_classes');

Route::post('/importclasses', [ClassesController::class, 'import'])->name('import_classes');
Route::post('/importupdateclasses', [ClassesController::class, 'updateByImport'])->name('import_bulk_edit');
Route::get('/downloadclasses/{filename}', [ClassesController::class, 'export'])->name('download_template_classes');
Route::get('/exportclasses', [ClassesController::class, 'exportclasses'])->name('export_xlsx_classes');
Route::get('/exportclassescsv', [ClassesController::class, 'exportcsv'])->name('export_csv_classes');
Route::get('/exportclassespdf', [ClassesController::class, 'exportpdf'])->name('export_pdf_classes');
Route::post('/sendmailclasses', [ClassesController::class, 'sendmail'])->name('export_email_classes');
Route::get('/restoreclasses', [ClassesController::class, 'restore'])->name('restore_classes');
