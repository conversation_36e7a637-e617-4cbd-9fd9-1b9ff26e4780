<?php

use App\DoctorFrequency;
use App\Http\Controllers\ClassFrequencyController;
use App\Http\Controllers\DoctorFrequencyController;
use App\Http\Controllers\SpecialityClassFrequencyController;
use App\Http\Controllers\SpecialityFrequencyController;
use Illuminate\Support\Facades\Route;

//resource frequencies
Route::get('/classfrequencies', [ClassFrequencyController::class, 'index'])->name('show_all_class_frequencies');
Route::post('/classfrequencies', [ClassFrequencyController::class, 'store'])->name('create_class_frequencies');
Route::get('/classfrequencies/create', [ClassFrequencyController::class, 'create'])->name('create_class_frequencies');
Route::get('/classfrequencies/{id}/edit', [ClassFrequencyController::class, 'edit'])->name('edit_class_frequencies');
Route::get('/classfrequencies/{id}', [ClassFrequencyController::class, 'show'])->name('show_single_class_frequencies');
Route::put('/classfrequencies/{id}', [ClassFrequencyController::class, 'update'])->name('edit_class_frequencies');
Route::delete('/classfrequencies/{id}', [ClassFrequencyController::class, 'destroy'])->name('delete_class_frequencies');

Route::post('/import-class-frequency', [ClassFrequencyController::class, 'import'])->name('import_class_frequencies');
Route::post('/import-update-class-frequency', [ClassFrequencyController::class, 'updateByImport'])->name('import_bulk_edit');
Route::get('/export-class-frequency', [ClassFrequencyController::class, 'exportxlsx'])->name('export_xlsx_class_frequencies');
Route::get('/export-class-frequency-pdf', [ClassFrequencyController::class, 'exportpdf'])->name('export_pdf_class_frequencies');
Route::get('/export-class-frequency-csv', [ClassFrequencyController::class, 'exportcsv'])->name('export_csv_class_frequencies');
Route::post('/sendmail-class-frequency', [ClassFrequencyController::class, 'sendmail'])->name('export_email_class_frequencies');



Route::get('/getLineClasses/{id}', [ClassFrequencyController::class, 'getLineClasses'])->name('show_all_line_classes');


//resource speciality frequencies
Route::get('/specialityfrequencies', [SpecialityFrequencyController::class, 'index'])->name('show_all_speciality_frequencies');
Route::post('/specialityfrequencies', [SpecialityFrequencyController::class, 'store'])->name('create_speciality_frequencies');
Route::get('/specialityfrequencies/create', [SpecialityFrequencyController::class, 'create'])->name('create_speciality_frequencies');
Route::get('/specialityfrequencies/{id}/edit', [SpecialityFrequencyController::class, 'edit'])->name('edit_speciality_frequencies');
Route::get('/specialityfrequencies/{id}', [SpecialityFrequencyController::class, 'show'])->name('show_single_speciality_frequencies');
Route::put('/specialityfrequencies/{id}', [SpecialityFrequencyController::class, 'update'])->name('edit_speciality_frequencies');
Route::delete('/specialityfrequencies/{id}', [SpecialityFrequencyController::class, 'destroy'])->name('delete_speciality_frequencies');

Route::get('/getLineSpecialities/{line}', [SpecialityFrequencyController::class, 'getLineSpecialities'])->name('show_all_line_specialities');



// Speciality Class Frequency

//resource speciality frequencies
Route::get('/speciality-class-frequencies', [SpecialityClassFrequencyController::class, 'index'])->name('show_all_speciality_class_frequencies');
Route::post('/speciality-class-frequencies', [SpecialityClassFrequencyController::class, 'store'])->name('create_speciality_class_frequencies');
Route::get('/speciality-class-frequencies/{id}', [SpecialityClassFrequencyController::class, 'show'])->name('show_single_speciality_class_frequencies');
Route::put('/speciality-class-frequencies/{id}', [SpecialityClassFrequencyController::class, 'update'])->name('edit_speciality_frequencies');
Route::delete('/speciality-class-frequencies/{id}', [SpecialityClassFrequencyController::class, 'destroy'])->name('delete_speciality_class_frequencies');

Route::post('/import-speciality-class-frequency', [SpecialityClassFrequencyController::class, 'import'])->name('import_speciality_class_frequencies');
Route::post('/import-update-speciality-class-frequency', [SpecialityClassFrequencyController::class, 'updateByImport'])->name('import_bulk_edit');
Route::get('/export-speciality-class-frequency', [SpecialityClassFrequencyController::class, 'exportxlsx'])->name('export_xlsx_speciality_class_frequencies');
Route::get('/export-speciality-class-frequency-pdf', [SpecialityClassFrequencyController::class, 'exportpdf'])->name('export_pdf_speciality_class_frequencies');
Route::get('/export-speciality-class-frequency-csv', [SpecialityClassFrequencyController::class, 'exportcsv'])->name('export_csv_speciality_class_frequencies');
Route::post('/sendmail-speciality-class-frequency', [SpecialityClassFrequencyController::class, 'sendmail'])->name('export_email_speciality_class_frequencies');

//resource doctor frequencies
Route::post('/doctorfrequencies/index', [DoctorFrequencyController::class, 'index'])->name('show_all_doctor_frequencies');
Route::post('/doctorfrequencies', [DoctorFrequencyController::class, 'store'])->name('create_doctor_frequencies');
Route::post('/list-data', [DoctorFrequencyController::class, 'getList'])->name('create_doctor_frequencies');
Route::get('/date-control', [DoctorFrequencyController::class, 'date'])->name('');
Route::get('/doctorfrequencies/create', [DoctorFrequencyController::class, 'create'])->name('create_doctor_frequencies');
Route::get('/doctorfrequencies/{id}/edit', [DoctorFrequencyController::class, 'edit'])->name('edit_doctor_frequencies');
Route::get('/doctorfrequencies/{id}', [DoctorFrequencyController::class, 'show'])->name('show_single_doctor_frequencies');
Route::put('/doctorfrequencies/{id}', [DoctorFrequencyController::class, 'update'])->name('edit_doctor_frequencies');
Route::delete('/doctorfrequencies/{id}', [DoctorFrequencyController::class, 'destroy'])->name('delete_doctor_frequencies');

Route::post('/import-doctor-frequency', [DoctorFrequencyController::class, 'import'])->name('import_doctor_frequencies');
Route::post('/import-update-doctor-frequency', [DoctorFrequencyController::class, 'updateByImport'])->name('import_bulk_edit');
Route::get('/export-doctor-frequency', [DoctorFrequencyController::class, 'exportxlsx'])->name('export_xlsx_doctor_frequencies');
Route::get('/export-doctor-frequency-pdf', [DoctorFrequencyController::class, 'exportpdf'])->name('export_pdf_doctor_frequencies');
Route::get('/export-doctor-frequency-csv', [DoctorFrequencyController::class, 'exportcsv'])->name('export_csv_doctor_frequencies');
Route::post('/sendmail-doctor-frequency', [DoctorFrequencyController::class, 'sendmail'])->name('export_email_doctor_frequencies');

Route::get('/lines/{line}/accounts', [DoctorFrequencyController::class, 'getLineAccounts'])->name('show_all_account_lines');
Route::get('/accounts/{account}/doctors', [DoctorFrequencyController::class, 'getAccountDoctor'])->name('show_all_account_doctors');


Route::get('/frequency-replicate', [DoctorFrequencyController::class, 'replicate'])->name('');
