<?php

//resource

use App\Http\Controllers\Coaching\TypeController;
use Illuminate\Support\Facades\Route;

Route::get('/coaching/types',[TypeController::class,'index'])->name('show_all_types');
Route::post('/coaching/types',[TypeController::class,'store'])->name('create_types');
Route::get('/coaching/types/{type}',[TypeController::class,'show'])->name('show_single_types');
Route::put('/coaching/types/{type}',[TypeController::class,'update'])->name('edit_types');
Route::delete('/coaching/types/{type}',[TypeController::class,'destroy'])->name('delete_types');

// Tools 
Route::post('/coaching/importtypes', [TypeController::class,'import'])->name('import_types');
Route::post('/coaching/importupdatetypes', [TypeController::class, 'updateByImport'])->name('import_bulk_edit');
Route::get('/coaching/downloadtype/{filename}', [TypeController::class,'export'])->name('download_template_types');
Route::get('/coaching/exporttypes', [TypeController::class,'exporttypes'])->name('export_xlsx_types');
Route::get('/coaching/exporttypescsv', [TypeController::class,'exportcsv'])->name('export_csv_types');
Route::get('/coaching/exporttypepdf', [TypeController::class,'exportpdf'])->name('export_pdf_types');

Route::get('/coaching/restoretype', [TypeController::class,'restore'])->name('restore_types');

Route::post('/coaching/sendmailtypes', [TypeController::class,'sendmail'])->name('export_email_types');
