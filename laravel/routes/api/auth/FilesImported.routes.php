<?php

use App\Http\Controllers\ImportController;
use Illuminate\Support\Facades\Route;


//resource
Route::post('/imports/index',[ImportController::class,'index'])->name('show_all_imports');
Route::get('/imports/{import}/errors',[ImportController::class,'getErrors'])->name('');
Route::delete('/imports/{id}',[ImportController::class,'destroy'])->name('delete_imports');
Route::delete('/force-delete-file/{id}',[ImportController::class,'deleteForever'])->name('delete_imports');
Route::get('/imports/{id}/reload',[ImportController::class,'reload'])->name('reload-imports'); // TODO: permission not implemented yet

Route::get('/restoreIndex', [ImportController::class, 'restoreIndex'])->name('restore_imports');
Route::get('/importedDownload/{id}', [ImportController::class, 'download'])->name('download_imports');
Route::post('/importedRestore/{id}', [ImportController::class, 'restore'])->name('restore_imports');
