<?php

use App\Http\Controllers\DivisionTypeController;
use Illuminate\Support\Facades\Route;

//resource
Route::get('/divisiontypes',[DivisionTypeController::class,'index'])->name('show_all_divisiontypes');
Route::post('/divisiontypes',[DivisionTypeController::class,'store'])->name('create_divisiontypes');
Route::get('/divisiontypes/create',[DivisionTypeController::class,'create'])->name('create_divisiontypes');
Route::get('/divisiontypes/{id}/edit',[DivisionTypeController::class,'edit'])->name('edit_divisiontypes');
Route::get('/divisiontypes/{id}',[DivisionTypeController::class,'show'])->name('show_single_divisiontypes');
Route::put('/divisiontypes/{id}',[DivisionTypeController::class,'update'])->name('edit_divisiontypes');
Route::delete('/divisiontypes/{id}',[DivisionTypeController::class,'destroy'])->name('delete_divisiontypes');

Route::post('/importdivisiontypes', [DivisionTypeController::class, 'import'])->name('import_divisiontypes');
Route::post('/importupdatedivisiontypes', [DivisionTypeController::class, 'updateByImport'])->name('import_bulk_edit');
Route::get('/exportdivisiontypes', [DivisionTypeController::class, 'exportdivisiontypes'])->name('export_xlsx_divisiontypes');
Route::get('/exportdivisiontypepdf', [DivisionTypeController::class, 'exportpdf'])->name('export_pdf_divisiontypes');
Route::post('/sendmaildivisiontypes', [DivisionTypeController::class, 'sendmail'])->name('export_email_divisiontypes');
Route::get('/exportdivisiontypescsv', [DivisionTypeController::class, 'exportcsv'])->name('export_csv_divisiontypes');
