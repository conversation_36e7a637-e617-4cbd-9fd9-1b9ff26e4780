<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Help\MainTopic\MainTopicSubTopicArticleController;
use App\Http\Controllers\Help\ImageController;
use App\Http\Controllers\Help\Article\ArticleKeywordController;
use App\Http\Controllers\Help\Keyword\KeywordArticleController;
use App\Http\Controllers\Help\MainTopic\MainTopicKeywordController;
use App\Http\Controllers\Help\SubTopic\SubTopicKeywordController;
use App\Http\Controllers\Help\Keyword\KeywordMainTopicController;
use App\Http\Controllers\Help\SubTopic\SubTopicArticleController;
use App\Http\Controllers\Help\MainTopic\MainTopicArticleController;
use App\Http\Controllers\Help\Article\ArticleMainTopicController;
use App\Http\Controllers\Help\Article\ArticleSubTopicController;
use App\Http\Controllers\Help\Keyword\KeywordSubTopicController;
use App\Http\Controllers\Help\MainTopic\MainTopicSubTopicController;
use App\Http\Controllers\Help\SubTopic\SubTopicMainTopicController;
use App\Http\Controllers\Help\Keyword\KeywordController;
use App\Http\Controllers\Help\MainTopic\MainTopicController;
use App\Http\Controllers\Help\SubTopic\SubTopicController;
use App\Http\Controllers\Help\Article\ArticleController;
use App\Http\Controllers\Help\TopicController;


Route::prefix('help')->name('help.')->group(function () {
    //Article
    Route::get('/articles', [ArticleController::class, 'index'])->name('show_all_articles');
    Route::get('/articles/{article}', [ArticleController::class, 'show'])->name('show_single_articles');
    Route::get('/articles/{article}/subtopics', [ArticleSubTopicController::class, 'index'])->name(''); //Todo permission not implemented
    Route::get('/articles/{article}/maintopics', [ArticleMainTopicController::class, 'index'])->name(''); //Todo permission not implemented
    Route::get('/articles/{article}/keywords', [ArticleKeywordController::class, 'index'])->name(''); //Todo permission not implemented
    Route::post('/articles/{article}/keywords', [ArticleKeywordController::class, 'store'])->name(''); //Todo permission not implemented

    //MainTopic
    Route::get('/maintopics', [MainTopicController::class, 'index'])->name('show_all_main_topics');
    Route::get('/maintopics/{mainTopic}', [MainTopicController::class, 'show'])->name('show_single_main_topics');
    Route::get('/maintopics/{mainTopic}/subtopics', [MainTopicSubTopicController::class, 'index'])->name(''); //Todo permission not implemented
    Route::get('/maintopics/{mainTopic}/articles', [MainTopicArticleController::class, 'index'])->name(''); //Todo permission not implemented
    Route::get('/maintopics/{mainTopic}/keywords', [MainTopicKeywordController::class, 'index'])->name(''); //Todo permission not implemented
    Route::get('alltopics', [MainTopicSubTopicArticleController::class, 'index'])->name('');

    //SubTopic
    Route::get('/subtopics', [SubTopicController::class, 'index'])->name('show_all_sub_topics');
    Route::get('/subtopics/{subTopic}', [SubTopicController::class, 'show'])->name('show_single_sub_topics');
    Route::get('/subtopics/{subTopic}/maintopics', [SubTopicMainTopicController::class, 'index'])->name(''); //Todo permission not implemented
    Route::get('/subtopics/{subTopic}/articles', [SubTopicArticleController::class, 'index'])->name(''); //Todo permission not implemented
    Route::post('/subtopics/{subTopic}/articles', [SubTopicArticleController::class, 'store'])->name(''); //Todo permission not implemented
    Route::get('/subtopics/{subTopic}/keywords', [SubTopicKeywordController::class, 'index'])->name(''); //Todo permission not implemented

    //keyword
    Route::get('/keywords', [KeywordController::class, 'index'])->name('show_all_keywords');
    Route::get('/keywords/{keyword}', [KeywordController::class, 'show'])->name('show_single_keywords');
    Route::get('/keywords/{keyword}/articles', [KeywordArticleController::class, 'index'])->name(''); //Todo permission not implemented
    Route::get('/keywords/{keyword}/subtopics', [KeywordSubTopicController::class, 'index'])->name(''); //Todo permission not implemented
    Route::get('/keywords/{keyword}/maintopics', [KeywordMainTopicController::class, 'index'])->name(''); //Todo permission not implemented
    

    //Topic
    Route::get('/topics', [TopicController::class, 'index'])->name('show_all_topics');
    Route::post('/topics', [TopicController::class, 'store'])->name('create_topics');
    Route::get('/topics/{topic}', [TopicController::class, 'show'])->name('show_single_topics');
    Route::put('/topics/{topic}', [TopicController::class, 'update'])->name('edit_topics');
    Route::delete('/topics/{topic}', [TopicController::class, 'destroy'])->name('delete_topics');
});
