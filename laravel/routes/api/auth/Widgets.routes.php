<?php

use App\Http\Controllers\DashboardSettingController;
use App\Http\Controllers\StaticWidgetController;
use App\Http\Controllers\WidgetController;
use App\Http\Controllers\WidgetSettingController;
use Illuminate\Support\Facades\Route;

Route::get('/widgets', [WidgetController::class,'index'])->name('');
Route::get('/widgets/only', [WidgetController::class,'widgetsOnly'])->name('');
Route::get('/widgets/{id}', [WidgetController::class,'show'])->name('');
Route::post('/widgets/{id}', [WidgetController::class,'show'])->name('');
Route::get('/widget-settings/show', [WidgetSettingController::class,'show'])->name('');
Route::post('/widget-settings/store', [WidgetSettingController::class,'store'])->name('');
Route::get('/static/widgets', [StaticWidgetController::class,'index'])->name('');


Route::get('/dashboard-widget-settings', [DashboardSettingController::class,'index'])->name('');
Route::get('/dashboard-widget-settings/{id}', [DashboardSettingController::class,'show'])->name('');
Route::put('/dashboard-widget-settings/{id}', [DashboardSettingController::class,'update'])->name('');
