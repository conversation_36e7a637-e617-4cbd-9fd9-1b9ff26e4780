<?php

use App\Http\Controllers\LineBricksController;
use App\Http\Controllers\LineClassesController;
use App\Http\Controllers\LineController;
use App\Http\Controllers\LineDivisionParentController;
use App\Http\Controllers\LineDivisionsController;
use App\Http\Controllers\LineProductsController;
use App\Http\Controllers\LineSpecialitiesController;
use App\Http\Controllers\LineUserDivisionsController;
use Illuminate\Support\Facades\Route;

//resource
Route::get('/lines', [LineController::class, 'index'])->name('show_all_lines');
Route::post('/lines', [LineController::class, 'store'])->name('create_lines');
Route::get('/lines/create', [LineController::class, 'create'])->name('create_lines');
Route::get('/lines/{id}/edit', [LineController::class, 'edit'])->name('edit_lines');
Route::get('/lines/show/{id}', [LineController::class, 'show'])->name('show_single_lines');
Route::put('/lines/{id}', [LineController::class, 'update'])->name('edit_lines');
Route::delete('/lines/{id}', [LineController::class, 'destroy'])->name('delete_lines');
Route::get('/lines/{id}', [LineController::class, 'delete'])->name('delete_lines');
Route::post('/replicate-line', [LineController::class, 'replicate'])->name('');
Route::get('/general-setting', [LineController::class, 'setting'])->name('');
// lines
Route::post('/importlines', [LineController::class, 'import'])->name('import_lines');
Route::post('/importupdatelines', [LineController::class, 'updateByImport'])->name('import_bulk_edit');

Route::post('/import-line-structure', [LineController::class, 'importLineStructure'])->name('import_lines');
Route::post('/importuserdivisions', [LineUserDivisionsController::class, 'importUserDivision'])->name('import_lines');
Route::post('/importupdateuserDivisions', [LineUserDivisionsController::class, 'updateByImport'])->name('import_bulk_edit');
// LineProducts
Route::post('/importlineproducts', [LineProductsController::class, 'importLineProducts'])->name('import_line_products');
Route::post('/importupdatelineproducts', [LineProductsController::class, 'updateByImport'])->name('import_bulk_edit');
// LineBricks
Route::post('/importlinebricks', [LineBricksController::class, 'importLineBricks'])->name('import_line_bricks');
Route::post('/importupdatelinebricks', [LineBricksController::class, 'updateByImport'])->name('import_bulk_edit');
// LineSpecialities
Route::post('/importlinespecialities', [LineSpecialitiesController::class, 'importLineSpecialities'])->name('import_lines');
Route::post('/importupdatelinespecialities', [LineSpecialitiesController::class, 'updateByImport'])->name('import_bulk_edit');
// LineClasses
Route::post('/importlineclasses', [LineClassesController::class, 'importLineClasses'])->name('import_line_classes');
Route::post('/importupdatelineclasses', [LineClassesController::class, 'updateByImport'])->name('import_bulk_edit');
// LineDivisions
Route::post('/importdivisions', [LineDivisionsController::class, 'importDivision'])->name('import_line_divisions'); //TODO:edit request route
Route::post('/importupdatelinedivisions', [LineDivisionsController::class, 'updateByImport'])->name('import_bulk_edit');
// lineDivisionParents
Route::post('/importdivisionparents', [LineDivisionParentController::class, 'importDivisionParent'])->name('import_line_div_parents'); //TODO:edit request route
Route::post('/importupdatelinedivisionparents', [LineDivisionParentController::class, 'updateByImport'])->name('import_bulk_edit');

Route::get('/exportlines', [LineController::class, 'exportlines'])->name('export_xlsx_lines');
Route::get('/exportlinepdf', [LineController::class, 'exportpdf'])->name('export_pdf_lines');
Route::post('/sendmaillines', [LineController::class, 'sendmail'])->name('export_email_lines');
Route::get('/exportlinescsv', [LineController::class, 'exportcsv'])->name('export_csv_lines');
