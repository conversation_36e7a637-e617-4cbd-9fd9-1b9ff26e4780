<?php

use App\Http\Controllers\LineController;
use App\Http\Controllers\LineDivisionParentController;
use App\Http\Controllers\LineDivisionProductController;
use App\Http\Controllers\LineDivisionsController;
use App\Http\Controllers\LineDivisionTypesController;
use App\Http\Controllers\LineProductsController;
use App\Http\Controllers\LineUserDivisionsController;
use App\Http\Controllers\LineUsersController;
use Illuminate\Support\Facades\Route;

// test Line Division Types

Route::get('/lines/division-types', [LineDivisionTypesController::class, 'index'])->name('show_all_line_division_types');
Route::get('/lines/{line}/division-types', [LineDivisionTypesController::class, 'getLineDivisionTypes'])->name('show_all_line_division_types');
Route::post('/lines/{line}/division-types', [LineDivisionTypesController::class, 'store'])->name('create_line_division_types');
Route::get('lines/division-types/{id}', [LineDivisionTypesController::class, 'show'])->name('show_single_line_division_types');
Route::put('/lines/{line}/division-types/{id}', [LineDivisionTypesController::class, 'update'])->name('edit_line_division_types');
Route::delete('/line-division-types/{id}', [LineDivisionTypesController::class, 'destroy'])->name('delete_line_division_types');

// test line divisions

Route::get('/line-divisions/{line}', [LineDivisionsController::class, 'index'])->name('show_all_line_divisions');
Route::get('/lines/{line}/divisions', [LineDivisionsController::class, 'getLineDivisions'])->name('show_all_line_divisions');
Route::post('/lines/{line}/divisions', [LineDivisionsController::class, 'store'])->name('create_line_divisions');
Route::get('/lines/divisions/{id}', [LineDivisionsController::class, 'show'])->name('show_single_line_divisions');
Route::put('/lines/{line}/divisions/{id}', [LineDivisionsController::class, 'update'])->name('edit_line_divisions');
Route::delete('/lines-divisions/{id}', [LineDivisionsController::class, 'destroy'])->name('delete_line_divisions');
Route::get('/division-location/{lineDivision}', [LineDivisionsController::class, 'getLocation'])->name('');

// test division parents
Route::get('/lines/{line}/divisions-parent', [LineDivisionParentController::class, 'index'])->name('show_all_line_div_parents');
Route::get('/line/divisions/{lineDivision}/parent', [LineDivisionParentController::class, 'changeParents']);
Route::get('/divisions-parent/{id}', [LineDivisionParentController::class, 'getParents'])->name('show_all_line_div_parents');
Route::post('/line/{line}/divisions/parent', [LineDivisionParentController::class, 'store'])->name('create_line_div_parents');
Route::get('/division-parent/{id}', [LineDivisionParentController::class, 'show'])->name('show_single_line_div_parents');
Route::put('/line/{line}/divisions/parent/{id}', [LineDivisionParentController::class, 'update'])->name('edit_line_div_parents');
Route::delete('/division-parent/{id}', [LineDivisionParentController::class, 'destroy'])->name('delete_line_div_parents');

// test line products
Route::get('/line-products', [LineProductsController::class, 'index'])->name('show_all_line_products');
Route::get('/lines/{line}/products', [LineProductsController::class, 'getLineProducts'])->name('show_all_line_products');
Route::post('/lines/{line}/products', [LineProductsController::class, 'store'])->name('create_line_products');
Route::get('/line-products/{id}', [LineProductsController::class, 'show'])->name('show_single_line_products');
Route::put('/lines/{line}/products/{id}', [LineProductsController::class, 'update'])->name('edit_line_products');
Route::delete('/line-products/{id}', [LineProductsController::class, 'destroy'])->name('delete_line_products');


// test line Division products
Route::get('/line-division-products', [LineDivisionProductController::class, 'index'])->name('show_all_line_products');
Route::get('/lines/{line}/division/products', [LineDivisionProductController::class, 'getLineProducts'])->name('show_all_line_products');
Route::post('/lines/{line}/division/products', [LineDivisionProductController::class, 'store'])->name('create_line_products');
Route::get('/line-division-products/{id}', [LineDivisionProductController::class, 'show'])->name('show_single_line_products');
Route::put('/lines/{line}/division/products/{id}', [LineDivisionProductController::class, 'update'])->name('edit_line_products');
Route::delete('/line-division-products/{id}', [LineDivisionProductController::class, 'destroy'])->name('delete_line_products');

Route::post('/import-line-division-products', [LineDivisionProductController::class, 'importLineProducts'])->name('import_line_products');
Route::post('/import-update-line-division-products', [LineDivisionProductController::class, 'updateByImport'])->name('import_bulk_edit');

//test Line Users
Route::get('/line-users', [LineUsersController::class, 'index'])->name('show_single_line_users');
Route::get('/get_line_users/{line}', [LineUsersController::class, 'getLineUsers'])->name('show_single_line_users');
Route::post('/lines/{line}/users', [LineUsersController::class, 'store'])->name('create_line_users');
Route::get('/line-users/{id}', [LineUsersController::class, 'show'])->name('show_single_line_users');
Route::put('/lines/{line}/user/{id}', [LineUsersController::class, 'update'])->name('edit_line_users');
Route::delete('/line-users/{id}', [LineUsersController::class, 'destroy'])->name('delete_line_users');

// test line user divisions

Route::get('/lines/{line}/users', [LineUserDivisionsController::class, 'index'])->name('show_all_line_user_divisions');
Route::get('/get_line_division_users/{line}', [LineUserDivisionsController::class, 'getLineDivisionUsers'])->name('show_all_line_user_divisions');
Route::post('/lines/{line}/user/divisions', [LineUserDivisionsController::class, 'store'])->name('create_line_user_divisions');
Route::get('/line-user-divisions/{id}', [LineUserDivisionsController::class, 'show'])->name('show_single_line_user_divisions');
Route::put('/lines/{line}/user/division/{id}', [LineUserDivisionsController::class, 'update'])->name('edit_line_user_divisions');
Route::delete('/line-user-divisions/{id}', [LineUserDivisionsController::class, 'destroy'])->name('delete_line_user_divisions');
