
<?php


// guest

use App\Http\Controllers\AppApiController;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\CompanyController;
use Illuminate\Support\Facades\Route;

Route::post('login', [AuthController::class,'login'])->name('login');
Route::get('login2', [AuthController::class, 'login2'])->name('login');
Route::get('/logo-company/{company}', [CompanyController::class, 'logCompany'])->name('');
Route::post('register', [AuthController::class,'register']);
