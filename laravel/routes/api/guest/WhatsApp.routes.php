<?php

use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Route;

Route::post('/webhooks/status', function () {
    $data = request()->all();
    Log::Info($data);
    return response()->json(["result" => "success"]);
});

Route::post('/webhooks/inbound', function () {
    $data = request()->all();
    Log::info($data);
    $text = $data['text'];
    $number = intval($text);
    Log::Info($number);
    if ($number > 0) {
            $random = rand(1, 8);
            Log::Info($random);
            $respond_number = $number * $random;
            Log::Info($respond_number);
            $url = "https://messages-sandbox.nexmo.com/v1/messages";
            $params = [
                    "from" =>  "14157386102",
                    "to" => $data['from'],
                    "message_type" => "text",
                    "channel" => "whatsapp",
                    "text" => "The answer is " . $respond_number . ", we multiplied by " . $random . ".",
            ];
            $headers = ["Authorization" => "Basic " . base64_encode(env('NEXMO_API_KEY') . ":" . env('NEXMO_API_SECRET'))];

            $client = new \GuzzleHttp\Client();
            $response = $client->request('POST', $url, ["headers" => $headers, "json" => $params]);
            $data = $response->getBody();
    }
    Log::Info($data);
    return response()->json(["result" => "success"]);
});
